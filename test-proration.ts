/**
 * Test file for proration utilities
 * This can be run manually to verify the calculations work correctly
 */

import { calculateProratedAmount, getRemainingDaysInMonth, getTotalDaysInMonth } from './lib/proration-utils';

// Test scenarios
console.log('=== Proration Utility Tests ===\n');

// Test 1: January 15th scenario (mid-month)
const jan15 = new Date(2025, 0, 15); // January 15, 2025
const monthlyAmount = 1000; // 1000 TL monthly fee

console.log('Test 1: January 15th, 2025');
console.log(`Monthly Amount: ${monthlyAmount} TL`);
console.log(`Total days in January: ${getTotalDaysInMonth(jan15)}`);
console.log(`Remaining days from Jan 15: ${getRemainingDaysInMonth(jan15)}`);
console.log(`Prorated amount: ${calculateProratedAmount(monthlyAmount, jan15)} TL`);
console.log('Expected: ~548.39 TL (17 days out of 31 days)\n');

// Test 2: February 1st scenario (full month)
const feb1 = new Date(2025, 1, 1); // February 1, 2025
console.log('Test 2: February 1st, 2025');
console.log(`Monthly Amount: ${monthlyAmount} TL`);
console.log(`Total days in February: ${getTotalDaysInMonth(feb1)}`);
console.log(`Remaining days from Feb 1: ${getRemainingDaysInMonth(feb1)}`);
console.log(`Prorated amount: ${calculateProratedAmount(monthlyAmount, feb1)} TL`);
console.log('Expected: 1000 TL (full month)\n');

// Test 3: End of month scenario
const jan31 = new Date(2025, 0, 31); // January 31, 2025
console.log('Test 3: January 31st, 2025');
console.log(`Monthly Amount: ${monthlyAmount} TL`);
console.log(`Total days in January: ${getTotalDaysInMonth(jan31)}`);
console.log(`Remaining days from Jan 31: ${getRemainingDaysInMonth(jan31)}`);
console.log(`Prorated amount: ${calculateProratedAmount(monthlyAmount, jan31)} TL`);
console.log('Expected: ~32.26 TL (1 day out of 31 days)\n');

// Test 4: Different monthly amount
const differentAmount = 750;
console.log('Test 4: Different amount on January 10th, 2025');
const jan10 = new Date(2025, 0, 10);
console.log(`Monthly Amount: ${differentAmount} TL`);
console.log(`Total days in January: ${getTotalDaysInMonth(jan10)}`);
console.log(`Remaining days from Jan 10: ${getRemainingDaysInMonth(jan10)}`);
console.log(`Prorated amount: ${calculateProratedAmount(differentAmount, jan10)} TL`);
console.log('Expected: ~532.26 TL (22 days out of 31 days)');
