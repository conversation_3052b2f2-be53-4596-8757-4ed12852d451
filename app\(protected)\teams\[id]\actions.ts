"use server";

import { redirect } from "next/navigation";
import { athleteTeamService } from "@/lib/services";
import { getServerTenantId, getServerUserId } from "@/lib/tenant-utils-server";

interface AddAthleteToTeamData {
  athleteId: string;
  teamId: string;
  paymentPlanId?: string;
  assignPaymentPlan?: boolean;
  useProrated?: boolean;
  customProratedAmount?: string;
  locale?: string;
}

/**
 * Server action wrapper for adding athlete to team with prorated balance
 * This follows the architecture pattern: Client → Server Action → Service Layer
 */
export async function addAthleteToTeamAction(
  data: AddAthleteToTeamData
) {
  try {
    // Basic validation
    if (!data.athleteId?.trim() || !data.teamId?.trim()) {
      return {
        success: false,
        error: 'Athlete ID and Team ID are required'
      };
    }

    if (data.assignPaymentPlan && !data.paymentPlanId?.trim()) {
      return {
        success: false,
        error: 'Payment plan ID is required when assigning payment plan'
      };
    }

    // Get tenant and user context
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    // Call service layer directly (following best practices)
    const result = await athleteTeamService().addAthleteToTeamWithPaymentPlan(
      {
        athleteId: data.athleteId,
        teamId: data.teamId,
        paymentPlanId: data.paymentPlanId,
        assignPaymentPlan: data.assignPaymentPlan || false,
        useProrated: data.useProrated || false,
        customProratedAmount: data.customProratedAmount,
        locale: data.locale || 'en'
      },
      userId?.toString(),
      tenantId || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.userMessage || 'Failed to add athlete to team'
      };
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error in addAthleteToTeamAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add athlete to team'
    };
  }
}

/**
 * Server action wrapper for adding athlete to team from athlete profile with prorated balance
 */
export async function addAthleteToTeamFromProfileAction(
  formData: FormData
) {
  try {
    // Extract form data
    const athleteData: AddAthleteToTeamData = {
      athleteId: formData.get('athleteId') as string,
      teamId: formData.get('teamId') as string,
      paymentPlanId: formData.get('paymentPlanId') as string || undefined,
      assignPaymentPlan: formData.get('assignPaymentPlan') === 'true',
      useProrated: formData.get('useProrated') === 'true',
      locale: formData.get('locale') as string || 'en'
    };

    const result = await addAthleteToTeamAction(athleteData);
    
    if (result.success) {
      // Redirect on success for form-based submissions
      redirect(`/athletes/${athleteData.athleteId}`);
    }

    return result;
  } catch (error) {
    console.error('Error in addAthleteToTeamFromProfileAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to add athlete to team'
    };
  }
}
