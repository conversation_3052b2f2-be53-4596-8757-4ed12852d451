"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Plus, Users, CreditCard, UserMinus } from "lucide-react";
import { toast } from "sonner";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { format } from "date-fns";
import { calculateProratedAmount, getRemainingDaysInMonth, getTotalDaysInMonth } from "@/lib/proration-utils";
import { addAthleteToTeamAction } from "@/app/(protected)/teams/[id]/actions";
import { getAvailableTeamsAction, getTeamPaymentPlansForAthleteAction, removeAthleteFromTeamAction } from "@/app/(protected)/athletes/[id]/team-actions";
import { RemoveAthleteFromTeamDialog } from "@/components/teams/remove-athlete-from-team-dialog";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Team {
  id: string;
  name: string;
  branchId: string;
  branchName?: string;
  schoolId: string;
  schoolName?: string;
}

interface AthleteTeamDetail {
  teamId: string;
  teamName: string;
  branchName: string;
  branchId: string;
  schoolName: string;
  schoolId: string;
  instructorName: string;
  instructorSurname: string;
  joinedAt: string;
  leftAt?: string | null;
}

interface AthleteTeamManagementProps {
  athleteId: string;
  athleteName: string;
  athleteSurname: string;
  athleteTeams: AthleteTeamDetail[];
  onTeamChange?: () => void;
}

export function AthleteTeamManagement({
  athleteId,
  athleteName,
  athleteSurname,
  athleteTeams,
  onTeamChange
}: AthleteTeamManagementProps) {
  const { t } = useSafeTranslation();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState("");
  const [shouldAssignPaymentPlan, setShouldAssignPaymentPlan] = useState(false);
  const [useProrated, setUseProrated] = useState(false);
  const [proratedAmount, setProratedAmount] = useState("");
  const [calculatedProrated, setCalculatedProrated] = useState<number | null>(null);
  const [selectedPaymentPlanId, setSelectedPaymentPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [availableTeams, setAvailableTeams] = useState<Team[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [loadingData, setLoadingData] = useState(false);

  const loadAvailableTeams = useCallback(async () => {
    setLoadingData(true);
    try {
      const result = await getAvailableTeamsAction();
      
      if (!result.success) {
        console.error("Error loading teams:", result.error);
        toast.error(t('common.error'));
        return;
      }
      
      const allTeams = result.data;
      const currentTeamIds = athleteTeams.map(at => at.teamId);
      const filteredTeams = allTeams ? allTeams.filter((team: any) => !currentTeamIds.includes(team.id)) : [];
      setAvailableTeams(filteredTeams);
    } catch (error) {
      console.error("Error loading teams:", error);
      toast.error(t('common.error'));
    } finally {
      setLoadingData(false);
    }
  }, [athleteTeams, t]);

  const loadPaymentPlansForTeam = useCallback(async (teamId: string) => {
    try {
      const selectedTeam = availableTeams.find((t: any) => t.id === teamId);
      if (selectedTeam) {
        const result = await getTeamPaymentPlansForAthleteAction(selectedTeam.branchId);
        
        if (result.success && result.data) {
          setAvailablePaymentPlans(result.data);
        } else {
          console.error("Error loading payment plans:", result.error);
          setAvailablePaymentPlans([]);
        }
      }
    } catch (error) {
      console.error("Error loading payment plans:", error);
      setAvailablePaymentPlans([]);
    }
  }, [availableTeams]);

  // Load available teams when dialog opens
  useEffect(() => {
    if (isAddDialogOpen) {
      loadAvailableTeams();
    }
  }, [isAddDialogOpen, loadAvailableTeams]); // Add loadAvailableTeams to dependencies

  // Load payment plans when team is selected
  useEffect(() => {
    if (selectedTeamId && shouldAssignPaymentPlan) {
      loadPaymentPlansForTeam(selectedTeamId);
    }
  }, [selectedTeamId, shouldAssignPaymentPlan, loadPaymentPlansForTeam]);

  // Calculate prorated amount when useProrated changes or payment plan is selected
  useEffect(() => {
    if (useProrated && shouldAssignPaymentPlan && selectedPaymentPlanId) {
      const selectedPlan = availablePaymentPlans.find(plan => plan.id === selectedPaymentPlanId);
      if (selectedPlan) {
        const monthlyAmount = parseFloat(selectedPlan.monthlyValue);
        const prorated = calculateProratedAmount(monthlyAmount);
        setCalculatedProrated(prorated);
        setProratedAmount(prorated.toFixed(2));
      }
    } else if (useProrated) {
      setCalculatedProrated(null);
      setProratedAmount("");
    }
  }, [useProrated, shouldAssignPaymentPlan, selectedPaymentPlanId, availablePaymentPlans]);

  const handleAddToTeam = async () => {
    if (!selectedTeamId) {
      toast.error(t('teams.management.selectAthlete'));
      return;
    }

    if (shouldAssignPaymentPlan && !selectedPaymentPlanId) {
      toast.error(t('teams.management.selectPaymentPlan'));
      return;
    }

    setIsLoading(true);
    try {
      const result = await addAthleteToTeamAction({
        athleteId,
        teamId: selectedTeamId,
        paymentPlanId: shouldAssignPaymentPlan ? selectedPaymentPlanId : undefined,
        assignPaymentPlan: shouldAssignPaymentPlan,
        useProrated: shouldAssignPaymentPlan ? useProrated : false,
        customProratedAmount: useProrated && proratedAmount ? proratedAmount : undefined,
        locale: 'tr' // You might want to get this from a context
      });

      if (result.success) {
        const selectedTeam = availableTeams.find(t => t.id === selectedTeamId);
        const selectedPlan = availablePaymentPlans.find(p => p.id === selectedPaymentPlanId);
        
        let successMessage = t('athletes.management.addedToTeamSuccess', {
          teamName: selectedTeam?.name
        });

        if (shouldAssignPaymentPlan && selectedPlan) {
          successMessage += ` ${t('teams.management.withPaymentPlan', { 
            planName: selectedPlan.name 
          })}`;
        }

        toast.success(successMessage);
        
        // Reset form
        setSelectedTeamId("");
        setSelectedPaymentPlanId("");
        setShouldAssignPaymentPlan(false);
        setUseProrated(false);
        setProratedAmount("");
        setCalculatedProrated(null);
        setIsAddDialogOpen(false);
        
        // Notify parent component
        onTeamChange?.();
      } else {
        toast.error(t('athletes.management.addToTeamError'));
      }
    } catch (error) {
      console.error("Error adding athlete to team:", error);
      toast.error(t('athletes.management.addToTeamError'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('athletes.table.teams')}
          </CardTitle>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                {t('athletes.management.joinTeam')}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t('athletes.management.joinTeam')}
                </DialogTitle>
                <DialogDescription>
                  {t('athletes.management.joinTeamDescription', { 
                    athleteName: `${athleteName} ${athleteSurname}` 
                  })}
                </DialogDescription>
              </DialogHeader>

              {loadingData ? (
                <div className="space-y-4">
                  <div className="h-10 bg-muted animate-pulse rounded-md" />
                  <div className="h-10 bg-muted animate-pulse rounded-md" />
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Team Selection */}
                  <div className="space-y-2">
                    <Label>{t('teams.details.team')} *</Label>
                    <Select value={selectedTeamId} onValueChange={setSelectedTeamId}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('teams.details.selectTeam')} />
                      </SelectTrigger>
                      <SelectContent>
                        {availableTeams.length === 0 ? (
                          <div className="p-2 text-center text-muted-foreground">
                            {t('athletes.management.noAvailableTeams')}
                          </div>
                        ) : (
                          availableTeams.map((team) => (
                            <SelectItem key={team.id} value={team.id}>
                              {team.name} ({t(`common.branches.${team.branchName}`, { ns: 'shared' })})
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Payment Plan Assignment Option */}
                  <Card className="border-dashed">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <Checkbox
                          id="assignPaymentPlan"
                          checked={shouldAssignPaymentPlan}
                          onCheckedChange={(checked) => setShouldAssignPaymentPlan(checked === true)}
                        />
                        <Label htmlFor="assignPaymentPlan" className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          {t('teams.management.assignPaymentPlan')}
                        </Label>
                      </div>

                      {shouldAssignPaymentPlan && (
                        <div className="space-y-2">
                          <Label>{t('payments.plans.plan')} *</Label>
                          <Select value={selectedPaymentPlanId} onValueChange={setSelectedPaymentPlanId}>
                            <SelectTrigger>
                              <SelectValue placeholder={t('payments.plans.select')} />
                            </SelectTrigger>
                            <SelectContent>
                              {availablePaymentPlans.length === 0 ? (
                                <div className="p-2 text-center text-muted-foreground">
                                  {t('teams.management.noAvailablePaymentPlans')}
                                </div>
                              ) : (
                                availablePaymentPlans.map((plan) => (
                                  <SelectItem key={plan.id} value={plan.id}>
                                    {plan.name} - {formatCurrency(plan.monthlyValue)}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                          
                          {/* Prorated Balance Option */}
                          {selectedPaymentPlanId && (
                            <div className="mt-3 p-3 bg-muted/50 rounded-md">
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id="useProrated"
                                  checked={useProrated}
                                  onCheckedChange={(checked) => setUseProrated(checked === true)}
                                />
                                <Label htmlFor="useProrated" className="text-sm font-normal">
                                  {t('athletes.proratedBalance', 'Apply prorated balance for remaining days of the month')}
                                </Label>
                              </div>
                              
                              {useProrated && (
                                <div className="mt-3 space-y-3">
                                  {/* Calculation Details */}
                                  {calculatedProrated !== null && (
                                    <div className="p-3 bg-background rounded border text-sm">
                                      <div className="space-y-2 text-left">
                                        <div className="flex justify-between">
                                          <span><strong>{t('athletes.selectedPlan', 'Selected Payment Plan')}:</strong></span>
                                        </div>
                                        <div className="pl-4 text-muted-foreground">
                                          {availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.name} - {parseFloat(availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.monthlyValue || '0').toFixed(2)} {t('common.currency', 'TL')}/{t('common.month', 'month')}
                                        </div>
                                        <div className="flex justify-between">
                                          <span><strong>{t('athletes.remainingDays', 'Remaining Days')}:</strong></span>
                                          <span>{getRemainingDaysInMonth()} / {getTotalDaysInMonth()}</span>
                                        </div>
                                        <div className="flex justify-between font-medium text-primary border-t pt-2">
                                          <span><strong>{t('athletes.calculatedAmount', 'Calculated Prorated Amount')}:</strong></span>
                                          <span>{calculatedProrated.toFixed(2)} {t('common.currency', 'TL')}</span>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                  
                                  {/* Editable Prorated Amount */}
                                  <div className="space-y-2">
                                    <Label htmlFor="proratedAmount" className="text-sm">
                                      {t('athletes.proratedAmountLabel', 'Prorated Amount')}
                                    </Label>
                                    <Input
                                      id="proratedAmount"
                                      type="number"
                                      step="0.01"
                                      min="0"
                                      value={proratedAmount}
                                      onChange={(e) => setProratedAmount(e.target.value)}
                                      placeholder={t('athletes.enterProratedAmount', 'Enter prorated amount')}
                                      className="text-sm"
                                    />
                                    <p className="text-xs text-muted-foreground">
                                      {t('athletes.proratedEditHint', 'You can modify the automatically calculated amount or enter a custom value.')}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Action Buttons */}
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                    >
                      {t('common.actions.cancel')}
                    </Button>
                    <Button
                      onClick={handleAddToTeam}
                      disabled={
                        isLoading || 
                        !selectedTeamId || 
                        availableTeams.length === 0 ||
                        (shouldAssignPaymentPlan && !selectedPaymentPlanId)
                      }
                    >
                      {isLoading ? t('common.actions.saving') : t('common.actions.add')}
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {athleteTeams.length > 0 ? (
          <div className="space-y-3">
            {athleteTeams.map((teamDetail) => (
              <div key={teamDetail.teamId} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{teamDetail.teamName}</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>{t(`common.branches.${teamDetail.branchName}`, { ns: 'shared' })} • {teamDetail.schoolName}</p>
                    <p>{t('instructors.title')}: {teamDetail.instructorName} {teamDetail.instructorSurname}</p>
                    <p>{t('athletes.management.joinedAt')}: {format(new Date(teamDetail.joinedAt), "dd/MM/yyyy")}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{t('common.status.active')}</Badge>
                  <RemoveAthleteFromTeamDialog
                    athleteId={athleteId}
                    athleteName={athleteName}
                    athleteSurname={athleteSurname}
                    teamId={teamDetail.teamId}
                    teamName={teamDetail.teamName}
                    onAthleteRemoved={onTeamChange}
                    triggerComponent={
                      <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                        <UserMinus className="h-4 w-4" />
                      </Button>
                    }
                  />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground mb-3">{t('athletes.messages.noTeams')}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setIsAddDialogOpen(true)}
            >
              {t('athletes.management.joinTeam')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
