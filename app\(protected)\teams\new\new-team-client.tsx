"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Calendar, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { School, Instructor, Branch, Facility } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createTeamWithSchedules } from "@/lib/actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FacilityAvailability } from "@/components/teams/facility-availability";
import { checkScheduleConflict } from "@/lib/schedule-utils";
import { getAllFacilitySchedules } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";

interface TrainingTime {
  id: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  facilityId: string;
}

interface NewTeamClientProps {
  schools: School[];
  instructors: Instructor[];
  facilities: Facility[];
}

export default function NewTeamClient({ 
  schools: initialSchools, 
  instructors: initialInstructors, 
  facilities 
}: NewTeamClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [schools] = useState<School[]>(initialSchools);
  const [instructors] = useState<Instructor[]>(initialInstructors);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedSchool, setSelectedSchool] = useState<string>("");
  const [selectedFacilityForAvailability, setSelectedFacilityForAvailability] = useState<string>("");
  const [facilitySchedules, setFacilitySchedules] = useState<any[]>([]);
  const [trainingTimes, setTrainingTimes] = useState<TrainingTime[]>([
    { id: "1", dayOfWeek: "1", startTime: "", endTime: "", facilityId: "" },
  ]);

  const [team, setTeam] = useState({
    name: "",
    schoolId: "",
    branchId: "",
    instructorId: "",
  });

  const handleSchoolChange = (schoolId: string) => {
    const school = schools.find(s => s.id === schoolId);
    if (school) {
      setBranches(school.branches);
      setSelectedSchool(schoolId);
      setTeam(prev => ({
        ...prev,
        schoolId,
        branchId: "",
        instructorId: "",
      }));
    }
  };

  // Load facility schedules on component mount
  useEffect(() => {
    async function loadFacilitySchedules() {
      try {
        const schedules = await getAllFacilitySchedules();
        setFacilitySchedules(schedules);
      } catch (error) {
        console.error("Failed to load facility schedules:", error);
      }
    }
    loadFacilitySchedules();
  }, []);

  // Function to check if a training time has conflicts
  const hasConflict = (trainingTime: TrainingTime) => {
    if (!trainingTime.facilityId || !trainingTime.startTime || !trainingTime.endTime || !trainingTime.dayOfWeek) {
      return false;
    }

    const conflictResult = checkScheduleConflict(
      facilitySchedules,
      trainingTime.facilityId,
      parseInt(trainingTime.dayOfWeek),
      trainingTime.startTime,
      trainingTime.endTime
    );
    
    return conflictResult.hasConflict;
  };

  const addTrainingTime = () => {
    setTrainingTimes(prev => [
      ...prev,
      {
        id: String(prev.length + 1),
        dayOfWeek: "1",
        startTime: "",
        endTime: "",
        facilityId: "",
      },
    ]);
  };

  const removeTrainingTime = (id: string) => {
    setTrainingTimes(prev => prev.filter(time => time.id !== id));
  };

  const updateTrainingTime = (
    id: string,
    field: keyof TrainingTime,
    value: string
  ) => {
    setTrainingTimes(prev =>
      prev.map(time =>
        time.id === id ? { ...time, [field]: value } : time
      )
    );
    
    // Update selected facility for availability if facility changes
    if (field === "facilityId" && value) {
      setSelectedFacilityForAvailability(value);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate form data
      if (!team.name || !team.schoolId || !team.branchId) {
        toast({
          title: t('common.error'),
          description: t('common.validation.requiredFields'),
          variant: "destructive",
        });
        return;
      }

      // Check for schedule conflicts only for complete schedules
      const completeSchedules = trainingTimes.filter(time =>
        time.dayOfWeek && time.startTime && time.endTime
      );
      const hasAnyConflicts = completeSchedules.some(time => hasConflict(time));
      if (hasAnyConflicts) {
        toast({
          title: t('common.error'),
          description: t('teams.messages.conflictError'),
          variant: "destructive",
        });
        return;
      }

      // Prepare training schedules
      const validSchedules = trainingTimes
        .filter(time => time.dayOfWeek && time.startTime && time.endTime)
        .map(time => ({
          dayOfWeek: parseInt(time.dayOfWeek),
          startTime: time.startTime,
          endTime: time.endTime,
          facilityId: time.facilityId || null,
        }));

      // Create team with schedules
      await createTeamWithSchedules(
        {
          name: team.name,
          schoolId: team.schoolId,
          branchId: team.branchId,
          instructorId: team.instructorId || null,
        },
        validSchedules
      );
      toast({
        title: t('common.success'),
        description: t('teams.messages.createSuccess'),
      });
      router.push("/teams");
      router.refresh();
    } catch (error) {
      console.error("Failed to create team:", error);
      toast({
        title: t('common.error'),
        description: t('teams.messages.createError'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredInstructors = instructors.filter(instructor => 
    instructor.schools.includes(selectedSchool)
  );

  const daysOfWeek = [
    { value: "1", label: t("teams.days.monday") },
    { value: "2", label: t("teams.days.tuesday") },
    { value: "3", label: t("teams.days.wednesday") },
    { value: "4", label: t("teams.days.thursday") },
    { value: "5", label: t("teams.days.friday") },
    { value: "6", label: t("teams.days.saturday") },
    { value: "0", label: t("teams.days.sunday") },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/teams">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold tracking-tight">{t("teams.new")}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t("teams.details.name")}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t("teams.details.name")}</Label>
                <Input
                  id="name"
                  value={team.name}
                  onChange={(e) =>
                    setTeam((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder={t("teams.details.name")}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="school">{t("teams.details.school")}</Label>
                <Select
                  value={team.schoolId}
                  onValueChange={handleSchoolChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("teams.details.school")} />
                  </SelectTrigger>
                  <SelectContent>
                    {schools.map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="branch">{t("teams.details.branch")}</Label>
                <Select
                  value={team.branchId}
                  onValueChange={(value) =>
                    setTeam((prev) => ({ ...prev, branchId: value }))
                  }
                  disabled={!selectedSchool}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("teams.details.branch")} />
                  </SelectTrigger>
                  <SelectContent>
                    {branches
                      .filter(branch => branch.id !== null)
                      .map((branch) => (
                      <SelectItem key={branch.id} value={branch.id!}>
                        {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructor">{t("teams.details.instructor")}</Label>
                <Select
                  value={team.instructorId}
                  onValueChange={(value) =>
                    setTeam((prev) => ({ ...prev, instructorId: value }))
                  }
                  disabled={!selectedSchool}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("teams.details.instructor")} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredInstructors.map((instructor) => (
                      <SelectItem key={instructor.id} value={instructor.id}>
                        {instructor.name} {instructor.surname}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>{t("teams.details.schedule")} ({t("common.optional")})</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {t("teams.messages.scheduleOptional")}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addTrainingTime}
                  >
                    {t("teams.schedule.addSlot")}
                  </Button>
                </div>
                
                {trainingTimes.map((time) => {
                  const conflict = hasConflict(time);
                  return (
                  <div key={time.id}>
                    <div className="grid grid-cols-5 gap-4 items-end">
                      <div className="space-y-2">
                        <Label>{t("teams.schedule.day")}</Label>
                        <Select
                          value={time.dayOfWeek}
                          onValueChange={(value) =>
                            updateTrainingTime(time.id, "dayOfWeek", value)
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={t("teams.schedule.day")} />
                          </SelectTrigger>
                          <SelectContent>
                            {daysOfWeek.map((day) => (
                              <SelectItem key={day.value} value={day.value}>
                                {day.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>{t("teams.schedule.startTime")}</Label>
                        <Input
                          type="time"
                          value={time.startTime}
                          onChange={(e) =>
                            updateTrainingTime(time.id, "startTime", e.target.value)
                          }
                          className={conflict ? "border-red-500" : ""}
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label>{t("teams.schedule.endTime")}</Label>
                        <Input
                          type="time"
                          value={time.endTime}
                          onChange={(e) =>
                            updateTrainingTime(time.id, "endTime", e.target.value)
                          }
                          className={conflict ? "border-red-500" : ""}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label>{t("teams.schedule.facility")} ({t("common.optional")})</Label>
                        <Select
                          value={time.facilityId || "none"}
                          onValueChange={(value) =>
                            updateTrainingTime(time.id, "facilityId", value === "none" ? "" : value)
                          }
                        >
                          <SelectTrigger className={conflict ? "border-red-500" : ""}>
                            <SelectValue placeholder={t("common.selectFacility")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">{t("common.actions.none")}</SelectItem>
                            {facilities.map((facility) => (
                              <SelectItem key={facility.id} value={facility.id}>
                                {facility.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex justify-end">
                        {trainingTimes.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeTrainingTime(time.id)}
                          >
                            {t("teams.schedule.remove")}
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {/* Conflict Warning */}
                    {conflict && (
                      <div className="mt-2 flex items-center space-x-2 text-red-600 text-sm">
                        <AlertTriangle className="h-4 w-4" />
                        <span>{t("teams.schedule.conflictWarning")}</span>
                      </div>
                    )}
                  </div>
                )})}
              </div>

              {/* Facility Availability Section */}
              {selectedFacilityForAvailability && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <Label className="text-base font-medium">
                      {t("teams.schedule.facilityAvailability")}
                    </Label>
                  </div>
                  <FacilityAvailability
                    facilityId={selectedFacilityForAvailability}
                    facilityName={
                      facilities.find(f => f.id === selectedFacilityForAvailability)?.name || ""
                    }
                    selectedDay={
                      trainingTimes.find(t => t.facilityId === selectedFacilityForAvailability)?.dayOfWeek
                        ? parseInt(trainingTimes.find(t => t.facilityId === selectedFacilityForAvailability)!.dayOfWeek)
                        : undefined
                    }
                    selectedStartTime={
                      trainingTimes.find(t => t.facilityId === selectedFacilityForAvailability)?.startTime
                    }
                    selectedEndTime={
                      trainingTimes.find(t => t.facilityId === selectedFacilityForAvailability)?.endTime
                    }
                  />
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/teams")}
              >
                {t("common.actions.cancel")}
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? t("common.loading") : t("common.actions.create")}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}