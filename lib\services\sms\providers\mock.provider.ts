import { SmsProvider, SendSmsParams, SmsProviderResponse } from '../types';

/**
 * Mock SMS Provider
 * Useful for testing and development
 */
export class MockSmsProvider implements SmsProvider {
  private readonly shouldFail: boolean;
  private readonly delay: number;

  constructor(config?: {
    shouldFail?: boolean;
    delay?: number;
  }) {
    this.shouldFail = config?.shouldFail || false;
    this.delay = config?.delay || 100;
  }

  /**
   * Mock SMS sending
   * @param params SMS parameters
   * @returns Promise with mock response
   */
  async sendSms(params: SendSmsParams): Promise<SmsProviderResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, this.delay));

    console.log('Mock SMS Provider - sendSms called with:', {
      senderIdentifier: params.senderIdentifier,
      encoding: params.encoding,
      messageCount: params.messages.length,
      messages: params.messages.map(msg => ({
        receiver: msg.receiver,
        messageLength: msg.message.length
      }))
    });

    // Simulate failure if configured
    if (this.shouldFail) {
      return {
        success: false,
        error: 'Mock provider configured to fail',
        errorCode: 'MOCK_FAILURE'
      };
    }

    // Note: Validation is handled at the service layer
    // Provider assumes all parameters are valid

    // Generate mock message IDs
    const mockMessageIds = params.messages.map((_, index) => 
      `mock_${Date.now()}_${index}`
    );

    return {
      success: true,
      data: {
        provider: 'Mock',
        sentAt: new Date().toISOString(),
        messageCount: params.messages.length,
        mockDelay: this.delay
      },
      messageIds: mockMessageIds
    };
  }

  /**
   * Get provider name
   */
  getProviderName(): string {
    return 'Mock SMS Provider';
  }
}
