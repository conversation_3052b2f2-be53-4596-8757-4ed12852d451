"use server";

import { redirect } from "next/navigation";
import { athleteService } from "@/lib/services";
import { getServerTenantId, getServerUserId } from "@/lib/tenant-utils-server";

interface CreateAthleteData {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  teamAssignments?: Array<{
    teamId: string;
    paymentPlanId?: string;
  }>;
  initialBalance?: string;
  useProrated?: boolean;
}

/**
 * Server action wrapper for athlete creation
 * This follows the architecture pattern: Client → Server Component → Server Action → Service
 */
export async function createAthleteAction(
  formData: FormData,
  locale: string = 'en'
) {
  try {
    // Extract and validate form data
    const athleteData: CreateAthleteData = {
      name: formData.get('name') as string,
      surname: formData.get('surname') as string,
      nationalId: formData.get('nationalId') as string,
      birthDate: formData.get('birthDate') as string,
      parentName: formData.get('parentName') as string,
      parentSurname: formData.get('parentSurname') as string,
      parentPhone: formData.get('parentPhone') as string,
      parentEmail: formData.get('parentEmail') as string || undefined,
      parentAddress: formData.get('parentAddress') as string || undefined,
      initialBalance: formData.get('initialBalance') as string || undefined,
      useProrated: formData.get('useProrated') === 'true',
    };

    // Parse team assignments from JSON if provided
    const teamAssignmentsJson = formData.get('teamAssignments') as string;
    if (teamAssignmentsJson) {
      try {
        athleteData.teamAssignments = JSON.parse(teamAssignmentsJson);
      } catch (e) {
        console.error('Failed to parse team assignments:', e);
      }
    }

    // Basic validation
    if (!athleteData.name?.trim() || !athleteData.surname?.trim()) {
      return {
        success: false,
        error: 'Name and surname are required'
      };
    }

    // Get tenant and user context
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    // Call service layer directly (following best practices)
    const result = await athleteService().createAthleteWithTeamAssignmentsAndBalance(
      {
        name: athleteData.name,
        surname: athleteData.surname,
        nationalId: athleteData.nationalId,
        birthDate: athleteData.birthDate,
        parentName: athleteData.parentName,
        parentSurname: athleteData.parentSurname,
        parentPhone: athleteData.parentPhone,
        parentEmail: athleteData.parentEmail || "",
        parentAddress: athleteData.parentAddress || "",
        teamAssignments: athleteData.teamAssignments,
        initialBalance: athleteData.initialBalance,
        useProrated: athleteData.useProrated,
      },
      userId?.toString(),
      tenantId || undefined,
      locale
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.userMessage || 'Failed to create athlete'
      };
    }

    if (result.success) {
      // Redirect on success
      redirect('/athletes');
    }

    return {
      success: false,
      error: result.error?.userMessage || 'Failed to create athlete'
    };
  } catch (error) {
    console.error('Error in createAthleteAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create athlete'
    };
  }
}

/**
 * Server action for JSON-based submissions (for progressive enhancement)
 */
export async function createAthleteJsonAction(
  data: CreateAthleteData,
  locale: string = 'en'
) {
  try {
    // Basic validation
    if (!data.name?.trim() || !data.surname?.trim()) {
      return {
        success: false,
        error: 'Name and surname are required'
      };
    }

    // Get tenant and user context
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    // Call service layer directly (following best practices)
    const result = await athleteService().createAthleteWithTeamAssignmentsAndBalance(
      {
        name: data.name,
        surname: data.surname,
        nationalId: data.nationalId,
        birthDate: data.birthDate,
        parentName: data.parentName,
        parentSurname: data.parentSurname,
        parentPhone: data.parentPhone,
        parentEmail: data.parentEmail || "",
        parentAddress: data.parentAddress || "",
        teamAssignments: data.teamAssignments,
        initialBalance: data.initialBalance,
        useProrated: data.useProrated,
      },
      userId?.toString(),
      tenantId || undefined,
      locale
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.userMessage || 'Failed to create athlete'
      };
    }

    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    console.error('Error in createAthleteJsonAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create athlete'
    };
  }
}
