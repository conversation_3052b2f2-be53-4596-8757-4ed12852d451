'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Plus, CreditCard, CheckCircle } from 'lucide-react';
import { calculateSmsPrice } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import PaymentDialog from '@/components/sms/payment-dialog';
import {useToast} from "@/hooks/use-toast";

const smsBalanceSchema = z.object({
  amount: z.number().min(1, 'Amount must be at least 1').max(10000, 'Amount cannot exceed 10,000'),
});

type SmsBalanceFormData = z.infer<typeof smsBalanceSchema>;

interface SmsBalanceFormProps {
  currentBalance: number;
  onBalanceUpdate: () => void;
}

export default function SmsBalanceForm({ currentBalance, onBalanceUpdate }: SmsBalanceFormProps) {
  const { t } = useSafeTranslation();
  const { toast }= useToast();
  const [isPending, startTransition] = useTransition();
  const [addResult, setAddResult] = useState<{ success: boolean; newBalance: number } | null>(null);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [priceCalculation, setPriceCalculation] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<SmsBalanceFormData>({
    resolver: zodResolver(smsBalanceSchema),
    defaultValues: {
      amount: 100
    }
  });

  const amount = watch('amount');

  const quickAmounts = [50, 100, 250, 500, 1000];

  // Calculate price when amount changes
  useEffect(() => {
    if (amount && amount > 0) {
      startTransition(async () => {
        try {
          const result = await calculateSmsPrice(amount);
          if (result.success) {
            setPriceCalculation(result.data);
          }
        } catch (error) {
          console.error('Error calculating price:', error);
          setPriceCalculation(null);
        }
      });
    } else {
      setPriceCalculation(null);
    }
  }, [amount]);

  const formatPrice = (priceInCents: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(priceInCents / 100);
  };

  const onSubmit = (data: SmsBalanceFormData) => {
    if (!priceCalculation) {
      toast({
        title: t('common.error'),
        description: t('sms:balance.messages.waitPriceCalculation'),
        variant: "destructive",
      });
      return;
    }

    setPaymentDialogOpen(true);
  };

  const handlePaymentSuccess = () => {
    setAddResult({ success: true, newBalance: currentBalance + (amount || 0) });
    onBalanceUpdate();
    reset();
    setPriceCalculation(null);
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Quick Amount Buttons */}
        <div className="space-y-2">
          <Label>{t('sms:balance.add.quickAmounts')}</Label>
          <div className="flex flex-wrap gap-2">
            {quickAmounts.map((quickAmount) => (
              <Button
                key={quickAmount}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setValue('amount', quickAmount)}
                className={amount === quickAmount ? 'bg-primary text-primary-foreground' : ''}
              >
                {quickAmount} {t('sms:common.credits')}
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Amount */}
        <div className="space-y-2">
          <Label htmlFor="amount">{t('sms:balance.add.customAmount')}</Label>
          <div className="flex space-x-2">
            <Input
              id="amount"
              type="number"
              {...register('amount', { valueAsNumber: true })}
              placeholder={t('sms:balance.add.placeholder')}
              className={errors.amount ? 'border-red-500' : ''}
            />
            <Button type="submit" disabled={isPending}>
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Plus className="mr-2 h-4 w-4" />
              {t('sms:balance.payment.payNow')}
            </Button>
          </div>
          {errors.amount && (
            <p className="text-sm text-red-500">{errors.amount.message}</p>
          )}
        </div>

        {/* Price Preview */}
        {amount && amount > 0 && (
          <div className="p-4 bg-muted rounded-md space-y-3">
            {priceCalculation ? (
              <>
                <div className="flex items-center justify-between text-sm">
                  <span>{t('sms:balance.add.preview.credits')}</span>
                  <span className="font-medium">{amount.toLocaleString()} {t('sms:common.credits')}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>{t('sms:balance.add.preview.pricePerCredit')}</span>
                  <span className="font-medium">{formatPrice(priceCalculation.pricePerCredit)}</span>
                </div>
                <hr className="my-2" />
                <div className="flex items-center justify-between font-medium text-lg">
                  <span>{t('sms:balance.add.preview.totalPrice')}</span>
                  <span className="text-green-600">{formatPrice(priceCalculation.totalPrice)}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {t('sms:balance.add.preview.tier')}: {priceCalculation.tier.description}
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm text-muted-foreground">{t('sms:balance.add.preview.calculating')}</span>
              </div>
            )}
          </div>
        )}
      </form>

      {/* Add Result */}
      {addResult && (
        <Alert className={addResult.success ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}>
          {addResult.success ? (
            <CheckCircle className="h-4 w-4 text-green-600" />
          ) : (
            <CreditCard className="h-4 w-4 text-red-600" />
          )}
          <AlertDescription className={addResult.success ? 'text-green-800' : 'text-red-800'}>
            {addResult.success 
              ? t('sms:balance.messages.successBalance').replace('{balance}', addResult.newBalance.toString())
              : t('sms:balance.messages.errorBalance')
            }
          </AlertDescription>
        </Alert>
      )}



      {/* Payment Dialog */}
      {priceCalculation && (
        <PaymentDialog
          open={paymentDialogOpen}
          onOpenChange={setPaymentDialogOpen}
          credits={amount || 0}
          totalPrice={priceCalculation.totalPrice}
          pricePerCredit={priceCalculation.pricePerCredit}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}
