{"schools": {"title": "Schools", "new": "New School", "edit": "Edit School", "details": {"name": "School Name", "logo": "School Logo", "address": "Address", "phone": "Phone Number", "email": "Email", "foundedYear": "Founded Year", "information": "School Information", "branches": "Branches", "instructors": "Instructors"}, "actions": {"viewDetails": "View Details", "addNew": "Add a new school", "backToSchools": "Back to Schools", "saveChanges": "Save Changes", "createSchool": "Create School", "deleteConfirmTitle": "Delete School", "deleteConfirmMessage": "Are you sure you want to delete {{name}}? This action cannot be undone."}, "messages": {"noLogo": "No logo", "instructorCount": "{{count}} Instructor", "instructorCount_other": "{{count}} Instructors", "updateError": "Failed to update school", "updateSuccess": "School updated successfully", "deleteSuccess": "School deleted successfully", "deleteSuccessDescription": "The school has been removed from the system", "deleteError": "Failed to delete school", "createSuccess": "School created successfully", "createError": "Failed to create school", "branchLoadError": "Failed to load branches", "noBranchesAvailable": "No branches available"}, "placeholders": {"enterName": "Enter school name", "enterAddress": "Enter street address", "enterPhone": "Enter phone number", "enterEmail": "<EMAIL>", "uploadImage": "Click to upload or drag and drop", "imageFormats": "PNG, JPG or WEBP (MAX. 5MB)", "changeImage": "Change Image", "newSchoolDescription": "Enter the details for the new school.", "selectBranches": "Select the branches this school offers", "noBranchesSelected": "No branches selected"}}}