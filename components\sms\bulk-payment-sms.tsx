'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { MessageSquare, AlertTriangle } from 'lucide-react';
import { Payment } from '@/lib/types';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import PaymentReminderDialog from './payment-reminder-dialog';

interface BulkPaymentSmsProps {
  payments: Payment[];
}

export default function BulkPaymentSms({ payments }: BulkPaymentSmsProps) {
  const { t } = useSafeTranslation();
  const [selectedPayments, setSelectedPayments] = useState<Payment[]>([]);

  // Filter payments that can receive SMS reminders
  const eligiblePayments = useMemo(() => {
    return payments.filter(payment => 
      (payment.status === 'pending' || payment.status === 'overdue') &&
      payment.athlete?.parentPhone
    );
  }, [payments]);

  const pendingPayments = useMemo(() => {
    return eligiblePayments.filter(payment => payment.status === 'pending');
  }, [eligiblePayments]);

  const overduePayments = useMemo(() => {
    return eligiblePayments.filter(payment => payment.status === 'overdue');
  }, [eligiblePayments]);

  const handleSelectAll = (paymentList: Payment[]) => {
    setSelectedPayments(paymentList);
  };

  const handleClearSelection = () => {
    setSelectedPayments([]);
  };

  if (eligiblePayments.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <MessageSquare className="h-5 w-5" />
            <p>{t('sms:sending.bulk.noEligible')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5" />
          <span>{t('sms:sending.bulk.title')}</span>
        </CardTitle>
        <CardDescription>
          {t('sms:sending.bulk.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{pendingPayments.length}</div>
              <div className="text-sm text-muted-foreground">{t('sms:sending.bulk.pending')}</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-red-600">{overduePayments.length}</div>
              <div className="text-sm text-muted-foreground">{t('sms:sending.bulk.overdue')}</div>
            </div>
            <div className="text-center p-4 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-green-600">{eligiblePayments.length}</div>
              <div className="text-sm text-muted-foreground">{t('sms:sending.bulk.totalEligible')}</div>
            </div>
          </div>

          {/* Payment Selection */}
          <div className="space-y-4">
            <h4 className="font-medium">{t('sms:sending.bulk.selectPayments')}</h4>
            <div className="max-h-60 overflow-y-auto border rounded-md">
              <div className="p-3 space-y-2">
                {eligiblePayments.map((payment) => (
                  <div key={payment.id} className="flex items-center space-x-3 p-2 hover:bg-muted rounded-md">
                    <Checkbox
                      id={`payment-${payment.id}`}
                      checked={selectedPayments.some(p => p.id === payment.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedPayments(prev => [...prev, payment]);
                        } else {
                          setSelectedPayments(prev => prev.filter(p => p.id !== payment.id));
                        }
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium truncate">
                            {payment.athlete?.name} {payment.athlete?.surname}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {payment.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 ml-2">
                          <span className="text-sm font-medium">{payment.amount}</span>
                          <Badge
                            variant={payment.status === 'overdue' ? 'destructive' : 'secondary'}
                            className="text-xs"
                          >
                            {t(`sms:status.${payment.status}`)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">{t('sms:sending.bulk.quickActions')}</h4>
              {selectedPayments.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    {selectedPayments.length} {t('sms:sending.bulk.selected')}
                  </Badge>
                  <Button variant="outline" size="sm" onClick={handleClearSelection}>
                    {t('sms:sending.bulk.clearSelection')}
                  </Button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Pending Payments */}
              {pendingPayments.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('sms:sending.bulk.pending')}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSelectAll(pendingPayments)}
                    >
                      {t('sms:sending.bulk.selectAll')} ({pendingPayments.length})
                    </Button>
                  </div>
                  <PaymentReminderDialog
                    payments={selectedPayments.length > 0 ? selectedPayments.filter(p => p.status === 'pending') : pendingPayments}
                    defaultTemplateType="pending"
                    trigger={
                      <Button className="w-full" variant="outline">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        {t('sms:sending.bulk.sendPending')}
                        {selectedPayments.length > 0 && selectedPayments.some(p => p.status === 'pending')
                          ? ` (${selectedPayments.filter(p => p.status === 'pending').length})`
                          : ` (${pendingPayments.length})`
                        }
                      </Button>
                    }
                    onSuccess={handleClearSelection}
                  />
                </div>
              )}

              {/* Overdue Payments */}
              {overduePayments.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{t('sms:sending.bulk.overdue')}</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSelectAll(overduePayments)}
                    >
                      {t('sms:sending.bulk.selectAll')} ({overduePayments.length})
                    </Button>
                  </div>
                  <PaymentReminderDialog
                    payments={selectedPayments.length > 0 ? selectedPayments.filter(p => p.status === 'overdue') : overduePayments}
                    defaultTemplateType="overdue"
                    trigger={
                      <Button className="w-full" variant="destructive">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        {t('sms:sending.bulk.sendOverdue')}
                        {selectedPayments.length > 0 && selectedPayments.some(p => p.status === 'overdue')
                          ? ` (${selectedPayments.filter(p => p.status === 'overdue').length})`
                          : ` (${overduePayments.length})`
                        }
                      </Button>
                    }
                    onSuccess={handleClearSelection}
                  />
                </div>
              )}
            </div>
          </div>



          {/* Important Notes */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-yellow-800">Important Notes</p>
                <ul className="text-xs text-yellow-700 space-y-1">
                  <li>• Only payments with valid parent phone numbers are eligible</li>
                  <li>• Each SMS will consume 1 credit from your balance</li>
                  <li>• Messages are sent immediately and cannot be cancelled</li>
                  <li>• Failed messages will not consume credits</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
