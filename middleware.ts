import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import acceptLanguage from 'accept-language';
// Note: Using middleware-specific utilities instead of lib/tenant-utils.ts
// to avoid Edge Runtime compatibility issues with server-side dependencies
import { extractTenantId, extractUserId } from './lib/middleware-tenant-utils';

acceptLanguage.languages(['en', 'tr']);

const languages = ['en', 'tr'];

// Protected routes that require authentication
const protectedRoutes = ['/dashboard', '/athletes', '/instructors', '/teams', '/facilities', '/items', '/expenses', '/payments', '/schools'];

// Update matcher to exclude only public auth routes and NextAuth endpoints
export const config = {
  matcher: [
    '/((?!api/auth/signin|api/auth/signup|api/auth/callback|api/auth/csrf|api/auth/session|api/auth/providers|api/health|_next/static|_next/image|assets|favicon.ico|sw.js).*)',
  ],
};

export async function middleware(request: NextRequest) {
  // Handle authentication for protected routes
  const { pathname } = request.nextUrl;
  
  // Check if the route is protected
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isApiRoute = pathname.startsWith('/api/');
  const isAuthRoute = pathname.startsWith('/auth');
  
  // Validate session token using NextAuth's getToken function
  // This properly verifies the JWT signature and expiration
  let token = null;
  try {
    token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
      secureCookie: process.env.NODE_ENV === 'production'
    });
  } catch (error) {
    console.error('Token validation error:', error);
    // If token validation fails, treat as unauthenticated
    token = null;
  }
  
  // If accessing protected route without valid session, redirect to signin
  if (isProtectedRoute && !token) {
    return NextResponse.redirect(new URL('/auth/signin', request.url));
  }
  
  // If accessing any API route without valid session, return 401
  // (since public API routes are excluded in config.matcher)
  if (isApiRoute && !token) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }
  
  // For protected routes and API routes, validate tenant access
  if ((isProtectedRoute || isApiRoute) && token) {
    let tenantId: string | null = null;
    let userId: bigint | null = null;
    
    // First, try to get from token properties (session-stored values) - most efficient
    if (token.tenantId && token.userId) {
      tenantId = token.tenantId as string;
      try {
        userId = BigInt(token.userId as string);
      } catch (error) {
        console.error('Error converting token userId to bigint:', error);
      }
    }
    
    // Fallback: extract from token structure (less efficient)
    if (!tenantId || !userId) {
      if (!tenantId) {
        tenantId = extractTenantId(token);
      }
      if (!userId) {
        userId = extractUserId(token);
      }
    }
    
    // Debug: Log tenant extraction for development (but not for every request)
    if (process.env.NODE_ENV === 'development' && !pathname.includes('/_next') && !pathname.includes('/api/')) {
      console.log(`🔍 Middleware - Path: ${pathname}, Tenant ID: ${tenantId}, User ID: ${userId}`);
    }
    
    if (!tenantId) {
      console.error('No tenant ID found in token for protected route');
      // For API routes, return JSON error; for page routes, redirect
      if (isApiRoute) {
        return NextResponse.json(
          { error: 'No organization found' },
          { status: 403 }
        );
      } else {
        return NextResponse.redirect(new URL('/auth/signin?error=no-organization', request.url));
      }
    }
    
    if (!userId) {
      console.error('No user ID found in token for protected route');
      // For API routes, return JSON error; for page routes, redirect
      if (isApiRoute) {
        return NextResponse.json(
          { error: 'Invalid user' },
          { status: 403 }
        );
      } else {
        return NextResponse.redirect(new URL('/auth/signin?error=invalid-user', request.url));
      }
    }
    
    // Add tenant ID and user ID to request headers for use in API routes
    const response = NextResponse.next();
    response.headers.set('x-tenant-id', tenantId);
    response.headers.set('x-user-id', userId.toString());
    
    // Handle language for protected routes (skip for API routes)
    if (isProtectedRoute) {
      handleLanguageSettings(request, response);
    }
    
    return response;
  }
  
  // If authenticated user tries to access auth pages, redirect to dashboard
  if (isAuthRoute && token && pathname !== '/auth/signout') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Handle language detection and setting
  let lng: string | null | undefined;
  if (request.cookies.has('NEXT_LOCALE')) {
    lng = acceptLanguage.get(request.cookies.get('NEXT_LOCALE')?.value);
  }
  if (!lng) {
    lng = acceptLanguage.get(request.headers.get('Accept-Language'));
  }
  if (!lng) {
    lng = 'en';
  }

  // Redirect root to dashboard if authenticated, otherwise to signin
  if (pathname === '/') {
    if (token) {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } else {
      return NextResponse.redirect(new URL('/auth/signin', request.url));
    }
  }

  if (request.headers.has('referer')) {
    const refererUrl = new URL(request.headers.get('referer') || '');
    const lngInReferer = languages.find(
      (l) => refererUrl.pathname.startsWith(`/${l}`) || l === lng
    );
    const response = NextResponse.next();
    if (lngInReferer) {
      response.cookies.set('NEXT_LOCALE', lngInReferer);
    }
    return response;
  }

  return NextResponse.next();
}

function handleLanguageSettings(request: NextRequest, response: NextResponse) {
  let lng: string | null | undefined;
  if (request.cookies.has('NEXT_LOCALE')) {
    lng = acceptLanguage.get(request.cookies.get('NEXT_LOCALE')?.value);
  }
  if (!lng) {
    lng = acceptLanguage.get(request.headers.get('Accept-Language'));
  }
  if (!lng) {
    lng = 'en';
  }

  if (request.headers.has('referer')) {
    const refererUrl = new URL(request.headers.get('referer') || '');
    const lngInReferer = languages.find(
      (l) => refererUrl.pathname.startsWith(`/${l}`) || l === lng
    );
    if (lngInReferer) {
      response.cookies.set('NEXT_LOCALE', lngInReferer);
    }
  }
}