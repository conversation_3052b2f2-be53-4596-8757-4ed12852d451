// Initialize the payment scheduler when the application starts
import { initializeScheduler } from './index';

// Global flag to prevent multiple initializations
let isSchedulerInitialized = false;

// Initialize scheduler function that can be called manually
export function initPaymentScheduler() {
  if (isSchedulerInitialized) {
    console.log('Payment scheduler already initialized, skipping...');
    return;
  }
  
  // Don't run on client side
  if (typeof window !== 'undefined') {
    console.log('Skipping payment scheduler initialization on client side');
    return;
  }
  
  console.log('🚀 Starting payment scheduler initialization...');
  isSchedulerInitialized = true;
  
  try {
    initializeScheduler();
    console.log('✅ Payment scheduler initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize payment scheduler:', error);
    isSchedulerInitialized = false; // Reset flag on error
  }
}

export {};

export {};
