"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, MoreHorizontal, Pencil, Trash, Package2, ShoppingCart, Eye } from "lucide-react";
import { Item } from "@/lib/types";
import { SecureImage } from "@/components/ui/secure-image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { deleteItem } from "@/lib/actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON>D<PERSON>og<PERSON>ooter,
  <PERSON><PERSON>DialogHead<PERSON>,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

interface ItemsListClientProps {
  items: Item[];
}

function ItemCard({ item }: { item: Item }) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleDelete = async () => {
    try {
      const result = await deleteItem(item.id);
      if(!result.success){
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'items.messages.deleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
        return;
      }else{
        toast({
          title: t('items.messages.deleteSuccess'),
          description: item.name + ' ' + t('items.messages.hasBeenDeleted')
        });
        // Refresh the page to show updated list
        window.location.reload();
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('items.messages.deleteError'),
        variant: "destructive"
      });
      console.error("Error deleting item:", error);
    }
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { label: t('items.status.outOfStock'), color: "text-red-500" };
    if (stock < 5) return { label: t('items.status.lowStock'), color: "text-yellow-500" };
    return { label: t('items.status.inStock'), color: "text-green-500" };
  };

  const status = getStockStatus(item.stock);

  // Format price to number for display
  const formattedPrice = parseFloat(item.price);

  return (
    <div className="relative">
      <Link href={`/items/${item.id}`} className="block">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg font-medium">{item.name}</CardTitle>
            <div className="relative z-10" onClick={(e) => e.preventDefault()}>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">{t('items.actions.openMenu')}</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href={`/items/${item.id}`} className="flex items-center">
                      <Eye className="mr-2 h-4 w-4" />
                      {t('items.actions.viewDetails')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/items/${item.id}/sell`} className="flex items-center">
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      {t('items.actions.sellItem')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href={`/items/${item.id}/edit`} className="flex items-center">
                      <Pencil className="mr-2 h-4 w-4" />
                      {t('items.actions.editItem')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive"
                    onSelect={(e) => {
                      e.preventDefault();
                      setShowDeleteDialog(true);
                    }}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    {t('items.actions.deleteItem')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t('common.actions.confirm')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('items.messages.deleteConfirm')}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
                  <AlertDialogAction
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    onClick={handleDelete}
                  >
                    {t('items.actions.deleteItem')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardHeader>
          <CardContent>
            <div className="relative aspect-square mb-4">
              <SecureImage
                src={item.image || ""}
                alt={item.name}
                fill
                className="object-cover rounded-md"
                placeholderIcon={Package2}
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="capitalize">
                  {t(`items.categories.${item.category}`)}
                </Badge>
                <span className="font-bold">{formattedPrice.toFixed(2)} {t('common.currency')}</span>
              </div>
              
              {item.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {item.description}
                </p>
              )}
              
              <div className="flex items-center justify-between text-sm">
                <span>{t('items.details.stock')}:</span>
                <span className={status.color}>{item.stock} ({status.label})</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    </div>
  );
}

export default function ItemsListClient({ items }: ItemsListClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('items.title')}</h1>
        <Button asChild>
          <Link href="/items/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            {t('items.addItem')}
          </Link>
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {items.map((item) => (
          <ItemCard key={item.id} item={item} />
        ))}
        
        <Link href="/items/new" className="block">
          <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
                <PlusCircle className="h-12 w-12 text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground text-center">
                {t('items.actions.addNew')}
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
