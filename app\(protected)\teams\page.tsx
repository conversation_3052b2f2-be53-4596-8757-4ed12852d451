import React, { Suspense } from "react";
import { getTeamsPaginated } from "@/lib/db/actions/teams";
import { TeamsListPaginated } from "./teams-list-paginated";

interface TeamsPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    name?: string;
    description?: string;
    schoolName?: string;
    branchName?: string;
    instructorName?: string;
  }>;
}

async function TeamsListServer({ searchParams }: TeamsPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = parseInt(resolvedSearchParams.limit || '10');
  const search = resolvedSearchParams.search;
  const sortBy = resolvedSearchParams.sortBy || 'createdAt';
  const sortOrder = resolvedSearchParams.sortOrder || 'desc';
  
  const filters: Record<string, string> = {};
  if (resolvedSearchParams.name) filters.name = resolvedSearchParams.name;
  if (resolvedSearchParams.description) filters.description = resolvedSearchParams.description;
  if (resolvedSearchParams.schoolName) filters.schoolName = resolvedSearchParams.schoolName;
  if (resolvedSearchParams.branchName) filters.branchName = resolvedSearchParams.branchName;
  if (resolvedSearchParams.instructorName) filters.instructorName = resolvedSearchParams.instructorName;

  const result = await getTeamsPaginated(
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    filters
  );

  return (
    <TeamsListPaginated 
      initialData={result.data || []}
      initialPagination={result.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      }}
      initialSearchParams={resolvedSearchParams}
    />
  );
}

function TeamsListSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
      ))}
    </div>
  );
}

export default function TeamsPage({ searchParams }: TeamsPageProps) {
  return (
    <Suspense fallback={<TeamsListSkeleton />}>
      <TeamsListServer searchParams={searchParams} />
    </Suspense>
  );
}