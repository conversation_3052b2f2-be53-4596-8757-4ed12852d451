import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { BusinessRuleError } from '../errors/errors';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { SmsProvider, SendSmsParams, SmsMessage, SmsServiceConfig } from './sms/types';
// Import the default provider - this can be changed to use different providers
import { NetGsmProvider } from '@/lib/services/sms';

export interface SendSmsServiceParams {
  /** Sender identifier (optional if default is configured) */
  senderIdentifier?: string;
  /** Encoding parameter (optional if default is configured) */
  encoding?: string;
  /** Array of messages to send */
  messages: SmsMessage[];
  /** Optional metadata for tracking */
  metadata?: Record<string, any>;
}

export interface SmsServiceResult {
  /** Whether all messages were sent successfully */
  success: boolean;
  /** Number of messages sent successfully */
  sentCount: number;
  /** Number of messages that failed */
  failedCount: number;
  /** Total number of messages */
  totalCount: number;
  /** Array of message IDs for tracking */
  messageIds?: string[];
  /** Provider response data */
  providerData?: any;
}

/**
 * SMS Service
 * Handles SMS sending using the adapter pattern
 */
export class SmsService extends BaseService {
  private provider: SmsProvider;
  private config: SmsServiceConfig;

  constructor(config?: Partial<SmsServiceConfig>) {
    super('SmsService');
    
    // Initialize with default NetGSM provider
    // To use a different provider, just change this import and instantiation
    this.provider = config?.provider || new NetGsmProvider();
    
    this.config = {
      provider: this.provider,
      defaultSender: config?.defaultSender || process.env.SMS_DEFAULT_SENDER || '',
      defaultEncoding: config?.defaultEncoding || process.env.SMS_DEFAULT_ENCODING || 'TR',
      enabled: config?.enabled !== undefined ? config.enabled : process.env.SMS_ENABLED !== 'false',
      ...config
    };
  }

  /**
   * Send SMS messages
   */
  async sendSms(
    params: SendSmsServiceParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<SmsServiceResult>> {
    // Create validation functions
    const validationFunctions = this.createSmsValidationFunctions();

    return this.executeWithValidation(
      'sendSms',
      params,
      validationFunctions,
      async (validatedParams) => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Check if SMS service is enabled
        if (!this.config.enabled) {
          throw new BusinessRuleError(
            'sms_disabled',
            'SMS service is currently disabled'
          );
        }

        // Prepare SMS parameters with defaults
        const smsParams: SendSmsParams = {
          senderIdentifier: validatedParams.senderIdentifier || this.config.defaultSender || '',
          encoding: validatedParams.encoding || this.config.defaultEncoding || 'UTF-8',
          messages: validatedParams.messages
        };

        // Final validation
        if (!smsParams.senderIdentifier) {
          throw new BusinessRuleError(
            'missing_sender',
            'Sender identifier is required and no default is configured'
          );
        }

        // Send SMS using the provider
        const providerResponse = await this.provider.sendSms(smsParams);

        if (!providerResponse.success) {
          throw new BusinessRuleError(
            'sms_send_failed',
            `SMS sending failed: ${providerResponse.error}`,
            {
              userId: effectiveUserId ? String(effectiveUserId) : undefined,
              tenantId: effectiveTenantId || undefined,
              operation: 'sendSms',
              resource: 'sms',
              metadata: {
                providerError: providerResponse.error,
                providerErrorCode: providerResponse.errorCode,
                provider: this.provider.getProviderName()
              }
            }
          );
        }

        // Prepare service result
        const result: SmsServiceResult = {
          success: true,
          sentCount: validatedParams.messages.length,
          failedCount: 0,
          totalCount: validatedParams.messages.length,
          messageIds: providerResponse.messageIds,
          providerData: providerResponse.data
        };

        return result;
      },
      {
        userId,
        tenantId,
        resource: 'sms',
        metadata: {
          messageCount: params.messages?.length || 0,
          provider: this.provider.getProviderName(),
          ...params.metadata
        }
      }
    );
  }

  /**
   * Get SMS service status and configuration
   */
  async getServiceStatus(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<{
    enabled: boolean;
    provider: string;
    configured: boolean;
    defaultSender?: string;
    defaultEncoding?: string;
  }>> {
    return this.executeOperation(
      'getServiceStatus',
      async () => {
        const isConfigured = this.provider instanceof NetGsmProvider 
          ? (this.provider as NetGsmProvider).isConfigured()
          : true; // Assume other providers are configured

        return {
          enabled: this.config.enabled || false,
          provider: this.provider.getProviderName(),
          configured: isConfigured,
          defaultSender: this.config.defaultSender,
          defaultEncoding: this.config.defaultEncoding
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_status'
      }
    );
  }



  /**
   * Create validation functions for executeWithValidation
   */
  private createSmsValidationFunctions(): Array<(data: SendSmsServiceParams) => ValidationError | null> {
    return [
      // Messages array validation
      (data: SendSmsServiceParams) => {
        if (!data.messages || !Array.isArray(data.messages)) {
          return {
            field: 'messages',
            message: 'Messages must be an array',
            value: data.messages
          };
        }
        return null;
      },
      // Messages not empty validation
      (data: SendSmsServiceParams) => {
        if (data.messages && data.messages.length === 0) {
          return {
            field: 'messages',
            message: 'At least one message is required',
            value: data.messages
          };
        }
        return null;
      },
      // Individual message validation
      (data: SendSmsServiceParams) => {
        if (data.messages && Array.isArray(data.messages)) {
          for (let i = 0; i < data.messages.length; i++) {
            const message = data.messages[i];
            if (!message.receiver) {
              return {
                field: `messages[${i}].receiver`,
                message: 'Receiver phone number is required',
                value: message.receiver
              };
            }
            if (!message.message) {
              return {
                field: `messages[${i}].message`,
                message: 'Message content is required',
                value: message.message
              };
            }
            // Basic phone number validation
            if (message.receiver && !/^\+?[\d\s\-\(\)]+$/.test(message.receiver)) {
              return {
                field: `messages[${i}].receiver`,
                message: 'Invalid phone number format',
                value: message.receiver
              };
            }
          }
        }
        return null;
      }
    ];
  }

  /**
   * Update SMS service configuration
   */
  updateConfig(newConfig: Partial<SmsServiceConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig
    };

    if (newConfig.provider) {
      this.provider = newConfig.provider;
    }
  }

  /**
   * Get current provider name
   */
  getProviderName(): string {
    return this.provider.getProviderName();
  }
}
