import { eq, and } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class FacilitiesDB extends TenantAwareDBBase {
  
  static async getFacilities(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const facilities = await db.select().from(schema.facilities)
      .where(eq(schema.facilities.tenantId, filter.tenantId))
      .orderBy(schema.facilities.name);
    
    // Transform data to include computed capacity property for backward compatibility
    return facilities.map((facility: any) => ({
      ...facility,
      capacity: {
        total: facility.totalCapacity || 0,
        currentlyOccupied: facility.currentlyOccupied || 0,
        dimensions: facility.length && facility.width && facility.dimensionUnit ? {
          length: parseFloat(facility.length),
          width: parseFloat(facility.width),
          unit: facility.dimensionUnit
        } : undefined
      }
    }));
  }

  static async getFacilityById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.facilities)
      .where(and(
        eq(schema.facilities.id, id),
        eq(schema.facilities.tenantId, filter.tenantId)
      ));
    
    if (!result[0]) return null;
    
    // Transform data to include computed capacity property for backward compatibility
    const facility = result[0];
    return {
      ...facility,
      capacity: {
        total: facility.totalCapacity || 0,
        currentlyOccupied: facility.currentlyOccupied || 0,
        dimensions: facility.length && facility.width && facility.dimensionUnit ? {
          length: parseFloat(facility.length),
          width: parseFloat(facility.width),
          unit: facility.dimensionUnit
        } : undefined
      }
    };
  }

  static async getFacilityByName(name: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const facility = await db
      .select()
      .from(schema.facilities)
      .where(and(
        eq(schema.facilities.name, name),
        eq(schema.facilities.tenantId, filter.tenantId)
      ))
      .limit(1);

    return facility[0] || null;
  }

  static async getFacilitySchedules(facilityId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.trainingSchedules.id,
      teamId: schema.trainingSchedules.teamId,
      dayOfWeek: schema.trainingSchedules.dayOfWeek,
      startTime: schema.trainingSchedules.startTime,
      endTime: schema.trainingSchedules.endTime,
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      }
    })
    .from(schema.trainingSchedules)
    .innerJoin(schema.teams, eq(schema.trainingSchedules.teamId, schema.teams.id))
    .where(and(
      eq(schema.trainingSchedules.facilityId, facilityId),
      eq(schema.trainingSchedules.tenantId, filter.tenantId)
    ))
    .orderBy(schema.trainingSchedules.dayOfWeek, schema.trainingSchedules.startTime);
  }

  static async getTrainingSchedulesByFacilityId(facilityId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const schedules = await db
      .select()
      .from(schema.trainingSchedules)
      .where(and(
        eq(schema.trainingSchedules.facilityId, facilityId),
        eq(schema.trainingSchedules.tenantId, filter.tenantId)
      ));

    return schedules;
  }

  static async getAllFacilitySchedules(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      facilityId: schema.trainingSchedules.facilityId,
      facilityName: schema.facilities.name,
      scheduleId: schema.trainingSchedules.id,
      teamId: schema.trainingSchedules.teamId,
      teamName: schema.teams.name,
      dayOfWeek: schema.trainingSchedules.dayOfWeek,
      startTime: schema.trainingSchedules.startTime,
      endTime: schema.trainingSchedules.endTime,
    })
    .from(schema.trainingSchedules)
    .innerJoin(schema.teams, eq(schema.trainingSchedules.teamId, schema.teams.id))
    .innerJoin(schema.facilities, eq(schema.trainingSchedules.facilityId, schema.facilities.id))
    .where(eq(schema.trainingSchedules.tenantId, filter.tenantId))
    .orderBy(
      schema.facilities.name,
      schema.trainingSchedules.dayOfWeek, 
      schema.trainingSchedules.startTime
    );
  }

  static async createFacility(
    data: Omit<typeof schema.facilities.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.facilities, data, tenantId, userId);
  }

  static async updateFacility(
    id: string, 
    data: Partial<typeof schema.facilities.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.facilities, id, data, tenantId, userId);
  }

  static async deleteFacility(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.facilities, id, tenantId);
  }
}
