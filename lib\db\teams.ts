import { eq, and, like, ilike, count, desc, asc, or, inArray } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class TeamsDB extends TenantAwareDBBase {
  
  static async getTeamsPaginated(
    tenantId?: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: {
        name?: string;
        description?: string;
        schoolName?: string;
        branchName?: string;
        instructorName?: string;
      };
    } = { page: 1, limit: 10 }
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const { page, limit, search, sortBy = 'createdAt', sortOrder = 'desc', filters = {} } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(schema.teams.tenantId, filter.tenantId)];

    // Add search condition
    if (search) {
      whereConditions.push(
        or(
          ilike(schema.teams.name, `%${search}%`),
          ilike(schema.teams.description, `%${search}%`),
          ilike(schema.schools.name, `%${search}%`),
          ilike(schema.branches.name, `%${search}%`),
          ilike(schema.instructors.name, `%${search}%`),
          ilike(schema.instructors.surname, `%${search}%`)
        )!
      );
    }

    // Add column-based filters
    if (filters.name) {
      whereConditions.push(ilike(schema.teams.name, `%${filters.name}%`));
    }
    if (filters.description) {
      whereConditions.push(ilike(schema.teams.description, `%${filters.description}%`));
    }
    if (filters.schoolName) {
      whereConditions.push(ilike(schema.schools.name, `%${filters.schoolName}%`));
    }
    if (filters.branchName) {
      whereConditions.push(ilike(schema.branches.name, `%${filters.branchName}%`));
    }
    if (filters.instructorName) {
      whereConditions.push(
        or(
          ilike(schema.instructors.name, `%${filters.instructorName}%`),
          ilike(schema.instructors.surname, `%${filters.instructorName}%`)
        )!
      );
    }

    const whereClause = whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0];

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(schema.teams)
      .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
      .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
      .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    // Determine sort column and order
    let orderByClause;
    switch (sortBy) {
      case 'name':
        orderByClause = sortOrder === 'asc' ? asc(schema.teams.name) : desc(schema.teams.name);
        break;
      case 'description':
        orderByClause = sortOrder === 'asc' ? asc(schema.teams.description) : desc(schema.teams.description);
        break;
      case 'schoolName':
        orderByClause = sortOrder === 'asc' ? asc(schema.schools.name) : desc(schema.schools.name);
        break;
      case 'branchName':
        orderByClause = sortOrder === 'asc' ? asc(schema.branches.name) : desc(schema.branches.name);
        break;
      case 'instructorName':
        orderByClause = sortOrder === 'asc' ? asc(schema.instructors.name) : desc(schema.instructors.name);
        break;
      case 'createdAt':
      default:
        orderByClause = sortOrder === 'asc' ? asc(schema.teams.createdAt) : desc(schema.teams.createdAt);
        break;
    }

    // First, get the paginated team IDs
    const teamIds = await db
      .select({ id: schema.teams.id })
      .from(schema.teams)
      .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
      .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
      .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    if (teamIds.length === 0) {
      return {
        data: [],
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };
    }

    // Then get the full team data with relationships for the paginated IDs
    const result = await db.select({
      id: schema.teams.id,
      name: schema.teams.name,
      description: schema.teams.description,
      schoolId: schema.teams.schoolId,
      branchId: schema.teams.branchId,
      instructorId: schema.teams.instructorId,
      tenantId: schema.teams.tenantId,
      createdAt: schema.teams.createdAt,
      updatedAt: schema.teams.updatedAt,
      createdBy: schema.teams.createdBy,
      updatedBy: schema.teams.updatedBy,
      // Branch info
      branch: {
        id: schema.branches.id,
        name: schema.branches.name,
        description: schema.branches.description,
      },
      // School info
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      },
      // Instructor info  
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
    })
    .from(schema.teams)
    .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
    .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
    .where(
      inArray(schema.teams.id, teamIds.map(t => t.id))
    )
    .orderBy(orderByClause);

    // Get training schedules for teams
    let trainingSchedules: any[] = [];
    if (result.length > 0) {
      trainingSchedules = await db.select({
        id: schema.trainingSchedules.id,
        teamId: schema.trainingSchedules.teamId,
        dayOfWeek: schema.trainingSchedules.dayOfWeek,
        startTime: schema.trainingSchedules.startTime,
        endTime: schema.trainingSchedules.endTime,
        facilityId: schema.trainingSchedules.facilityId,
      })
      .from(schema.trainingSchedules)
      .where(inArray(schema.trainingSchedules.teamId, result.map(t => t.id)));
    }

    // Get athlete counts for teams
    let athleteCounts: any[] = [];
    if (result.length > 0) {
      athleteCounts = await db.select({
        teamId: schema.athleteTeams.teamId,
        athleteId: schema.athleteTeams.athleteId,
      })
      .from(schema.athleteTeams)
      .where(inArray(schema.athleteTeams.teamId, result.map(t => t.id)));
    }

    // Combine the data
    const teams = result.map((team: any) => ({
      ...team,
      trainingSchedule: trainingSchedules.filter(ts => ts.teamId === team.id),
      athletes: athleteCounts.filter(ac => ac.teamId === team.id).map(ac => ac.athleteId),
    }));

    return {
      data: teams,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  static async getTeams(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.teams.id,
      name: schema.teams.name,
      description: schema.teams.description,
      schoolId: schema.teams.schoolId,
      branchId: schema.teams.branchId,
      instructorId: schema.teams.instructorId,
      tenantId: schema.teams.tenantId,
      createdAt: schema.teams.createdAt,
      updatedAt: schema.teams.updatedAt,
      createdBy: schema.teams.createdBy,
      updatedBy: schema.teams.updatedBy,
      // Branch info
      branch: {
        id: schema.branches.id,
        name: schema.branches.name,
        description: schema.branches.description,
      },
      // School info
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      },
      // Instructor info  
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
    })
    .from(schema.teams)
    .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
    .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
    .where(eq(schema.teams.tenantId, filter.tenantId));

    // Get training schedules for each team
    const teamIds = result.map((team: any) => team.id);
    let trainingSchedules: any[] = [];
    if (teamIds.length > 0) {
      trainingSchedules = await db.select({
        id: schema.trainingSchedules.id,
        teamId: schema.trainingSchedules.teamId,
        dayOfWeek: schema.trainingSchedules.dayOfWeek,
        startTime: schema.trainingSchedules.startTime,
        endTime: schema.trainingSchedules.endTime,
        facilityId: schema.trainingSchedules.facilityId,
      })
      .from(schema.trainingSchedules)
      .where(inArray(schema.trainingSchedules.teamId, teamIds));
    }

    // Get athlete counts for each team
    let athleteCounts: any[] = [];
    if (teamIds.length > 0) {
      athleteCounts = await db.select({
        teamId: schema.athleteTeams.teamId,
        athleteId: schema.athleteTeams.athleteId,
      })
      .from(schema.athleteTeams)
      .where(inArray(schema.athleteTeams.teamId, teamIds));
    }

    // Combine the data
    return result.map((team: any) => ({
      ...team,
      trainingSchedule: trainingSchedules.filter(ts => ts.teamId === team.id),
      athletes: athleteCounts.filter(ac => ac.teamId === team.id).map(ac => ac.athleteId),
    }));
  }

  static async getTeamById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.teams.id,
      name: schema.teams.name,
      description: schema.teams.description,
      schoolId: schema.teams.schoolId,
      branchId: schema.teams.branchId,
      instructorId: schema.teams.instructorId,
      tenantId: schema.teams.tenantId,
      createdAt: schema.teams.createdAt,
      updatedAt: schema.teams.updatedAt,
      createdBy: schema.teams.createdBy,
      updatedBy: schema.teams.updatedBy,
      // Branch info
      branch: {
        id: schema.branches.id,
        name: schema.branches.name,
        description: schema.branches.description,
      },
      // School info
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      },
      // Instructor info  
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
    })
    .from(schema.teams)
    .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
    .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
    .where(and(
      eq(schema.teams.id, id),
      eq(schema.teams.tenantId, filter.tenantId)
    ));

    if (result.length === 0) {
      return null;
    }

    const team = result[0];

    // Get training schedules for this team with facility information
    const trainingSchedules = await db.select({
      id: schema.trainingSchedules.id,
      teamId: schema.trainingSchedules.teamId,
      dayOfWeek: schema.trainingSchedules.dayOfWeek,
      startTime: schema.trainingSchedules.startTime,
      endTime: schema.trainingSchedules.endTime,
      facilityId: schema.trainingSchedules.facilityId,
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
      },
    })
    .from(schema.trainingSchedules)
    .leftJoin(schema.facilities, eq(schema.trainingSchedules.facilityId, schema.facilities.id))
    .where(eq(schema.trainingSchedules.teamId, team.id));

    // Get athletes for this team
    const athleteTeams = await db.select({
      athleteId: schema.athleteTeams.athleteId,
    })
    .from(schema.athleteTeams)
    .where(eq(schema.athleteTeams.teamId, team.id));

    // Combine the data
    return {
      ...team,
      trainingSchedule: trainingSchedules,
      athletes: athleteTeams.map((at: any) => at.athleteId),
    };
  }

  static async getTeamByNameAndSchool(name: string, schoolId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select()
      .from(schema.teams)
      .where(and(
        eq(schema.teams.name, name),
        eq(schema.teams.schoolId, schoolId),
        eq(schema.teams.tenantId, filter.tenantId)
      ));
    
    return result[0] || null;
  }

  static async createTeam(
    data: Omit<typeof schema.teams.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.teams, data, tenantId, userId);
  }

  static async createTeamWithSchedules(
    teamData: Omit<typeof schema.teams.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>,
    scheduleData: Omit<typeof schema.trainingSchedules.$inferInsert, 'teamId' | 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>[],
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);

    return await db.transaction(async (tx: any) => {
      // Create team
      const teamResult = await tx.insert(schema.teams).values({
        ...teamData,
        ...filter,
        ...auditInfo,
      } as any).returning();

      const team = teamResult[0];

      // Create training schedules
      if (scheduleData.length > 0) {
        const schedules = scheduleData.map(schedule => ({
          ...schedule,
          teamId: team.id,
          ...filter,
          ...auditInfo,
        }));

        await tx.insert(schema.trainingSchedules).values(schedules as any);
      }

      return team;
    });
  }

  static async updateTeam(
    id: string, 
    data: Partial<typeof schema.teams.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.teams, id, data, tenantId, userId);
  }

  static async deleteTeam(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.teams, id, tenantId);
  }

  static async deleteTrainingSchedulesByTeamId(teamId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    await db.delete(schema.trainingSchedules)
      .where(and(
        eq(schema.trainingSchedules.teamId, teamId),
        eq(schema.trainingSchedules.tenantId, filter.tenantId)
      ));
  }

  static async updateTeamWithSchedules(
    teamId: string,
    teamData: Partial<typeof schema.teams.$inferInsert>,
    scheduleData: Omit<typeof schema.trainingSchedules.$inferInsert, 'teamId' | 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>[],
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    const updateAuditInfo = await this.getUpdateAuditInfo(userId);

    return await db.transaction(async (tx: any) => {
      // Update team
      await tx.update(schema.teams)
        .set({
          ...teamData,
          ...updateAuditInfo,
        } as any)
        .where(and(
          eq(schema.teams.id, teamId),
          eq(schema.teams.tenantId, filter.tenantId)
        ));

      // Delete existing training schedules
      await tx.delete(schema.trainingSchedules)
        .where(and(
          eq(schema.trainingSchedules.teamId, teamId),
          eq(schema.trainingSchedules.tenantId, filter.tenantId)
        ));

      // Create new training schedules
      if (scheduleData.length > 0) {
        const schedules = scheduleData.map(schedule => ({
          ...schedule,
          teamId: teamId,
          ...filter,
          ...auditInfo,
        }));

        await tx.insert(schema.trainingSchedules).values(schedules as any);
      }

      return teamId;
    });
  }

  static async getTrainingSchedulesByTeamId(teamId: string, tenantId?: string) {
    return db.select({
      id: schema.trainingSchedules.id,
      teamId: schema.trainingSchedules.teamId,
      dayOfWeek: schema.trainingSchedules.dayOfWeek,
      startTime: schema.trainingSchedules.startTime,
      endTime: schema.trainingSchedules.endTime,
      facilityId: schema.trainingSchedules.facilityId,
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
      },
    })
    .from(schema.trainingSchedules)
    .leftJoin(schema.facilities, eq(schema.trainingSchedules.facilityId, schema.facilities.id))
    .where(eq(schema.trainingSchedules.teamId, teamId));
  }
}
