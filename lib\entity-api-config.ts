/**
 * Configuration for entity API endpoints
 * This file helps developers integrate actual API calls for breadcrumb entity names
 */

export interface EntityApiConfig {
  endpoint: string;
  method: 'GET' | 'POST';
  nameField: string; // The field in the response that contains the entity name
  headers?: Record<string, string>;
}

export const ENTITY_API_CONFIG: Record<string, EntityApiConfig> = {
  items: {
    endpoint: '/api/items/:id',
    method: 'GET',
    nameField: 'name',
  },
  teams: {
    endpoint: '/api/teams/:id',
    method: 'GET',
    nameField: 'name',
  },
  facilities: {
    endpoint: '/api/facilities/:id',
    method: 'GET',
    nameField: 'name',
  },
  schools: {
    endpoint: '/api/schools/:id',
    method: 'GET',
    nameField: 'name',
  },
  athletes: {
    endpoint: '/api/athletes/:id',
    method: 'GET',
    nameField: 'fullName', // or could be 'firstName' + 'lastName'
  },
  instructors: {
    endpoint: '/api/instructors/:id',
    method: 'GET',
    nameField: 'fullName', // or could be 'firstName' + 'lastName'
  },
  payments: {
    endpoint: '/api/payments/:id',
    method: 'GET',
    nameField: 'description', // or a computed field like 'amount' + 'date'
  },
  expenses: {
    endpoint: '/api/expenses/:id',
    method: 'GET',
    nameField: 'description',
  },
};

/**
 * Helper function to fetch entity name from API
 * Replace the switch statement in useEntityNames with this function when APIs are ready
 */
export async function fetchEntityNameFromApi(
  entityType: string, 
  entityId: string
): Promise<string> {
  const config = ENTITY_API_CONFIG[entityType];
  
  if (!config) {
    throw new Error(`No API configuration found for entity type: ${entityType}`);
  }

  const url = config.endpoint.replace(':id', entityId);
  
  try {
    const response = await fetch(url, {
      method: config.method,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Extract the name field from the response
    const entityName = data[config.nameField];
    
    if (!entityName) {
      throw new Error(`Name field '${config.nameField}' not found in API response`);
    }

    return entityName;
  } catch (error) {
    console.error(`Failed to fetch ${entityType} name from API:`, error);
    throw error;
  }
}

/**
 * Example of how to integrate this into useEntityNames hook:
 * 
 * In hooks/use-entity-names.ts, replace the switch statement with:
 * 
 * try {
 *   entityName = await fetchEntityNameFromApi(entityType, entityId);
 * } catch (error) {
 *   // Fallback to default naming
 *   entityName = `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} #${entityId.slice(0, 8)}`;
 * }
 */
