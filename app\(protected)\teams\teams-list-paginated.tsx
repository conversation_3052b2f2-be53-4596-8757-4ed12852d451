"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Filter } from "lucide-react";
import Link from "next/link";
import { Team } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useTeamColumns } from "@/components/teams-table-columns";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaginationData, SearchParams } from "@/hooks/use-paginated-list";

interface TeamsListPaginatedProps {
  initialData: Team[];
  initialPagination: PaginationData;
  initialSearchParams: SearchParams & {
    name?: string;
    description?: string;
    schoolName?: string;
    branchName?: string;
    instructorName?: string;
  };
}

export function TeamsListPaginated({
  initialData,
  initialPagination,
  initialSearchParams
}: TeamsListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const columns = useTeamColumns();

  // Local state for filters
  const [filters, setFilters] = useState({
    name: initialSearchParams.name || "",
    description: initialSearchParams.description || "",
    schoolName: initialSearchParams.schoolName || "",
    branchName: initialSearchParams.branchName || "",
    instructorName: initialSearchParams.instructorName || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.name || initialSearchParams.description ||
       initialSearchParams.schoolName || initialSearchParams.branchName ||
       initialSearchParams.instructorName)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/teams?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      name: "",
      description: "",
      schoolName: "",
      branchName: "",
      instructorName: "",
    });

    // Clear URL parameters
    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter parameters but keep search and pagination
    ['name', 'description', 'schoolName', 'branchName', 'instructorName'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/teams?${searchParams.toString()}`);
  };

  // Create actions for the header
  const actions = (
    <Button asChild>
      <Link href="/teams/new">
        <PlusCircle className="mr-2 h-4 w-4" />
        {t('teams.actions.addNew')}
      </Link>
    </Button>
  );

  // Create filters component
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('teams.details.name')}</Label>
              <Input
                id="name"
                value={filters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                placeholder={t('teams.placeholders.enterName')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t('teams.details.description')}</Label>
              <Input
                id="description"
                value={filters.description}
                onChange={(e) => handleFilterChange('description', e.target.value)}
                placeholder={t('teams.placeholders.enterDescription')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="schoolName">{t('teams.details.school')}</Label>
              <Input
                id="schoolName"
                value={filters.schoolName}
                onChange={(e) => handleFilterChange('schoolName', e.target.value)}
                placeholder={t('teams.placeholders.enterSchoolName')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={columns}
      title={t('teams.title')}
      description={t('teams.messages.manageTeams')}
      basePath="/teams"
      initialSearchParams={initialSearchParams}
      actions={actions}
      filters={filtersComponent}
      searchPlaceholder={t('teams.placeholders.searchTeams')}
      paginationOptions={{
        defaultSortBy: 'createdAt',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}
