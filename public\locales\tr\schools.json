{"schools": {"title": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON> Düzenle", "details": {"name": "Okul Adı", "logo": "Okul Logosu", "address": "<PERSON><PERSON>", "phone": "Telefon Numarası", "email": "E-posta", "foundedYear": "Kuruluş Yılı", "information": "Okul Bilgileri", "branches": "Branşlar", "instructors": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "actions": {"viewDetails": "Detayları Görüntüle", "addNew": "<PERSON><PERSON> ok<PERSON>", "backToSchools": "<PERSON><PERSON><PERSON>", "saveChanges": "Değişiklikleri Kaydet", "createSchool": "Okul Oluştur", "deleteConfirmTitle": "<PERSON>ul Sil", "deleteConfirmMessage": "{{name}} okulunu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}, "messages": {"noLogo": "Logo yok", "instructorCount": "{{count}} Eğitmen", "instructorCount_other": "{{count}} Eğitmen", "updateError": "Okul güncellenirken hata oluştu", "updateSuccess": "Okul başarıyla gü<PERSON>llendi", "deleteSuccess": "<PERSON><PERSON> baş<PERSON><PERSON><PERSON>", "deleteSuccessDescription": "Okul sistemden kaldırıldı", "deleteError": "Okul silinirken hata oluş<PERSON>", "createSuccess": "Okul başarıyla oluşturuldu", "createError": "Okul oluşturulurken hata oluştu", "branchLoadError": "Branşlar yüklenirken hata oluştu", "noBranchesAvailable": "Kullanılabilir branş yok"}, "placeholders": {"enterName": "Okul adını girin", "enterAddress": "Sokak adresini girin", "enterPhone": "Telefon numarasını girin", "enterEmail": "<EMAIL>", "uploadImage": "Yüklemek için tıklayın veya sürükleyip bırakın", "imageFormats": "PNG, JPG veya WEBP (MAKS. 5MB)", "changeImage": "<PERSON><PERSON><PERSON>", "newSchoolDescription": "Yeni okul için de<PERSON>rı girin.", "selectBranches": "Bu okulun sunduğu branşları seçin", "noBranchesSelected": "Hiçbir branş seçilmedi"}}}