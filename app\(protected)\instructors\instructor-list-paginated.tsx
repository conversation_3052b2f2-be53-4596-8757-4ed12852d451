"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { PlusCircle, Filter } from "lucide-react";
import Link from "next/link";
import { Instructor } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useInstructorColumns } from "./columns";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaginationData, SearchParams } from "@/hooks/use-paginated-list";

interface InstructorListPaginatedProps {
  initialData: Instructor[];
  initialPagination: PaginationData;
  initialSearchParams: SearchParams & {
    name?: string;
    surname?: string;
    email?: string;
    phone?: string;
  };
}

export function InstructorListPaginated({
  initialData,
  initialPagination,
  initialSearchParams
}: InstructorListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const columns = useInstructorColumns();

  // Local state for filters
  const [filters, setFilters] = useState({
    name: initialSearchParams.name || "",
    surname: initialSearchParams.surname || "",
    email: initialSearchParams.email || "",
    phone: initialSearchParams.phone || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.name || initialSearchParams.surname ||
       initialSearchParams.email || initialSearchParams.phone)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/instructors?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      name: "",
      surname: "",
      email: "",
      phone: "",
    });

    // Clear URL parameters
    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter parameters but keep search and pagination
    ['name', 'surname', 'email', 'phone'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/instructors?${searchParams.toString()}`);
  };

  // Create actions for the header
  const actions = (
    <Button asChild>
      <Link href="/instructors/new">
        <PlusCircle className="mr-2 h-4 w-4" />
        {t('instructors.actions.addNew')}
      </Link>
    </Button>
  );

  // Create filters component
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('instructors.details.name')}</Label>
              <Input
                id="name"
                value={filters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                placeholder={t('instructors.details.firstName')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="surname">{t('instructors.details.surname')}</Label>
              <Input
                id="surname"
                value={filters.surname}
                onChange={(e) => handleFilterChange('surname', e.target.value)}
                placeholder={t('instructors.details.lastName')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('instructors.details.email')}</Label>
              <Input
                id="email"
                value={filters.email}
                onChange={(e) => handleFilterChange('email', e.target.value)}
                placeholder={t('instructors.details.email')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={columns}
      title={t('instructors.title')}
      description={t('instructors.messages.manageInstructors')}
      basePath="/instructors"
      initialSearchParams={initialSearchParams}
      actions={actions}
      filters={filtersComponent}
      searchPlaceholder={t('instructors.placeholders.searchInstructors')}
      paginationOptions={{
        defaultSortBy: 'createdAt',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}
