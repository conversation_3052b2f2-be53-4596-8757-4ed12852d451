import { getOverdueAthletes, getFinancialSummary } from "@/lib/actions";
import DashboardClient from "./dashboard-client";

export default async function DashboardPage() {
  // Fetch all data on the server
  const [financialData, overdueAthletes] = await Promise.all([
    getFinancialSummary(),
    getOverdueAthletes()
  ]);

  return (
    <DashboardClient 
      financialData={financialData}
      overdueAthletes={overdueAthletes}
    />
  );
}