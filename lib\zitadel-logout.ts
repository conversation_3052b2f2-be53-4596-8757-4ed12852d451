import { signOut } from "next-auth/react";

/**
 * Securely logs out the user by calling the server-side logout endpoint
 * This function will:
 * 1. Call the secure server-side logout API
 * 2. Clear the NextAuth session
 * 3. Redirect to Zitadel's logout endpoint for complete OIDC logout
 */
export async function logoutFromZitadel() {
  try {
    // Call the secure server-side logout endpoint
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Logout request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success || !data.logoutUrl) {
      throw new Error('Invalid logout response from server');
    }

    // Clear NextAuth session (without redirect to prevent auto-redirect to dashboard)
    await signOut({ redirect: false });
    
    // Redirect to the secure logout URL provided by the server
    window.location.href = data.logoutUrl;
    
  } catch (error) {
    console.error('Error during secure logout:', error);
    
    // Fallback: try to clear NextAuth session and redirect to sign-in
    try {
      await signOut({ redirect: false });
    } catch (signOutError) {
      console.error('Error during fallback signOut:', signOutError);
    }
    
    // Final fallback: redirect to sign-in page
    window.location.href = '/auth/signin';
  }
}
