/**
 * Test file for multi-payment plan proration calculation
 * Run this to verify the calculations work correctly for multiple plans
 */

import { calculateProratedAmount, getRemainingDaysInMonth, getTotalDaysInMonth } from './lib/proration-utils';

// Test scenario: Multiple payment plans for July 18, 2025
const testDate = new Date(2025, 6, 18); // July 18, 2025
const paymentPlans = [
  { name: "Swimming Plan", monthlyValue: 500 },
  { name: "Basketball Plan", monthlyValue: 400 },
  { name: "Tennis Plan", monthlyValue: 300 }
];

console.log('=== Multi-Payment Plan Proration Test ===\n');
console.log(`Test Date: July 18, 2025`);
console.log(`Total days in July: ${getTotalDaysInMonth(testDate)}`);
console.log(`Remaining days from July 18: ${getRemainingDaysInMonth(testDate)}`);
console.log('');

let totalProrated = 0;

paymentPlans.forEach((plan, index) => {
  const proratedAmount = calculateProratedAmount(plan.monthlyValue, testDate);
  totalProrated += proratedAmount;
  
  console.log(`Payment Plan ${index + 1}: ${plan.name}`);
  console.log(`  Monthly Amount: ${plan.monthlyValue} TL`);
  console.log(`  Prorated Amount: ${proratedAmount} TL`);
  console.log('');
});

console.log(`Total Combined Prorated Amount: ${totalProrated.toFixed(2)} TL`);
console.log('');

// Verify calculation manually
const remainingDays = getRemainingDaysInMonth(testDate);
const totalDays = getTotalDaysInMonth(testDate);
const totalMonthly = paymentPlans.reduce((sum, plan) => sum + plan.monthlyValue, 0);
const expectedTotal = (totalMonthly / totalDays) * remainingDays;

console.log('Manual Verification:');
console.log(`Total Monthly: ${totalMonthly} TL`);
console.log(`Daily Rate: ${(totalMonthly / totalDays).toFixed(4)} TL/day`);
console.log(`Expected Total: ${expectedTotal.toFixed(2)} TL`);
console.log(`Calculated Total: ${totalProrated.toFixed(2)} TL`);
console.log(`Match: ${Math.abs(expectedTotal - totalProrated) < 0.01 ? '✅ YES' : '❌ NO'}`);
