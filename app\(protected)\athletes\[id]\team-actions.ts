"use server";

import { getServerTenantId, getServerUserId } from "@/lib/tenant-utils-server";
import { getTeams } from "@/lib/db/actions/teams";
import { getAvailablePaymentPlansForTeam, getAthleteTeamPaymentPlans, removeAthleteFromTeamWithCleanup } from "@/lib/db/actions/athlete-team-management";

/**
 * Server action to get available teams for athlete assignment
 * Following architecture: Client → Server Action → DB Layer
 */
export async function getAvailableTeamsAction() {
  try {
    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const teams = await getTeams();

    return {
      success: true,
      data: teams
    };
  } catch (error) {
    console.error('Error in getAvailableTeamsAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch teams'
    };
  }
}

/**
 * Server action to get payment plans for a specific team/branch
 * Following architecture: Client → Server Action → DB Layer
 */
export async function getTeamPaymentPlansForAthleteAction(branchId: string) {
  try {
    // Validate input
    if (!branchId?.trim()) {
      return {
        success: false,
        error: 'Branch ID is required'
      };
    }

    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const paymentPlans = await getAvailablePaymentPlansForTeam(branchId);

    return {
      success: true,
      data: paymentPlans
    };
  } catch (error) {
    console.error('Error in getTeamPaymentPlansForAthleteAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch payment plans'
    };
  }
}

/**
 * Server action to get athlete team payment plans
 * Following architecture: Client → Server Action → DB Layer
 */
export async function getAthleteTeamPaymentPlansAction(athleteId: string, teamId: string) {
  try {
    // Validate inputs
    if (!athleteId?.trim() || !teamId?.trim()) {
      return {
        success: false,
        error: 'Athlete ID and Team ID are required'
      };
    }

    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const paymentPlans = await getAthleteTeamPaymentPlans(athleteId, teamId);

    return {
      success: true,
      data: paymentPlans
    };
  } catch (error) {
    console.error('Error in getAthleteTeamPaymentPlansAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch athlete payment plans'
    };
  }
}

/**
 * Server action to remove athlete from team with cleanup
 * Following architecture: Client → Server Action → DB Layer
 */
export async function removeAthleteFromTeamAction(params: {
  athleteId: string;
  teamId: string;
  removePaymentPlans?: boolean;
}) {
  try {
    // Validate inputs
    if (!params.athleteId?.trim() || !params.teamId?.trim()) {
      return {
        success: false,
        error: 'Athlete ID and Team ID are required'
      };
    }

    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer through service
    const result = await removeAthleteFromTeamWithCleanup({
      athleteId: params.athleteId,
      teamId: params.teamId,
      removePaymentPlans: params.removePaymentPlans || false
    });

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Error in removeAthleteFromTeamAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove athlete from team'
    };
  }
}
