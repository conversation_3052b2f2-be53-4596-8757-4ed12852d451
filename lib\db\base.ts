import { db } from '@/src/db';
import { eq, and } from 'drizzle-orm';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';

/**
 * Base class for tenant-aware database operations
 * Provides common functionality for tenant filtering and audit trails
 */
export abstract class TenantAwareDBBase {
  protected static async getTenantFilter(tenantId?: string) {
    try {
      const currentTenantId = tenantId || await getServerTenantId();
      if (!currentTenantId) {
        // During build time or when no tenant context is available,
        // return a special marker that will never match any real data
        return { tenantId: '__BUILD_TIME__' };
      }
      return { tenantId: currentTenantId };
    } catch (error) {
      // During build time, getServerTenantId() will throw because headers() is not available
      // Return the build time marker to allow static generation
      return { tenantId: '__BUILD_TIME__' };
    }
  }

  protected static async getAuditInfo(userId?: bigint) {
    const currentUserId = userId || await getServerUserId();
    if (!currentUserId) {
      throw new Error('User ID is required for audit operations');
    }
    return {
      createdBy: currentUserId,
      updatedBy: currentUserId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  protected static async getUpdateAuditInfo(userId?: bigint) {
    const currentUserId = userId || await getServerUserId();
    if (!currentUserId) {
      throw new Error('User ID is required for audit operations');
    }
    return {
      updatedBy: currentUserId,
      updatedAt: new Date(),
    };
  }

  // Generic insert method with automatic tenant and audit trail
  protected static async insertWithAudit<T extends { tenantId: string; createdBy: bigint; updatedBy: bigint }>(
    table: any,
    data: Omit<T, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    const insertData = {
      ...data,
      ...filter,
      ...auditInfo,
    } as any;

    const result = await db.insert(table).values(insertData).returning();
    return (result as any[])[0];
  }

  // Generic update method with automatic audit trail
  protected static async updateWithAudit<T>(
    table: any,
    id: string,
    data: Partial<T>,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getUpdateAuditInfo(userId);
    
    const updateData = {
      ...data,
      ...auditInfo,
    };

    const result = await db.update(table)
      .set(updateData)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ))
      .returning();
    
    return result[0] as T;
  }

  // Generic delete method with tenant filtering
  protected static async deleteWithTenantFilter(
    table: any,
    id: string,
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    
    const result = await db.delete(table)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ))
      .returning();
    
    return (result as any[])[0];
  }

  // Protected database instance for subclasses
  protected static get database() {
    return db;
  }
}
