import { db } from '@/src/db';
import { eq, and, count } from 'drizzle-orm';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';

/**
 * Base class for tenant-aware database operations
 * Provides common functionality for tenant filtering and audit trails
 */
export abstract class TenantAwareDBBase {
  protected static async getTenantFilter(tenantId?: string) {
    try {
      const currentTenantId = tenantId || await getServerTenantId();
      if (!currentTenantId) {
        // During build time or when no tenant context is available,
        // return a special marker that will never match any real data
        return { tenantId: '__BUILD_TIME__' };
      }
      return { tenantId: currentTenantId };
    } catch (error) {
      // During build time, getServerTenantId() will throw because headers() is not available
      // Return the build time marker to allow static generation
      return { tenantId: '__BUILD_TIME__' };
    }
  }

  protected static async getAuditInfo(userId?: bigint) {
    const currentUserId = userId || await getServerUserId();
    if (!currentUserId) {
      throw new Error('User ID is required for audit operations');
    }
    return {
      createdBy: currentUserId,
      updatedBy: currentUserId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  protected static async getUpdateAuditInfo(userId?: bigint) {
    const currentUserId = userId || await getServerUserId();
    if (!currentUserId) {
      throw new Error('User ID is required for audit operations');
    }
    return {
      updatedBy: currentUserId,
      updatedAt: new Date(),
    };
  }

  // Generic insert method with automatic tenant and audit trail
  protected static async insertWithAudit<T extends { tenantId: string; createdBy: bigint; updatedBy: bigint }>(
    table: any,
    data: Omit<T, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    const insertData = {
      ...data,
      ...filter,
      ...auditInfo,
    } as any;

    const result = await db.insert(table).values(insertData).returning();
    return (result as any[])[0];
  }

  // Generic update method with automatic audit trail
  protected static async updateWithAudit<T>(
    table: any,
    id: string,
    data: Partial<T>,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getUpdateAuditInfo(userId);
    
    const updateData = {
      ...data,
      ...auditInfo,
    };

    const result = await db.update(table)
      .set(updateData)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ))
      .returning();
    
    return result[0] as T;
  }

  // Generic delete method with tenant filtering
  protected static async deleteWithTenantFilter(
    table: any,
    id: string,
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    
    const result = await db.delete(table)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ))
      .returning();
    
    return (result as any[])[0];
  }

  // Protected database instance for subclasses
  protected static get database() {
    return db;
  }

  // Generic get all method with tenant filtering
  protected static async getAllWithTenantFilter<T>(
    table: any,
    tenantId?: string,
    orderBy?: any
  ): Promise<T[]> {
    const filter = await this.getTenantFilter(tenantId);

    if (orderBy) {
      return db.select().from(table)
        .where(eq(table.tenantId, filter.tenantId))
        .orderBy(orderBy) as Promise<T[]>;
    } else {
      return db.select().from(table)
        .where(eq(table.tenantId, filter.tenantId)) as Promise<T[]>;
    }
  }

  // Generic get by ID method with tenant filtering
  protected static async getByIdWithTenantFilter<T>(
    table: any,
    id: string,
    tenantId?: string
  ): Promise<T | null> {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(table)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ));

    return (result[0] as T) || null;
  }

  // Generic get by name method with tenant filtering
  protected static async getByNameWithTenantFilter<T>(
    table: any,
    name: string,
    tenantId?: string
  ): Promise<T | null> {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(table)
      .where(and(
        eq(table.name, name),
        eq(table.tenantId, filter.tenantId)
      ));

    return (result[0] as T) || null;
  }

  // Generic count method with tenant filtering
  protected static async countWithTenantFilter(
    table: any,
    tenantId?: string,
    additionalConditions?: any
  ): Promise<number> {
    const filter = await this.getTenantFilter(tenantId);
    const baseCondition = eq(table.tenantId, filter.tenantId);

    const whereCondition = additionalConditions
      ? and(baseCondition, additionalConditions)
      : baseCondition;

    const result = await db.select({ count: count() }).from(table)
      .where(whereCondition);

    return result[0]?.count || 0;
  }

  // Generic exists check method with tenant filtering
  protected static async existsWithTenantFilter(
    table: any,
    id: string,
    tenantId?: string
  ): Promise<boolean> {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({ id: table.id }).from(table)
      .where(and(
        eq(table.id, id),
        eq(table.tenantId, filter.tenantId)
      ))
      .limit(1);

    return result.length > 0;
  }
}
