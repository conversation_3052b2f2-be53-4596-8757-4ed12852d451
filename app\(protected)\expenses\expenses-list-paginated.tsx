"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useSearchP<PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, Search, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Expense } from "@/lib/types";
import { format } from "date-fns";
import { MoreHorizontal, Pencil, Trash, Eye } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { deleteExpense } from "@/lib/db/actions/expenses";
import { toast } from "sonner";

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface ExpensesListPaginatedProps {
  initialData: Expense[];
  initialPagination: PaginationData;
  initialSearchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    category?: string;
    fromDate?: string;
    toDate?: string;
    instructorId?: string;
    facilityId?: string;
  };
}

const ExpenseActionsCell = ({ expense }: { expense: Expense }) => {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteExpense(expense.id);
      toast.success(t('expenses.messages.deleteSuccess'));
      router.refresh();
    } catch (error) {
      console.error('Error deleting expense:', error);
      toast.error(t('expenses.messages.deleteError'));
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t("expenses.actions.openMenu")}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("common.actionsHeader")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/expenses/${expense.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            {t("expenses.actions.view")}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/expenses/${expense.id}/edit`} className="flex items-center">
            <Pencil className="mr-2 h-4 w-4" />
            {t("expenses.actions.edit")}
          </Link>
        </DropdownMenuItem>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onSelect={(e) => e.preventDefault()}
            >
              <Trash className="mr-2 h-4 w-4" />
              {t("expenses.actions.delete")}
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t('expenses.deleteDialog.title')}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t('expenses.deleteDialog.description')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>
                {t('common.actions.cancel')}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting 
                  ? t('common.actions.deleting') 
                  : t('common.actions.delete')
                }
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export function ExpensesListPaginated({ 
  initialData, 
  initialPagination, 
  initialSearchParams 
}: ExpensesListPaginatedProps) {
  const { t } = useSafeTranslation();
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [expenses, setExpenses] = useState<Expense[]>(initialData);
  const [loading] = useState(false);

  // Update expenses when initialData changes (when server component re-renders)
  useEffect(() => {
    console.log('Client received data:', {
      dataLength: initialData.length,
      pagination: initialPagination
    });
    
    // Force the component to use the full dataset received from the server
    setExpenses([...initialData]);
  }, [initialData, initialPagination]);

  // Read URL params for server-side values (read-only)
  const currentSearchParams = searchParams;
  const search = currentSearchParams.get('search') || "";
  const sortBy = currentSearchParams.get('sortBy') || "date";
  const sortOrder = (currentSearchParams.get('sortOrder') as 'asc' | 'desc') || "desc";
  const currentLimit = currentSearchParams.get('limit') || "10";
  
  // Local state for UI interactions - initialize once from URL, never sync back
  const [localSearch, setLocalSearch] = useState(() => initialSearchParams.search || "");
  const [filters, setFilters] = useState(() => ({
    category: initialSearchParams.category || "",
    fromDate: initialSearchParams.fromDate || "",
    toDate: initialSearchParams.toDate || "",
    instructorId: initialSearchParams.instructorId || "",
    facilityId: initialSearchParams.facilityId || "",
  }));
  const [showFilters, setShowFilters] = useState(() => 
    !!(initialSearchParams.category || initialSearchParams.fromDate || 
       initialSearchParams.toDate || initialSearchParams.instructorId || 
       initialSearchParams.facilityId)
  );

  // Calculate if search is valid (empty or has at least 3 characters)
  const isSearchValid = useMemo(() => {
    return localSearch.trim().length === 0 || localSearch.trim().length >= 3;
  }, [localSearch]);

  // Function to update URL with new search params
  const updateURL = useCallback((newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove undefined values and update params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Only reset to page 1 when filters/search/sort/limit change, but NOT when page is explicitly being changed
    const shouldResetPage = !('page' in newParams);
    if (shouldResetPage) {
      params.set('page', '1');
    }

    const finalURL = `/expenses?${params.toString()}`;
    router.push(finalURL);
  }, [router, searchParams]);

  // Track the previous search value to only trigger on actual changes
  const [prevLocalSearch, setPrevLocalSearch] = useState(localSearch);
  
  useEffect(() => {
    // Only trigger if localSearch actually changed (not just component re-render)
    if (localSearch !== prevLocalSearch) {
      setPrevLocalSearch(localSearch);
      
      const shouldTriggerSearch = localSearch.trim().length === 0 || localSearch.trim().length >= 3;
      
      if (shouldTriggerSearch) {
        const timeoutId = setTimeout(() => {
          updateURL({ search: localSearch.trim() || undefined });
        }, 300); // 300ms debounce
        
        return () => clearTimeout(timeoutId);
      }
    }
  }, [localSearch, prevLocalSearch, updateURL]);

  const handlePageChange = (newPage: number) => {
    updateURL({ page: newPage.toString() });
  };

  const handleLimitChange = (newLimit: string) => {
    updateURL({ limit: newLimit });
  };

  const handleSearch = () => {
    // Manual search - trigger immediate search
    updateURL({ search: localSearch.trim() || undefined });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(filters)
        .filter(([_, value]) => value.trim() !== "")
        .map(([key, value]) => [key, value === 'all' ? '' : value])
    );
    updateURL({
      category: cleanFilters.category || undefined,
      fromDate: cleanFilters.fromDate || undefined,
      toDate: cleanFilters.toDate || undefined,
      instructorId: cleanFilters.instructorId || undefined,
      facilityId: cleanFilters.facilityId || undefined,
    });
  };

  const handleClearFilters = () => {
    setFilters({
      category: "",
      fromDate: "",
      toDate: "",
      instructorId: "",
      facilityId: "",
    });
    updateURL({
      search: undefined,
      category: undefined,
      fromDate: undefined,
      toDate: undefined,
      instructorId: undefined,
      facilityId: undefined,
    });
  };

  const handleSortChange = (field: string) => {
    const newSortOrder = sortBy === field ? (sortOrder === 'asc' ? 'desc' : 'asc') : 'asc';
    updateURL({ 
      sortBy: field, 
      sortOrder: newSortOrder 
    });
  };

  const handleSortOrderChange = (newSortOrder: 'asc' | 'desc') => {
    updateURL({ sortOrder: newSortOrder });
  };

  // Create expense columns
  const expenseColumns = [
    {
      accessorKey: "date",
      header: t("expenses.details.date"),
      cell: ({ row }: { row: { original: Expense } }) => format(new Date(row.original.date), "dd/MM/yyyy"),
    },
    {
      accessorKey: "amount",
      header: t("expenses.details.amount"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="font-medium">{parseFloat(row.original.amount).toFixed(2)} {t('common.currency')}</div>
      ),
    },
    {
      accessorKey: "category",
      header: t("expenses.details.category"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="capitalize">{t(`expenses.categories.${row.original.category}`)}</div>
      ),
    },
    {
      accessorKey: "description",
      header: t("expenses.details.description"),
    },
    {
      accessorKey: "instructor",
      header: t("expenses.details.instructor"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="text-sm">
          {row.original.instructor ? `${row.original.instructor.name} ${row.original.instructor.surname}` : "-"}
        </div>
      ),
    },
    {
      accessorKey: "facility",
      header: t("expenses.details.facility"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="text-sm">
          {row.original.facility ? row.original.facility.name : "-"}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }: { row: { original: Expense } }) => (
        <ExpenseActionsCell expense={row.original} />
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t("expenses.title")}</CardTitle>
            <Button asChild>
              <Link href="/expenses/new">
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("expenses.new")}
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            {/* Search Bar */}
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Input
                  placeholder={t('expenses.placeholders.searchExpenses')}
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className={!isSearchValid ? "border-yellow-500 focus:border-yellow-500" : ""}
                />
                {localSearch.trim().length > 0 && localSearch.trim().length < 3 && (
                  <div className="absolute top-full left-0 mt-1 text-xs text-yellow-600 dark:text-yellow-400">
                    {t('expenses.placeholders.searchMinChars')}
                  </div>
                )}
              </div>
              <Button 
                onClick={handleSearch} 
                variant="outline"
                disabled={!isSearchValid}
              >
                <Search className="h-4 w-4 mr-2" />
                {t('common.actions.search')}
              </Button>
              <Button 
                onClick={() => setShowFilters(!showFilters)} 
                variant="outline"
              >
                <Filter className="h-4 w-4 mr-2" />
                {t('common.actions.filter')}
              </Button>
            </div>

            {/* Column Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
                <div className="space-y-2">
                  <Label htmlFor="categoryFilter">{t('expenses.details.category')}</Label>
                  <Select
                    value={filters.category}
                    onValueChange={(value) => handleFilterChange('category', value)}
                  >
                    <SelectTrigger id="categoryFilter">
                      <SelectValue placeholder={t('expenses.placeholders.filterByCategory')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t('common.all')}</SelectItem>
                      <SelectItem value="salary">{t('expenses.categories.salary')}</SelectItem>
                      <SelectItem value="insurance">{t('expenses.categories.insurance')}</SelectItem>
                      <SelectItem value="rent">{t('expenses.categories.rent')}</SelectItem>
                      <SelectItem value="equipment">{t('expenses.categories.equipment')}</SelectItem>
                      <SelectItem value="other">{t('expenses.categories.other')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fromDateFilter">{t('expenses.filters.fromDate')}</Label>
                  <Input
                    id="fromDateFilter"
                    type="date"
                    value={filters.fromDate}
                    onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="toDateFilter">{t('expenses.filters.toDate')}</Label>
                  <Input
                    id="toDateFilter"
                    type="date"
                    value={filters.toDate}
                    onChange={(e) => handleFilterChange('toDate', e.target.value)}
                  />
                </div>
                <div className="flex gap-2 md:col-span-2 lg:col-span-1">
                  <Button onClick={handleApplyFilters} size="sm" className="mt-8">
                    {t('common.actions.apply')}
                  </Button>
                  <Button onClick={handleClearFilters} variant="outline" size="sm" className="mt-8">
                    {t('common.actions.clear')}
                  </Button>
                </div>
              </div>
            )}

            {/* Sort and Page Size Controls */}
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center space-x-2">
                <Label>{t('common.sortBy')}:</Label>
                <Select 
                  value={sortBy} 
                  onValueChange={(field) => handleSortChange(field)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">{t('expenses.details.date')}</SelectItem>
                    <SelectItem value="amount">{t('expenses.details.amount')}</SelectItem>
                    <SelectItem value="category">{t('expenses.details.category')}</SelectItem>
                    <SelectItem value="description">{t('expenses.details.description')}</SelectItem>
                  </SelectContent>
                </Select>
                <Select 
                  value={sortOrder} 
                  onValueChange={(value) => handleSortOrderChange(value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">{t('common.ascending')}</SelectItem>
                    <SelectItem value="desc">{t('common.descending')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Label>{t('common.pageSize')}:</Label>
                <Select value={currentLimit} onValueChange={handleLimitChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Data Table */}
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
              ))}
            </div>
          ) : (
            <>
              <DataTable 
                columns={expenseColumns} 
                data={expenses}
                showPagination={false}
                initialPageSize={parseInt(currentLimit)}
              />
              
              {/* Debug info */}
              <div className="text-xs text-muted-foreground mt-2">
                Displaying {expenses.length} records (from total {initialPagination.total})
              </div>

              {/* Pagination Controls */}
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-muted-foreground">
                  {t('common.pagination.showing', {
                    start: initialPagination.total === 0 ? 0 : (initialPagination.page - 1) * initialPagination.limit + 1,
                    end: Math.min(initialPagination.page * initialPagination.limit, initialPagination.total),
                    total: initialPagination.total
                  })}
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(initialPagination.page - 1)}
                    disabled={!initialPagination.hasPreviousPage}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    {t('common.pagination.previous')}
                  </Button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, initialPagination.totalPages) }, (_, i) => {
                      let pageNum : number;
                      if (initialPagination.totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (initialPagination.page <= 3) {
                        pageNum = i + 1;
                      } else if (initialPagination.page >= initialPagination.totalPages - 2) {
                        pageNum = initialPagination.totalPages - 4 + i;
                      } else {
                        pageNum = initialPagination.page - 2 + i;
                      }
                      
                      return (
                        <Button
                          key={pageNum}
                          variant={initialPagination.page === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(initialPagination.page + 1)}
                    disabled={!initialPagination.hasNextPage}
                  >
                    {t('common.pagination.next')}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
