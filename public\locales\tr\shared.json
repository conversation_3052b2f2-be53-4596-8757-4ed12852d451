{"nav": {"dashboard": "Panel", "schools": "<PERSON><PERSON><PERSON>", "instructors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teams": "Takımlar", "athletes": "Sporcular", "payments": "Ö<PERSON>mel<PERSON>", "expenses": "<PERSON><PERSON><PERSON>", "facilities": "<PERSON><PERSON><PERSON>", "items": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "breadcrumbs": {"home": "<PERSON>", "dashboard": "Panel", "schools": "<PERSON><PERSON><PERSON>", "instructors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "teams": "Takımlar", "athletes": "Sporcular", "payments": "Ö<PERSON>mel<PERSON>", "expenses": "<PERSON><PERSON><PERSON>", "facilities": "<PERSON><PERSON><PERSON>", "items": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plans": "Planlar", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "details": "Detaylar", "sell": "Sat"}, "common": {"actionsHeader": "İşlemler", "details": {"description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "placeholders": {"enterDescription": "Açıklama girin"}, "all": "Tümü", "actions": {"title": "İşlemler", "create": "Oluştur", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "deleting": "Siliniyor...", "save": "<PERSON><PERSON>", "saving": "Kay<PERSON>ili<PERSON>r...", "cancel": "İptal", "back": "<PERSON><PERSON>", "confirm": "Emin misiniz?", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assign": "<PERSON>a", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "none": "Hiç<PERSON>i", "reset": "Sıfırla", "toggleMenu": "Menüyü aç/kapat", "signOut": "Çıkış yap", "checking": "<PERSON><PERSON><PERSON> edili<PERSON>...", "processing": "İşleniyor...", "add": "<PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON>", "removing": "Çıkarılıyor...", "search": "Ara", "filter": "Filtrele", "apply": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "filterBy": "{{field}} ile filtrele"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "suspended": "Askıya Alınmış"}, "loading": "Yükleniyor...", "dateCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noData": "<PERSON><PERSON> mev<PERSON> de<PERSON>", "error": "<PERSON>ir hata o<PERSON>", "success": "Başarılı", "optional": "İsteğe bağlı", "paymentPlan": "Ödeme Planı", "currency": "TL", "month": "ay", "unknown": "Bilinmiyor", "notAssigned": "Atanmamış", "noDescription": "Açıklama sağlanmadı", "dayOfMonth": ". g<PERSON><PERSON>", "generatedOn": "Oluşturma tarihi", "locale": "tr-TR", "messages": {"updateSuccess": "Başarıyla gü<PERSON>llendi", "updateError": "<PERSON><PERSON><PERSON><PERSON><PERSON> başarı<PERSON><PERSON>z", "deleteSuccess": "Başar<PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createSuccess": "Başarıyla oluşturuldu", "createError": "Oluşturma başarısız"}, "createdAt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarihi", "sortBy": "Sıralama", "order": "<PERSON><PERSON><PERSON>", "pageSize": "<PERSON><PERSON>", "page_size": "<PERSON><PERSON>", "ascending": "<PERSON><PERSON>", "descending": "<PERSON><PERSON><PERSON>", "search": "Ara", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "pagination_info": "{{total}} sonuçtan {{start}}-{{end}} gö<PERSON>iliyor", "page_of": "{{total}} sayfadan {{page}}. sayfa", "pagination": {"previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "showing": "{{total}} sonuçtan {{start}}-{{end}} gö<PERSON>iliyor"}, "upload": {"dragDrop": "Dosyaları buraya sürükleyip bırakın veya yüklemek için tıklayın", "maxSize": "<PERSON><PERSON><PERSON><PERSON> dosya boyutu: {{size}}", "formats": "Desteklenen formatlar: {{formats}}"}, "table": {"previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "noResults": "<PERSON><PERSON><PERSON> bulunamadı.", "searchPlaceholder": "Ara..."}, "months": {"january": "Ocak", "february": "Ş<PERSON><PERSON>", "march": "Mart", "april": "<PERSON><PERSON>", "may": "<PERSON><PERSON><PERSON>", "june": "Haziran", "july": "Temmuz", "august": "<PERSON><PERSON><PERSON><PERSON>", "september": "<PERSON><PERSON><PERSON><PERSON>", "october": "<PERSON><PERSON>", "november": "Kasım", "december": "Aralık"}, "validation": {"required": "<PERSON><PERSON> <PERSON><PERSON>", "minLength": "En az {{min}} karakter olmalıdır", "maxLength": "En fazla {{max}} karakter olmalıdır", "email": "Geçersiz e-posta adresi", "phoneMin": "Telefon numarası en az {{min}} karakter olmalıdır", "yearMin": "Yıl {{min}}'den sonra o<PERSON>ı<PERSON>", "yearMax": "<PERSON><PERSON><PERSON> gelecekte olamaz", "selectAtLeastOne": "Lütfen en az bir {{field}} se<PERSON><PERSON>", "maxFileSize": "<PERSON><PERSON><PERSON><PERSON> dosya boyutu {{size}}", "unsupportedFormat": "Yalnızca {{formats}} formatları desteklenmektedir"}, "fields": {"branch": "branş", "branches": "branş", "school": "okul", "schools": "okul"}, "branches": {"Football": "Futbol", "Basketball": "Basketbol", "Tennis": "<PERSON><PERSON>", "Swimming": "<PERSON><PERSON><PERSON><PERSON>", "Volleyball": "Voleybol", "Martial Arts": "Dövüş Sanatları"}}}