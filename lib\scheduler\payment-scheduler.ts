import * as cron from 'node-cron';
import { createPendingPaymentsForToday } from './payment-processor';

class PaymentScheduler {
  private static instance: PaymentScheduler;
  private task: cron.ScheduledTask | null = null;

  private constructor() {
    // Handle graceful shutdown
    process.on('SIGINT', () => this.handleShutdown('SIGINT'));
    process.on('SIGTERM', () => this.handleShutdown('SIGTERM'));
    process.on('SIGUSR2', () => this.handleShutdown('SIGUSR2')); // For nodemon restarts
  }

  static getInstance(): PaymentScheduler {
    if (!PaymentScheduler.instance) {
      PaymentScheduler.instance = new PaymentScheduler();
    }
    return PaymentScheduler.instance;
  }

  start() {
    const cronExpression = process.env.PAYMENT_CRON_SCHEDULE || '0 0 * * *'; // Default: midnight every day
    
    if (this.task) {
      console.log('⚠️  Payment scheduler is already running');
      return;
    }

    console.log(`📅 Starting payment scheduler with cron expression: ${cronExpression}`);
    console.log(`⚙️  Max retries: ${process.env.PAYMENT_SCHEDULER_MAX_RETRIES || '3'}`);
    console.log(`⏱️  Retry delay: ${(parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS || '1800000') / 1000 / 60)} minutes`);
    
    this.task = cron.schedule(cronExpression, async () => {
      console.log(`🔄 [${new Date().toISOString()}] Running payment scheduler...`);
      
      // TODO: Implement Redis-based lock mechanism to prevent multiple instances from running simultaneously
      // For now, this runs on a single instance as requested
      
      try {
        await this.processPayments();
      } catch (error) {
        console.error('❌ Error in payment scheduler:', error);
      }
    });

    console.log('✅ Payment scheduler started successfully');
  }

  stop() {
    if (this.task) {
      this.task.destroy();
      this.task = null;
      console.log('🛑 Payment scheduler stopped');
    }
  }

  private handleShutdown(signal: string) {
    console.log(`📥 Received ${signal}, shutting down payment scheduler...`);
    this.stop();
  }

  private async processPayments() {
    let retryCount = 0;
    const maxRetries = process.env.PAYMENT_SCHEDULER_MAX_RETRIES ? parseInt(process.env.PAYMENT_SCHEDULER_MAX_RETRIES) : 3;
    const retryDelayMs = process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS ? parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS) : 30 * 60 * 1000; // 30 minutes default

    while (retryCount <= maxRetries) {
      try {
        console.log(`💰 Processing payments (attempt ${retryCount + 1}/${maxRetries + 1})...`);
        const result = await createPendingPaymentsForToday();
        
        console.log(`✅ Payment processing completed successfully:`, {
          totalAssignments: result.totalAssignments,
          paymentsCreated: result.paymentsCreated,
          errors: result.errors?.length || 0,
          timestamp: new Date().toISOString()
        });
        
        // If successful, break out of retry loop
        break;
        
      } catch (error) {
        retryCount++;
        console.error(`❌ Payment processing failed (attempt ${retryCount}/${maxRetries + 1}):`, error);
        
        if (retryCount <= maxRetries) {
          console.log(`⏳ Retrying in ${retryDelayMs / 1000 / 60} minutes...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        } else {
          console.error('💥 Payment processing failed after all retries');
        }
      }
    }
  }
}

export default PaymentScheduler;
