import * as cron from 'node-cron';
import { createPendingPaymentsForToday } from './payment-processor';
import { runUpdatePaymentStatus} from '../payment-scheduler';

class PaymentScheduler {
  private static instance: PaymentScheduler;
  private paymentCreationTask: cron.ScheduledTask | null = null;
  private paymentStatusTask: cron.ScheduledTask | null = null;

  private constructor() {
    // Handle graceful shutdown
    process.on('SIGINT', () => this.handleShutdown('SIGINT'));
    process.on('SIGTERM', () => this.handleShutdown('SIGTERM'));
    process.on('SIGUSR2', () => this.handleShutdown('SIGUSR2')); // For nodemon restarts
  }

  static getInstance(): PaymentScheduler {
    if (!PaymentScheduler.instance) {
      PaymentScheduler.instance = new PaymentScheduler();
    }
    return PaymentScheduler.instance;
  }

  start() {
    // Payment Creation Task - runs at midnight every day
    const paymentCreationCron = process.env.PAYMENT_CREATION_CRON_SCHEDULE || '0 0 * * *'; // Default: midnight every day

    // Payment Status Update Task - runs every hour to check for overdue payments
    const paymentStatusCron = process.env.PAYMENT_STATUS_CRON_SCHEDULE || '0 0 * * *'; // Default: midnight every day

    if (this.paymentCreationTask || this.paymentStatusTask) {
      console.log('⚠️  Payment scheduler is already running');
      return;
    }

    console.log(`📅 Starting payment creation scheduler with cron expression: ${paymentCreationCron}`);
    console.log(`📅 Starting payment status scheduler with cron expression: ${paymentStatusCron}`);
    console.log(`⚙️  Max retries: ${process.env.PAYMENT_SCHEDULER_MAX_RETRIES || '3'}`);
    console.log(`⏱️  Retry delay: ${(parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS || '1800000') / 1000 / 60)} minutes`);

    // Payment Creation Task
    this.paymentCreationTask = cron.schedule(paymentCreationCron, async () => {
      console.log(`🔄 [${new Date().toISOString()}] Running payment creation scheduler...`);

      try {
        await this.processPaymentCreation();
      } catch (error) {
        console.error('❌ Error in payment creation scheduler:', error);
      }
    });

    // Payment Status Update Task
    this.paymentStatusTask = cron.schedule(paymentStatusCron, async () => {
      console.log(`🔄 [${new Date().toISOString()}] Running payment status update scheduler...`);

      try {
        await this.processPaymentStatusUpdates();
      } catch (error) {
        console.error('❌ Error in payment status update scheduler:', error);
      }
    });

    console.log('✅ Payment schedulers started successfully');
  }

  stop() {
    if (this.paymentCreationTask) {
      this.paymentCreationTask.destroy();
      this.paymentCreationTask = null;
      console.log('🛑 Payment creation scheduler stopped');
    }

    if (this.paymentStatusTask) {
      this.paymentStatusTask.destroy();
      this.paymentStatusTask = null;
      console.log('🛑 Payment status scheduler stopped');
    }
  }

  private handleShutdown(signal: string) {
    console.log(`📥 Received ${signal}, shutting down payment scheduler...`);
    this.stop();
  }

  private async processPaymentCreation() {
    let retryCount = 0;
    const maxRetries = process.env.PAYMENT_SCHEDULER_MAX_RETRIES ? parseInt(process.env.PAYMENT_SCHEDULER_MAX_RETRIES) : 3;
    const retryDelayMs = process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS ? parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS) : 30 * 60 * 1000; // 30 minutes default

    while (retryCount <= maxRetries) {
      try {
        console.log(`💰 Processing payments (attempt ${retryCount + 1}/${maxRetries + 1})...`);
        const result = await createPendingPaymentsForToday();
        
        console.log(`✅ Payment processing completed successfully:`, {
          totalAssignments: result.totalAssignments,
          paymentsCreated: result.paymentsCreated,
          errors: result.errors?.length || 0,
          timestamp: new Date().toISOString()
        });
        
        // If successful, break out of retry loop
        break;
        
      } catch (error) {
        retryCount++;
        console.error(`❌ Payment processing failed (attempt ${retryCount}/${maxRetries + 1}):`, error);
        
        if (retryCount <= maxRetries) {
          console.log(`⏳ Retrying in ${retryDelayMs / 1000 / 60} minutes...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        } else {
          console.error('💥 Payment processing failed after all retries');
        }
      }
    }
  }

  private async processPaymentStatusUpdates() {
    let retryCount = 0;
    const maxRetries = process.env.PAYMENT_SCHEDULER_MAX_RETRIES ? parseInt(process.env.PAYMENT_SCHEDULER_MAX_RETRIES) : 3;
    const retryDelayMs = process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS ? parseInt(process.env.PAYMENT_SCHEDULER_RETRY_DELAY_MS) : 30 * 60 * 1000; // 30 minutes default

    while (retryCount <= maxRetries) {
      try {
        console.log(`📊 Processing payment status updates (attempt ${retryCount + 1}/${maxRetries + 1})...`);
        const result = await runUpdatePaymentStatus();

        console.log(`✅ Payment status update completed successfully:`, {
          tenantsProcessed: result.tenantsProcessed,
          paymentsCreated: result.paymentsCreated,
          paymentsMarkedOverdue: result.paymentsMarkedOverdue,
          errors: result.errors?.length || 0,
          timestamp: new Date().toISOString()
        });

        // If successful, break out of retry loop
        break;

      } catch (error) {
        retryCount++;
        console.error(`❌ Payment status update failed (attempt ${retryCount}/${maxRetries + 1}):`, error);

        if (retryCount <= maxRetries) {
          console.log(`⏳ Retrying in ${retryDelayMs / 1000 / 60} minutes...`);
          await new Promise(resolve => setTimeout(resolve, retryDelayMs));
        } else {
          console.error('💥 Payment status update failed after all retry attempts');
          throw error;
        }
      }
    }

    const result = {
      success: true,
      timestamp: new Date().toISOString(),
      retryCount
    };

    return result;
  }
}

export default PaymentScheduler;
