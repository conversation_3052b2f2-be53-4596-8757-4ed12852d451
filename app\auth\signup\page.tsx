"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Users } from "lucide-react";
import { getBranches } from "@/lib/actions";
import { Branch } from "@/lib/types";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";

const formSchema = z.object({
  clubName: z.string().min(3, {
    message: "Club name must be at least 3 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  password: z.string().min(6, {
    message: "Password must be at least 6 characters.",
  }),
  confirmPassword: z.string(),
  branches: z.array(z.string()).min(1, {
    message: "Please select at least one branch.",
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

export default function SignUpPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const { t } = useTranslation(['shared', 'auth']);

  useEffect(() => {
    const loadBranches = async () => {
      try {
        const branchData = await getBranches();
        setBranches(branchData);
      } catch (error) {
        console.error("Failed to load branches:", error);
      }
    };
    
    loadBranches();
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      clubName: "",
      email: "",
      password: "",
      confirmPassword: "",
      branches: [],
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);
    
    // Simulate account creation
    setTimeout(() => {
      console.log(values);
      router.push("/dashboard");
    }, 1500);
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900 p-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8"
      >
        <div className="flex justify-center mb-8">
          <div className="relative">
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-orange-500 rounded-full blur opacity-75"></div>
            <div className="relative bg-white dark:bg-slate-800 rounded-full p-4">
              <div className="flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-orange-500 w-16 h-16">
                <Users className="h-8 w-8 text-white" />
              </div>
            </div>
          </div>
        </div>
        
        <h1 className="text-2xl font-bold text-center mb-2">{t('auth.signUp', { ns: 'auth' })}</h1>
        <p className="text-center text-muted-foreground mb-8">{t('auth.welcomeMessageSignup', { ns: 'auth' })}</p>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="clubName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('auth.clubName', { ns: 'auth' })}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('auth.clubNamePlaceholder', { ns: 'auth' })} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('auth.email', { ns: 'auth' })}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('auth.emailPlaceholder', { ns: 'auth' })} type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('auth.password', { ns: 'auth' })}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('auth.passwordPlaceholder', { ns: 'auth' })} type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('auth.confirmPassword', { ns: 'auth' })}</FormLabel>
                  <FormControl>
                    <Input placeholder={t('auth.confirmPasswordPlaceholder', { ns: 'auth' })} type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="branches"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">{t('auth.selectBranches', { ns: 'auth' })}</FormLabel>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {branches
                      .filter(branch => branch.id !== null)
                      .map((branch) => (
                      <FormField
                        key={branch.id}
                        control={form.control}
                        name="branches"
                        render={({ field }) => {
                          return (
                            <FormItem
                              key={branch.id}
                              className="flex items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(branch.id!)}
                                  onCheckedChange={(checked) => {
                                    return checked
                                      ? field.onChange([...field.value, branch.id!])
                                      : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== branch.id!
                                          )
                                        );
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="text-sm font-normal">
                                {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                              </FormLabel>
                            </FormItem>
                          );
                        }}
                      />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? t('common.loading', { ns: 'shared' }) : t('auth.signUp', { ns: 'auth' })}
            </Button>
          </form>
        </Form>
        
        <div className="text-center mt-6">
          <p className="text-sm text-muted-foreground">
            {t('auth.hasAccount', { ns: 'auth' })}{" "}
            <Link href="/auth/signin" className="text-primary hover:underline">
              {t('auth.signIn', { ns: 'auth' })}
            </Link>
          </p>
        </div>
      </motion.div>
    </div>
  );
}