import { eq, and, desc, count, sql, or, ilike, inArray } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class PaymentsDB extends TenantAwareDBBase {
  
  static async getPayments(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      method: schema.payments.method,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      updatedAt: schema.payments.updatedAt,
      createdBy: schema.payments.createdBy,
      updatedBy: schema.payments.updated<PERSON>y,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
      }
    })
    .from(schema.payments)
    .leftJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .where(eq(schema.payments.tenantId, filter.tenantId))
    .orderBy(desc(schema.payments.date));
  }

  static async getPaymentsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'date',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const offset = (page - 1) * limit;

    // Build WHERE conditions
    const conditions = [eq(schema.payments.tenantId, filter.tenantId)];

    // Add search condition
    if (search && search.trim()) {
      const searchTerm = `%${search.trim().toLowerCase()}%`;
      conditions.push(
        or(
          ilike(schema.athletes.name, searchTerm),
          ilike(schema.athletes.surname, searchTerm),
          ilike(schema.athletes.parentEmail, searchTerm),
          ilike(sql`COALESCE(${schema.payments.description}, '')`, searchTerm),
          ilike(sql`CAST(${schema.payments.amount} AS TEXT)`, searchTerm)
        )!
      );
    }

    // Add filters
    if (filters.athleteName) {
      conditions.push(ilike(schema.athletes.name, `%${filters.athleteName}%`));
    }
    if (filters.athleteSurname) {
      conditions.push(ilike(schema.athletes.surname, `%${filters.athleteSurname}%`));
    }
    if (filters.status) {
      conditions.push(eq(schema.payments.status, filters.status as any));
    }
    if (filters.type) {
      conditions.push(eq(schema.payments.type, filters.type as any));
    }
    if (filters.amount) {
      conditions.push(ilike(sql`CAST(${schema.payments.amount} AS TEXT)`, `%${filters.amount}%`));
    }
    if (filters.fromDate) {
      conditions.push(sql`${schema.payments.date} >= ${filters.fromDate}`);
    }
    if (filters.toDate) {
      conditions.push(sql`${schema.payments.date} <= ${filters.toDate}`);
    }

    const whereClause = and(...conditions);

    // Build ORDER BY clause
    let orderByClause;
    switch (sortBy) {
      case 'athleteName':
        orderByClause = sortOrder === 'asc' ? schema.athletes.name : desc(schema.athletes.name);
        break;
      case 'athleteSurname':
        orderByClause = sortOrder === 'asc' ? schema.athletes.surname : desc(schema.athletes.surname);
        break;
      case 'amount':
        orderByClause = sortOrder === 'asc' ? schema.payments.amount : desc(schema.payments.amount);
        break;
      case 'dueDate':
        orderByClause = sortOrder === 'asc' ? schema.payments.dueDate : desc(schema.payments.dueDate);
        break;
      case 'status':
        orderByClause = sortOrder === 'asc' ? schema.payments.status : desc(schema.payments.status);
        break;
      case 'type':
        orderByClause = sortOrder === 'asc' ? schema.payments.type : desc(schema.payments.type);
        break;
      case 'createdAt':
        orderByClause = sortOrder === 'asc' ? schema.payments.createdAt : desc(schema.payments.createdAt);
        break;
      case 'date':
      default:
        orderByClause = sortOrder === 'asc' ? schema.payments.date : desc(schema.payments.date);
        break;
    }

    // Get total count
    const totalResult = await db.select({ count: count() })
      .from(schema.payments)
      .leftJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    
    // For large limits that should show all data, set a higher value
    const queryLimit = limit >= 20 ? 100 : limit;
    
    const payments = await db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      method: schema.payments.method,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      updatedAt: schema.payments.updatedAt,
      createdBy: schema.payments.createdBy,
      updatedBy: schema.payments.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentEmail: schema.athletes.parentEmail,
      }
    })
    .from(schema.payments)
    .leftJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .where(whereClause)
    .orderBy(orderByClause)
    .limit(queryLimit)
    .offset(offset);
    

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: payments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }

  static async getPaymentById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      method: schema.payments.method,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      updatedAt: schema.payments.updatedAt,
      createdBy: schema.payments.createdBy,
      updatedBy: schema.payments.updatedBy,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentEmail: schema.athletes.parentEmail,
        parentPhone: schema.athletes.parentPhone,
        nationalId: schema.athletes.nationalId,
      },
      paymentPlan: {
        id: schema.paymentPlans.id,
        name: schema.paymentPlans.name,
        monthlyValue: schema.paymentPlans.monthlyValue,
        assignDay: schema.paymentPlans.assignDay,
        dueDay: schema.paymentPlans.dueDay,
        status: schema.paymentPlans.status,
        description: schema.paymentPlans.description,
      }
    })
    .from(schema.payments)
    .leftJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .leftJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .leftJoin(schema.paymentPlans, eq(schema.athletePaymentPlans.planId, schema.paymentPlans.id))
    .where(and(
      eq(schema.payments.id, id),
      eq(schema.payments.tenantId, filter.tenantId)
    ));
    
    return result[0] || null;
  }

  static async getPaymentPlans(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get payment plans
    const plans = await db.select().from(schema.paymentPlans)
      .where(eq(schema.paymentPlans.tenantId, filter.tenantId))
      .orderBy(desc(schema.paymentPlans.createdAt));
    
    // Get branches for each plan
    const plansWithBranches = await Promise.all(
      plans.map(async (plan) => {
        const branchesResult = await db
          .select({
            id: schema.branches.id,
            name: schema.branches.name,
            description: schema.branches.description,
          })
          .from(schema.paymentPlanBranches)
          .innerJoin(schema.branches, eq(schema.paymentPlanBranches.branchId, schema.branches.id))
          .where(eq(schema.paymentPlanBranches.paymentPlanId, plan.id));

        return {
          ...plan,
          branches: branchesResult,
        };
      })
    );
    
    return plansWithBranches;
  }

  static async getPaymentPlanById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get payment plan
    const planResult = await db.select().from(schema.paymentPlans)
      .where(and(
        eq(schema.paymentPlans.id, id),
        eq(schema.paymentPlans.tenantId, filter.tenantId)
      ));
    
    const plan = planResult[0];
    if (!plan) return null;

    // Get associated branches
    const branchesResult = await db
      .select({
        id: schema.branches.id,
        name: schema.branches.name,
        description: schema.branches.description,
      })
      .from(schema.paymentPlanBranches)
      .innerJoin(schema.branches, eq(schema.paymentPlanBranches.branchId, schema.branches.id))
      .where(eq(schema.paymentPlanBranches.paymentPlanId, id));

    return {
      ...plan,
      branches: branchesResult,
    };
  }

  static async getPaymentPlanByName(name: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get payment plan
    const planResult = await db.select().from(schema.paymentPlans)
      .where(and(
        eq(schema.paymentPlans.name, name),
        eq(schema.paymentPlans.tenantId, filter.tenantId)
      ));
    
    const plan = planResult[0];
    if (!plan) return null;
    return plan;
  }

  static async checkPaymentPlanHasActiveAssignments(planId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.athletePaymentPlans)
      .where(and(
        eq(schema.athletePaymentPlans.planId, planId),
        eq(schema.athletePaymentPlans.isActive, true),
        eq(schema.athletePaymentPlans.tenantId, filter.tenantId)
      )).limit(1);
    return result.length > 0;
  }

  static async getCompletedPayments(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.payments)
      .where(and(
        eq(schema.payments.tenantId, filter.tenantId),
        eq(schema.payments.status, 'completed')
      ));
  }

  static async getOverduePayments(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.payments)
      .where(and(
        eq(schema.payments.tenantId, filter.tenantId),
        eq(schema.payments.status, 'overdue')
      ));
  }

  static async getPendingPaymentsWithAthleteDetails(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentPhone: schema.athletes.parentPhone,
        parentEmail: schema.athletes.parentEmail,
      },
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      },
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      }
    })
    .from(schema.payments)
    .innerJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .leftJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .leftJoin(schema.teams, eq(schema.athletePaymentPlans.teamId, schema.teams.id))
    .leftJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .where(and(
      eq(schema.payments.tenantId, filter.tenantId),
      eq(schema.payments.status, 'pending')
    ))
    .orderBy(schema.payments.dueDate);
  }

  static async getOverduePaymentsWithAthleteDetails(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentPhone: schema.athletes.parentPhone,
        parentEmail: schema.athletes.parentEmail,
      },
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      },
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      }
    })
    .from(schema.payments)
    .innerJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .leftJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .leftJoin(schema.teams, eq(schema.athletePaymentPlans.teamId, schema.teams.id))
    .leftJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .where(and(
      eq(schema.payments.tenantId, filter.tenantId),
      eq(schema.payments.status, 'overdue')
    ))
    .orderBy(schema.payments.dueDate);
  }

  static async getPaymentsByIdsWithAthleteDetails(paymentIds: string[], tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      athletePaymentPlanId: schema.payments.athletePaymentPlanId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      description: schema.payments.description,
      createdAt: schema.payments.createdAt,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
        parentName: schema.athletes.parentName,
        parentPhone: schema.athletes.parentPhone,
        parentEmail: schema.athletes.parentEmail,
      },
      team: {
        id: schema.teams.id,
        name: schema.teams.name,
      },
      school: {
        id: schema.schools.id,
        name: schema.schools.name,
      }
    })
    .from(schema.payments)
    .innerJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .leftJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .leftJoin(schema.teams, eq(schema.athletePaymentPlans.teamId, schema.teams.id))
    .leftJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .where(and(
      eq(schema.payments.tenantId, filter.tenantId),
      inArray(schema.payments.id, paymentIds)
    ))
    .orderBy(schema.payments.dueDate);
  }

  static async createPaymentPlan(
    data: Omit<typeof schema.paymentPlans.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.paymentPlans, data, tenantId, userId);
  }

  static async updatePaymentPlan(
    id: string, 
    data: Partial<typeof schema.paymentPlans.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.paymentPlans, id, data, tenantId, userId);
  }

  static async createPayment(
    data: Omit<typeof schema.payments.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.payments, data, tenantId, userId);
  }

  static async updatePayment(
    id: string, 
    data: Partial<typeof schema.payments.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.payments, id, data, tenantId, userId);
  }

  static async deletePayment(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    // Get payment details before deletion for balance update
    const paymentToDelete = await db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      status: schema.payments.status,
      amount: schema.payments.amount
    })
    .from(schema.payments)
    .where(and(
      eq(schema.payments.id, id),
      eq(schema.payments.tenantId, filter.tenantId)
    ))
    .limit(1);

    if (!paymentToDelete.length) {
      throw new Error(`Payment with ID ${id} not found`);
    }

    // Delete the payment
    const result = await db.delete(schema.payments)
      .where(and(
        eq(schema.payments.id, id),
        eq(schema.payments.tenantId, filter.tenantId)
      ))
      .returning();

    // Return both the deleted payment and the original payment details
    return {
      deletedPayment: result[0],
      paymentDetails: paymentToDelete[0]
    };
  }

  static async deletePaymentPlan(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.paymentPlans, id, tenantId);
  }

  static async getPaymentsByPlanId(planId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get payments through athlete payment plan assignments
    return db.select({
      id: schema.payments.id,
      athleteId: schema.payments.athleteId,
      amount: schema.payments.amount,
      date: schema.payments.date,
      dueDate: schema.payments.dueDate,
      status: schema.payments.status,
      type: schema.payments.type,
      description: schema.payments.description,
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
      }
    })
    .from(schema.payments)
    .innerJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .leftJoin(schema.athletes, eq(schema.payments.athleteId, schema.athletes.id))
    .where(and(
      eq(schema.athletePaymentPlans.planId, planId),
      eq(schema.payments.tenantId, filter.tenantId)
    ))
    .orderBy(desc(schema.payments.createdAt));
  }

  static async getPaymentPlanStats(planId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get payment stats through athlete payment plan assignments
    const stats = await db.select({
      totalPayments: schema.payments.id,
      totalAmount: schema.payments.amount,
      status: schema.payments.status,
    })
    .from(schema.payments)
    .innerJoin(schema.athletePaymentPlans, eq(schema.payments.athletePaymentPlanId, schema.athletePaymentPlans.id))
    .where(and(
      eq(schema.athletePaymentPlans.planId, planId),
      eq(schema.payments.tenantId, filter.tenantId)
    ));

    let totalPayments = 0;
    let totalRevenue = 0;
    let completedPayments = 0;
    let pendingPayments = 0;
    let overduePayments = 0;

    stats.forEach(payment => {
      totalPayments++;
      totalRevenue += parseFloat(payment.totalAmount);
      
      switch (payment.status) {
        case 'completed':
          completedPayments++;
          break;
        case 'pending':
          pendingPayments++;
          break;
        case 'overdue':
          overduePayments++;
          break;
      }
    });

    return {
      totalPayments,
      totalRevenue: totalRevenue.toString(),
      completedPayments,
      pendingPayments,
      overduePayments,
    };
  }

  static async updatePaymentPlanBranches(planId: string, branchIds: string[], tenantId?: string) {
    // Delete existing associations
    await db.delete(schema.paymentPlanBranches)
      .where(eq(schema.paymentPlanBranches.paymentPlanId, planId));

    // Insert new associations
    if (branchIds.length > 0) {
      const associations = branchIds.map(branchId => ({
        paymentPlanId: planId,
        branchId: branchId,
      }));

      await db.insert(schema.paymentPlanBranches).values(associations);
    }

    return true;
  }

}
