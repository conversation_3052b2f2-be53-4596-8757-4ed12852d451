{"athletes": {"title": "Athletes", "addAthlete": "Add Athlete", "new": "New Athlete", "edit": "Edit Athlete", "select": "Select athlete", "selectedAthlete": "Selected Athlete", "viewDetails": "View Athlete Details", "details": {"personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "name": "Name", "surname": "Surname", "nationalId": "National ID", "birthDate": "Birth Date", "registrationDate": "Registration Date", "parent": "Parent Information", "parentName": "Parent Name", "parentPhone": "Parent Phone", "parentEmail": "<PERSON><PERSON>", "parentAddress": "Address", "title": "Athlete Information", "fullName": "Full Name", "status": "Status", "balance": "Balance", "statusAndBalance": "Status & Balance"}, "editPage": {"title": "{{name}} - Edit Athlete"}, "form": {"athleteInfo": "Athlete Information", "name": "Name", "surname": "Surname", "nationalId": "National ID", "birthDate": "Birth Date", "selectBirthDate": "Select birth date", "registrationDate": "Registration Date", "selectRegistrationDate": "Select registration date", "parentInfo": "Parent Information", "parentName": "Parent Name", "parentSurname": "<PERSON><PERSON> Surname", "parentPhone": "Parent Phone", "parentEmail": "<PERSON><PERSON>", "parentAddress": "Parent Address", "paymentPlans": "Payment Plans", "paymentPlansDescription": "Payment plan assignments for athlete", "noTeamsForPaymentPlans": "This athlete is not on any teams yet.", "addToTeamFirst": "Add the athlete to a team first to assign payment plans.", "teamAssignments": "Team Assignments", "addTeamAssignment": "Add Team Assignment", "selectTeam": "Select team", "optionalPaymentPlan": "Payment Plan (Optional)", "selectPaymentPlan": "Select payment plan", "removeAssignment": "Remove Assignment", "initialBalance": "Initial Balance", "proratedBalance": "Automatically calculate and apply prorated balance for the remaining days of the month", "selectedPlan": "Selected Payment Plan", "selectedPlans": "Selected Payment Plans", "monthlyAmount": "Monthly Amount", "remainingDays": "Remaining Days", "calculatedAmount": "Calculated Prorated Amount", "totalCalculatedAmount": "Total Calculated Prorated Amount", "manualBalance": "Manual Initial Balance", "enterBalance": "Enter initial balance amount", "balanceHint": "Enter 0 or leave empty for no initial balance. This amount will be added to the athlete's account as a pending payment.", "proratedHint": "This amount is automatically calculated based on the remaining days in the current month.", "proratedAmountLabel": "Prorated Amount", "enterProratedAmount": "Enter prorated amount", "proratedEditHint": "You can modify the automatically calculated amount or enter a custom value."}, "actions": {"createAthlete": "Create Athlete", "creating": "Creating...", "editAthlete": "Edit Athlete", "deleteAthlete": "Delete Athlete", "activateAthlete": "Activate Athlete", "deactivateAthlete": "Deactivate Athlete", "activate": "Activate", "deactivate": "Deactivate", "openMenu": "Open menu", "viewDetails": "View Details", "addNew": "Add New", "importFromExcel": "Import from Excel"}, "messages": {"createSuccess": "Athlete created successfully", "createError": "Failed to create athlete", "updateSuccess": "Athlete updated successfully", "updateError": "Failed to update athlete", "deleteSuccess": "Athlete deleted successfully", "deleteError": "Failed to delete athlete", "activateSuccess": "Athlete activated successfully", "activateError": "Failed to activate athlete", "deactivateSuccess": "Athlete deactivated successfully", "deactivateError": "Failed to deactivate athlete", "loadDataError": "Failed to load data", "operationError": "Operation failed", "teamCount": "{{count}} team", "teamCount_other": "{{count}} teams", "noTeams": "No teams", "manageAthletes": "Manage your athletes and their information", "requiredFields": "Please fill in all required fields", "requiredParentFields": "Please fill in all required parent fields", "noTeamsAvailable": "No teams available", "allTeamsSelected": "All available teams have been selected", "duplicateTeams": "Each team can only be selected once"}, "management": {"joinTeam": "Join Team", "leaveTeam": "Leave Team", "joinTeamDescription": "Add {{athlete<PERSON><PERSON>}} to a team", "joinedAt": "Joined", "noAvailableTeams": "No available teams to join", "addedToTeamSuccess": "Successfully added to {{teamName}}", "removedFromTeamSuccess": "Successfully removed from {{teamName}}", "addToTeamError": "Failed to add athlete to team", "removeFromTeamError": "Failed to remove athlete from team"}, "table": {"name": "Name", "surname": "Surname", "nationalId": "National ID", "birthDate": "Birth Date", "parentName": "Parent Name", "parentSurname": "<PERSON><PERSON> Surname", "parentPhone": "Parent Phone", "parentEmail": "<PERSON><PERSON>", "parentAddress": "Parent Address", "teams": "Teams", "parent": "Parent", "balance": "Balance", "status": "Status", "actions": "Actions"}, "placeholders": {"firstName": "Enter first name", "lastName": "Enter last name", "nationalId": "Enter national ID", "parentName": "Enter parent name", "parentPhone": "Enter phone number", "parentEmail": "Enter email address", "parentAddress": "Enter address", "searchAthletes": "Search athletes...", "searchMinChars": "Please enter at least 3 characters to search"}, "teamAssignments": "Team Assignments", "addTeamAssignment": "Add Team", "noTeamAssignments": "No team assignments. You can add the athlete to teams later.", "teamAssignment": "Team Assignment", "initialBalance": "Initial Balance", "proratedBalance": "Automatically calculate and apply prorated balance for the remaining days of the month", "selectedPlan": "Selected Payment Plan", "selectedPlans": "Selected Payment Plans", "monthlyAmount": "Monthly Amount", "remainingDays": "Remaining Days", "calculatedAmount": "Calculated Prorated Amount", "totalCalculatedAmount": "Total Calculated Prorated Amount", "manualBalance": "Manual Initial Balance", "enterBalance": "Enter initial balance amount", "balanceHint": "Enter 0 or leave empty for no initial balance. This amount will be added to the athlete's account as a pending payment.", "proratedHint": "This amount is automatically calculated based on the remaining days in the current month.", "proratedAmountLabel": "Prorated Amount", "enterProratedAmount": "Enter prorated amount", "proratedEditHint": "You can modify the automatically calculated amount or enter a custom value.", "proratedLabel": "Prorated", "status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "deleteDialog": {"title": "Delete Athlete", "description": "Are you sure you want to delete this athlete? This action cannot be undone."}, "activateDialog": {"title": "Activate Athlete", "description": "Select teams and payment plans to assign to this athlete.", "selectTeams": "Select teams to assign:", "selectPaymentPlan": "Select payment plan (optional):", "noPaymentPlan": "No payment plan", "noAvailableTeams": "No available teams."}, "deactivateDialog": {"title": "Deactivate Athlete", "description": "When you deactivate this athlete, they will be removed from all teams and payment plans will be deactivated.", "teamsToRemove": "Teams to remove from:", "paymentPlansToRemove": "Payment plans to deactivate:", "noActiveAssignments": "No active team or payment plan assignments."}, "import": {"title": "Import Athletes from Excel", "description": "Upload an Excel file to import multiple athletes at once", "downloadTemplate": "Download Template", "uploadFile": "Upload Excel File", "selectFile": "Select Excel file", "processing": "Processing...", "success": "Athletes imported successfully", "error": "Failed to import athletes", "template": {"columns": {"name": "Name*", "surname": "Surname*", "nationalId": "National Id*", "birthdate": "Birthdate*", "registrationDate": "Registration Date*", "parentName": "Parent Name*", "parentSurname": "Parent Surname*", "parentPhone": "Parent Phone*", "parentEmail": "<PERSON><PERSON>", "currentBalance": "Current Balance", "currentBalanceLastPaymentDate": "Current Balance Last Payment Date", "status": "Status", "team1": "Team1", "team2": "Team2", "team3": "Team3", "paymentPlan1": "Payment Plan1", "paymentPlan2": "Payment Plan2", "paymentPlan3": "Payment Plan3"}}, "validation": {"requiredFields": "Missing required fields in row {{row}}", "invalidDate": "Invalid date format in row {{row}}", "invalidEmail": "Invalid email format in row {{row}}", "duplicateNationalId": "Duplicate National ID in row {{row}}", "teamNotFound": "Team not found in row {{row}}: {{team}}", "paymentPlanNotFound": "Payment plan not found in row {{row}}: {{plan}}"}, "results": {"processed": "Processed {{count}} rows", "created": "Created {{count}} athletes", "errors": "{{count}} errors occurred", "viewErrors": "View Errors", "row": "Row"}, "messages": {"templateDownloaded": "Template downloaded successfully", "invalidFileType": "Please select a valid Excel file (.xlsx or .xls)", "noFileSelected": "Please select a file to upload", "importInProgress": "Import in progress...", "importCompleted": "Import completed successfully"}}}, "proratedCalculation": {"refundTitle": "Prorated Refund Calculation", "chargeTitle": "Prorated Charge Calculation", "remainingDays": "Remaining days in month: {{days}} of {{total}}", "totalRefund": "Total Refund:", "totalCharge": "Total Charge:", "refundNote": "A credit will be added to the athlete's balance for unused days.", "chargeNote": "A prorated charge will be added for the remaining days of this month."}}