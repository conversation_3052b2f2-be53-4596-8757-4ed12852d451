import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Save, X } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface PaymentFormActionsProps {
  onSave: () => void;
  onCancel: () => void;
  onGoBack: () => void;
  isSaving: boolean;
  isValid: boolean;
}

export function PaymentFormActions({ 
  onSave, 
  onCancel, 
  onGoBack, 
  isSaving, 
  isValid 
}: PaymentFormActionsProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="flex flex-col sm:flex-row gap-3">
      <Button
        variant="ghost"
        onClick={onGoBack}
        className="flex items-center gap-2"
      >
        <ChevronLeft className="h-4 w-4" />
        {t("common.actions.back")}
      </Button>
      
      <div className="flex gap-3 ml-auto">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isSaving}
          className="flex items-center gap-2"
        >
          <X className="h-4 w-4" />
          {t("common.actions.cancel")}
        </Button>
        
        <Button
          onClick={onSave}
          disabled={isSaving || !isValid}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {isSaving ? t("common.actions.saving") : t("common.actions.save")}
        </Button>
      </div>
    </div>
  );
}
