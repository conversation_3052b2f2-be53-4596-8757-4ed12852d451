{"athletes": {"title": "Sporcular", "addAthlete": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON>", "selectedAthlete": "Seçilen Sporcu", "viewDetails": "Sporcu Detaylarını Görüntüle", "details": {"personalInfo": "<PERSON><PERSON><PERSON><PERSON>", "firstName": "Ad", "lastName": "Soyad", "name": "Ad", "surname": "Soyad", "nationalId": "TC Kimlik No", "birthDate": "<PERSON><PERSON><PERSON>", "registrationDate": "<PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON>", "parentName": "<PERSON><PERSON>", "parentPhone": "Veli Telefonu", "parentEmail": "Veli E-postası", "parentAddress": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "fullName": "Ad Soyad", "status": "Durum", "balance": "Bakiye", "statusAndBalance": "Durum ve Bakiye"}, "editPage": {"title": "{{name}} - <PERSON><PERSON><PERSON>"}, "form": {"athleteInfo": "<PERSON><PERSON><PERSON>", "name": "Ad", "surname": "Soyad", "nationalId": "TC Kimlik No", "birthDate": "<PERSON><PERSON><PERSON>", "selectBirthDate": "<PERSON><PERSON><PERSON> tari<PERSON> se<PERSON>", "registrationDate": "<PERSON><PERSON><PERSON>", "selectRegistrationDate": "<PERSON><PERSON><PERSON> ta<PERSON>hi se<PERSON>in", "parentInfo": "<PERSON><PERSON>", "parentName": "<PERSON><PERSON>", "parentSurname": "Veli Soyadı", "parentPhone": "Veli Telefonu", "parentEmail": "Veli E-postası", "parentAddress": "<PERSON><PERSON>", "paymentPlans": "Ödeme <PERSON>", "paymentPlansDescription": "Sporcu için ödeme planı atamaları", "noTeamsForPaymentPlans": "Bu sporcu henüz hiçbir takımda değil.", "addToTeamFirst": "Ödeme planları atamak için önce sporcuyu bir takıma ekleyin.", "teamAssignments": "Tak<PERSON>m <PERSON>ı", "addTeamAssignment": "Takım Ataması Ekle", "selectTeam": "Tak<PERSON>m seç", "optionalPaymentPlan": "Ödeme Planı (İsteğe bağlı)", "selectPaymentPlan": "Ödeme planı seç", "removeAssignment": "Atamayı Kaldır", "initialBalance": "Başlangıç Bakiyesi", "proratedBalance": "A<PERSON>ın kalan günleri için hesaplanmış orantılı bakiyeyi otomatik olarak uygula", "selectedPlan": "Seçilen Ödeme Planı", "selectedPlans": "Seçilen Ödeme Planları", "monthlyAmount": "Aylık <PERSON>", "remainingDays": "<PERSON><PERSON>", "calculatedAmount": "Hesaplanan Orantılı Tutar", "totalCalculatedAmount": "Toplam Hesaplanan Orantılı Tutar", "proratedAmountLabel": "Orantılı Tutar", "enterProratedAmount": "Orantılı tutar girin", "proratedEditHint": "Otomatik olarak hesaplanan tutarı değiştirebilir veya özel bir değer girebilirsiniz.", "manualBalance": "<PERSON> Bakiyesi", "enterBalance": "Başlangıç bakiye tutarını girin", "balanceHint": "Başlangıç bakiyesi olmaması için 0 girin veya boş bırakın. <PERSON><PERSON> tutar, sporcunun hesabına beklemede ödeme olarak eklenecektir.", "proratedHint": "<PERSON><PERSON> tuta<PERSON>, mevcut ayın kalan günlerine göre otomatik olarak hesaplanır."}, "actions": {"createAthlete": "S<PERSON><PERSON>", "creating": "Oluşturuluyor...", "editAthlete": "<PERSON><PERSON><PERSON>", "deleteAthlete": "<PERSON><PERSON><PERSON>", "activateAthlete": "Sporcu Aktifleştir", "deactivateAthlete": "<PERSON><PERSON><PERSON>", "activate": "Aktifleştir", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "openMenu": "Menüyü a<PERSON>", "viewDetails": "Detayları Görüntüle", "addNew": "<PERSON><PERSON>", "importFromExcel": "Excel'den İçe Aktar"}, "messages": {"createSuccess": "Sporcu başarıyla oluşturuldu", "createError": "Sporcu oluşturulurken hata oluştu", "updateSuccess": "S<PERSON><PERSON> başarıyla gü<PERSON>di", "updateError": "<PERSON><PERSON><PERSON> gü<PERSON>llenirken hata oluş<PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON> si<PERSON>en hata oluş<PERSON>", "activateSuccess": "Sporcu başarıyla aktifleştirildi", "activateError": "Sporcu aktifleştirilirken hata oluştu", "deactivateSuccess": "S<PERSON><PERSON> başarıyla pasifleştirildi", "deactivateError": "<PERSON><PERSON><PERSON> p<PERSON>fleştirilirken hata oluştu", "loadDataError": "<PERSON><PERSON> hata o<PERSON>", "operationError": "İşlem başarısız", "teamCount": "{{count}} takım", "teamCount_other": "{{count}} takım", "noTeams": "Takım yok", "manageAthletes": "Sporcularınızı ve bilgilerini yönetin", "requiredFields": "Lütfen tüm gerekli alanları doldurun", "requiredParentFields": "Lütfen tüm gerekli veli alanlarını doldurun", "noTeamsAvailable": "Mevcut takım yok", "allTeamsSelected": "Tüm mevcut takımlar seçildi", "duplicateTeams": "Her takım yalnızca bir kez seçilebilir"}, "management": {"joinTeam": "<PERSON><PERSON><PERSON><PERSON>", "leaveTeam": "Takı<PERSON>dan <PERSON>", "joinTeamDescription": "{{athlete<PERSON>ame}} adlı sporcuyu bir takıma ekle", "joinedAt": "<PERSON><PERSON><PERSON>", "noAvailableTeams": "Katılınabilecek takım yok", "addedToTeamSuccess": "{{teamName}} takımına başarıyla eklendi", "removedFromTeamSuccess": "{{teamName}} takımından başarıyla çıkarıldı", "addToTeamError": "Sporcu takıma eklenirken hata oluştu", "removeFromTeamError": "Sporcu takı<PERSON>ı<PERSON> hata oluştu"}, "table": {"name": "Ad", "surname": "Soyad", "nationalId": "TC Kimlik No", "birthDate": "<PERSON><PERSON><PERSON>", "parentName": "<PERSON><PERSON>", "parentSurname": "Veli Soyadı", "parentPhone": "Veli Telefonu", "parentEmail": "Veli E-postası", "parentAddress": "<PERSON><PERSON>", "teams": "Takımlar", "parent": "<PERSON><PERSON>", "balance": "Bakiye", "status": "Durum", "actions": "İşlemler"}, "teamAssignments": "Tak<PERSON>m <PERSON>ı", "addTeamAssignment": "<PERSON><PERSON><PERSON><PERSON>", "noTeamAssignments": "Takım ataması yok. Sporcuyu daha sonra takımlara ekleyebilirsiniz.", "teamAssignment": "Takım Ataması", "initialBalance": "Başlangıç Bakiyesi", "proratedBalance": "A<PERSON>ın kalan günleri için hesaplanmış orantılı bakiyeyi otomatik olarak uygula", "selectedPlan": "Seçilen Ödeme Planı", "selectedPlans": "Seçilen Ödeme Planları", "monthlyAmount": "Aylık <PERSON>", "remainingDays": "<PERSON><PERSON>", "calculatedAmount": "Hesaplanan Orantılı Tutar", "totalCalculatedAmount": "Toplam Hesaplanan Orantılı Tutar", "proratedAmountLabel": "Orantılı Tutar", "enterProratedAmount": "Orantılı tutar girin", "proratedEditHint": "Otomatik olarak hesaplanan tutarı değiştirebilir veya özel bir değer girebilirsiniz.", "manualBalance": "<PERSON> Bakiyesi", "enterBalance": "Başlangıç bakiye tutarını girin", "balanceHint": "Başlangıç bakiyesi olmaması için 0 girin veya boş bırakın. <PERSON><PERSON> tutar, sporcunun hesabına beklemede ödeme olarak eklenecektir.", "proratedHint": "<PERSON><PERSON> tuta<PERSON>, mevcut ayın kalan günlerine göre otomatik olarak hesaplanır.", "proratedLabel": "Orantılı", "placeholders": {"firstName": "Adını girin", "lastName": "Soyadını girin", "nationalId": "TC Kimlik No girin", "parentName": "<PERSON><PERSON> adını girin", "parentPhone": "Telefon numarası girin", "parentEmail": "E-posta adresi girin", "parentAddress": "<PERSON><PERSON> girin", "searchAthletes": "S<PERSON><PERSON> ara...", "searchMinChars": "Arama için en az 3 karakter girin"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "suspended": "Askıya Alınmış"}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON>", "description": "Bu sporcuyu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}, "activateDialog": {"title": "Sporcu Aktifleştir", "description": "Bu sporcuya atanacak takımları ve ödeme planlarını seçin.", "selectTeams": "Atanacak takımları seçin:", "selectPaymentPlan": "Ödeme planı seç (isteğe bağlı):", "noPaymentPlan": "Ödeme planı yok", "noAvailableTeams": "Mevcut takım yok."}, "deactivateDialog": {"title": "<PERSON><PERSON><PERSON>", "description": "Bu sporcuyu pasifleş<PERSON>rdi<PERSON>de, tüm takı<PERSON><PERSON><PERSON> çıkarılacak ve ödeme planları devre dışı bırakılacak.", "teamsToRemove": "Çıkarılacak takımlar:", "paymentPlansToRemove": "Devre dışı bırakılacak ödeme planları:", "noActiveAssignments": "Aktif takım veya ödeme planı ataması yok."}, "import": {"title": "Excel'den Sporcu İçe Aktar", "description": "Birden fazla sporcuyu aynı anda içe aktarmak için Excel dosyası yükleyin", "downloadTemplate": "Şablon İndir", "uploadFile": "Excel Dosyası Yükle", "selectFile": "Excel dosyası seç", "processing": "İşleniyor...", "success": "Sporcular başarıyla içe aktarıldı", "error": "Sporcular içe aktarılırken hata oluştu", "template": {"columns": {"name": "Ad*", "surname": "Soyad*", "nationalId": "TC Kimlik No*", "birthdate": "<PERSON><PERSON><PERSON>*", "registrationDate": "<PERSON><PERSON><PERSON>*", "parentName": "<PERSON><PERSON>*", "parentSurname": "Veli Soyadı*", "parentPhone": "Veli Telefonu*", "parentEmail": "Veli E-postası", "currentBalance": "Mevcut Ba<PERSON>ye", "currentBalanceLastPaymentDate": "Mevcut Bakiye Son Ödeme <PERSON>", "status": "Durum", "team1": "Takım1", "team2": "Takım2", "team3": "Takım3", "paymentPlan1": "Ödeme Planı1", "paymentPlan2": "Ödeme Planı2", "paymentPlan3": "Ödeme Planı3"}}, "validation": {"requiredFields": "{{row}} <PERSON><PERSON><PERSON><PERSON><PERSON> gerekli alanlar eksik", "invalidDate": "{{row}} satırında geçersiz tarih formatı", "invalidEmail": "{{row}} satırında geçersiz e-posta formatı", "duplicateNationalId": "{{row}} satırında tekrarlanan TC Kimlik No", "teamNotFound": "{{row}} sat<PERSON><PERSON><PERSON>nda takım bulunamadı: {{team}}", "paymentPlanNotFound": "{{row}} satırında ödeme planı bulunamadı: {{plan}}"}, "results": {"processed": "{{count}} <PERSON><PERSON><PERSON>", "created": "{{count}} s<PERSON><PERSON>", "errors": "{{count}} hata <PERSON>", "viewErrors": "Hataları Görüntüle", "row": "Satır"}, "messages": {"templateDownloaded": "Şablon başarıyla indirildi", "invalidFileType": "Lütfen geçerli bir Excel dosyası seçin (.xlsx veya .xls)", "noFileSelected": "Lütfen yüklenecek bir dosya seçin", "importInProgress": "İçe aktarma devam ediyor...", "importCompleted": "İçe aktarma başarıyla tamamlandı"}}}, "proratedCalculation": {"refundTitle": "Hesaplanmış İade Hesaplaması", "chargeTitle": "Hesaplanmış Ücret Hesaplaması", "remainingDays": "<PERSON><PERSON><PERSON> gün<PERSON>: {{total}} günün {{days}}'i", "totalRefund": "Toplam İade:", "totalCharge": "Toplam Ücret:", "refundNote": "<PERSON><PERSON><PERSON><PERSON><PERSON> günler için sporcu bakiyesine kredi eklenecektir.", "chargeNote": "Bu ayın kalan günleri için hesaplanmış ücret eklenecektir."}}