import { useState, useEffect } from "react";
import { getTeams } from "@/lib/db/actions/teams";
import { getAthleteTeams } from "@/lib/db/actions/athlete-teams";
import { getAthleteAssignedPlans } from "@/lib/db/actions/payment-plan-assignments";
import { getPaymentPlans } from "@/lib/db/actions/payment-plans";

export interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

export interface Team {
  id: string;
  name: string;
  branchId: string;
  branchName?: string;
  schoolId: string;
  schoolName?: string;
}

export interface AthleteTeam {
  teamId: string;
  teamName: string;
  branchName: string;
  branchId: string;
  schoolName: string;
  schoolId: string;
  instructorName: string;
  instructorSurname: string;
  joinedAt: string;
  leftAt?: string | null;
}

export interface AthletePaymentPlan {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

interface UsePaymentPlanManagementProps {
  athleteId: string;
}

export function usePaymentPlanManagement({ athleteId }: UsePaymentPlanManagementProps) {
  const [paymentPlanData, setPaymentPlanData] = useState<{
    athleteTeams: AthleteTeam[];
    availableTeams: Team[];
    athletePaymentPlans: AthletePaymentPlan[];
    availablePaymentPlans: PaymentPlan[];
    loading: boolean;
  }>({
    athleteTeams: [],
    availableTeams: [],
    athletePaymentPlans: [],
    availablePaymentPlans: [],
    loading: true
  });

  const [paymentPlanChanges, setPaymentPlanChanges] = useState<AthletePaymentPlan[]>([]);
  const [originalPaymentPlans, setOriginalPaymentPlans] = useState<AthletePaymentPlan[]>([]);

  // Load payment plan data
  useEffect(() => {
    const loadPaymentPlanData = async () => {
      try {
        const [teams, athleteTeamsData, plans, assignedPlans] = await Promise.all([
          getTeams(),
          getAthleteTeams(athleteId),
          getPaymentPlans(),
          getAthleteAssignedPlans(athleteId),
        ]);

        setPaymentPlanData({
          athleteTeams: athleteTeamsData,
          availableTeams: teams,
          athletePaymentPlans: assignedPlans,
          availablePaymentPlans: plans,
          loading: false
        });
        
        // Store original payment plans for comparison
        setOriginalPaymentPlans(assignedPlans);
        setPaymentPlanChanges(assignedPlans);
      } catch (error) {
        console.error("Error loading payment plan data:", error);
        setPaymentPlanData(prev => ({ ...prev, loading: false }));
      }
    };

    loadPaymentPlanData();
  }, [athleteId]);

  const handlePaymentPlanChange = (assignments: AthletePaymentPlan[]) => {
    setPaymentPlanChanges(assignments);
  };

  return {
    paymentPlanData,
    paymentPlanChanges,
    originalPaymentPlans,
    handlePaymentPlanChange,
  };
}
