/**
 * Client-side Image Cache Utility
 * 
 * Provides secure, tenant-isolated image caching for authenticated images.
 * Uses blob URLs and memory storage with automatic cleanup and expiration.
 */

interface CachedImage {
  blobUrl: string;
  timestamp: number;
  tenantId: string;
  originalSrc: string;
}

interface ImageCacheOptions {
  maxAge?: number; // Cache duration in milliseconds
  maxSize?: number; // Maximum number of cached images
}

class ImageCache {
  private cache = new Map<string, CachedImage>();
  private readonly maxAge: number;
  private readonly maxSize: number;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(options: ImageCacheOptions = {}) {
    this.maxAge = options.maxAge || 30 * 60 * 1000; // 30 minutes default
    this.maxSize = options.maxSize || 100; // 100 images max
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Generate cache key from image source and tenant ID
   */
  private getCacheKey(src: string, tenantId: string): string {
    return `${tenantId}:${src}`;
  }

  /**
   * Check if cached image is still valid
   */
  private isValid(cachedImage: CachedImage): boolean {
    const now = Date.now();
    return (now - cachedImage.timestamp) < this.maxAge;
  }

  /**
   * Clean up expired cache entries and enforce size limits
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // Find expired entries
    this.cache.forEach((cachedImage, key) => {
      if (!this.isValid(cachedImage)) {
        expiredKeys.push(key);
        // Revoke blob URL to free memory
        URL.revokeObjectURL(cachedImage.blobUrl);
      }
    });

    // Remove expired entries
    expiredKeys.forEach(key => this.cache.delete(key));

    // Enforce size limit by removing oldest entries
    if (this.cache.size > this.maxSize) {
      const entries: Array<[string, CachedImage]> = [];
      this.cache.forEach((value, key) => {
        entries.push([key, value]);
      });
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, this.cache.size - this.maxSize);
      toRemove.forEach(([key, cachedImage]) => {
        URL.revokeObjectURL(cachedImage.blobUrl);
        this.cache.delete(key);
      });
    }
  }

  /**
   * Start automatic cleanup interval
   */
  private startCleanupInterval(): void {
    // Clean up every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Get cached image if available and valid
   */
  get(src: string, tenantId: string): string | null {
    const key = this.getCacheKey(src, tenantId);
    const cachedImage = this.cache.get(key);

    if (!cachedImage) {
      return null;
    }

    if (!this.isValid(cachedImage)) {
      // Clean up expired entry
      URL.revokeObjectURL(cachedImage.blobUrl);
      this.cache.delete(key);
      return null;
    }

    return cachedImage.blobUrl;
  }

  /**
   * Store image in cache
   */
  set(src: string, tenantId: string, blob: Blob): string {
    const key = this.getCacheKey(src, tenantId);
    
    // Remove existing entry if present
    const existing = this.cache.get(key);
    if (existing) {
      URL.revokeObjectURL(existing.blobUrl);
    }

    // Create blob URL
    const blobUrl = URL.createObjectURL(blob);

    // Store in cache
    const cachedImage: CachedImage = {
      blobUrl,
      timestamp: Date.now(),
      tenantId,
      originalSrc: src,
    };

    this.cache.set(key, cachedImage);

    // Trigger cleanup if needed
    if (this.cache.size > this.maxSize) {
      this.cleanup();
    }

    return blobUrl;
  }

  /**
   * Remove specific image from cache
   */
  invalidate(src: string, tenantId: string): void {
    const key = this.getCacheKey(src, tenantId);
    const cachedImage = this.cache.get(key);
    
    if (cachedImage) {
      URL.revokeObjectURL(cachedImage.blobUrl);
      this.cache.delete(key);
    }
  }

  /**
   * Clear all cached images for a specific tenant
   */
  invalidateTenant(tenantId: string): void {
    const keysToRemove: string[] = [];

    this.cache.forEach((cachedImage, key) => {
      if (cachedImage.tenantId === tenantId) {
        keysToRemove.push(key);
        URL.revokeObjectURL(cachedImage.blobUrl);
      }
    });

    keysToRemove.forEach(key => this.cache.delete(key));
  }

  /**
   * Clear all cached images
   */
  clear(): void {
    this.cache.forEach((cachedImage) => {
      URL.revokeObjectURL(cachedImage.blobUrl);
    });
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; maxSize: number; maxAge: number } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      maxAge: this.maxAge,
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

// Global cache instance
export const imageCache = new ImageCache({
  maxAge: 30 * 60 * 1000, // 30 minutes
  maxSize: 100, // 100 images
});

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    imageCache.destroy();
  });
}
