import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateFacilityData {
  name: string;
  type: 'field' | 'court' | 'pool' | 'studio' | 'other';
  address: string;
  totalCapacity?: number;
  currentlyOccupied?: number;
  length?: string;
  width?: string;
  dimensionUnit?: 'meters' | 'feet';
}

export interface UpdateFacilityData {
  name?: string;
  type?: 'field' | 'court' | 'pool' | 'studio' | 'other';
  address?: string;
  totalCapacity?: number;
  currentlyOccupied?: number;
  length?: string;
  width?: string;
  dimensionUnit?: 'meters' | 'feet';
}

export class FacilityService extends BaseService {
  constructor() {
    super('FacilityService');
  }

  /**
   * Get all facilities
   */
  async getFacilities(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getFacilities',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getFacilities(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facilities',
      }
    );
  }

  /**
   * Get facility by ID
   */
  async getFacilityById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getFacilityById',
      'Facility',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getFacilityById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facility',
      }
    );
  }

  /**
   * Get schedules for a specific facility
   */
  async getFacilitySchedules(
    facilityId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getFacilitySchedules',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getFacilitySchedules(facilityId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facilitySchedules',
        metadata: { facilityId },
      }
    );
  }

  /**
   * Get all facility schedules
   */
  async getAllFacilitySchedules(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAllFacilitySchedules',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getAllFacilitySchedules(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facilitySchedules',
      }
    );
  }

  /**
   * Create a new facility
   */
  async createFacility(
    data: CreateFacilityData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateFacilityData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'Facility name is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 2 || data.name.length > 255) {
          return { field: 'name', message: 'Facility name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (!data.type) {
          return { field: 'type', message: 'Facility type is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const validTypes = ['field', 'court', 'pool', 'studio', 'other'];
        if (!validTypes.includes(data.type)) {
          return { field: 'type', message: 'Invalid facility type', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (!data.address || data.address.trim().length === 0) {
          return { field: 'address', message: 'Address is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.address.length > 1000) {
          return { field: 'address', message: 'Address must be no more than 1000 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (data.totalCapacity !== undefined && data.totalCapacity !== null) {
          if (data.totalCapacity < 0) {
            return { field: 'totalCapacity', message: 'Total capacity must be a positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (data.currentlyOccupied !== undefined && data.currentlyOccupied !== null) {
          if (data.currentlyOccupied < 0) {
            return { field: 'currentlyOccupied', message: 'Currently occupied must be a positive number', code: 'INVALID_VALUE' };
          }
          if (data.totalCapacity !== undefined && data.currentlyOccupied > data.totalCapacity) {
            return { field: 'currentlyOccupied', message: 'Currently occupied cannot exceed total capacity', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (data.length !== undefined && data.length !== null && data.length.length > 0) {
          const lengthValue = parseFloat(data.length);
          if (isNaN(lengthValue) || lengthValue <= 0) {
            return { field: 'length', message: 'Length must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (data.width !== undefined && data.width !== null && data.width.length > 0) {
          const widthValue = parseFloat(data.width);
          if (isNaN(widthValue) || widthValue <= 0) {
            return { field: 'width', message: 'Width must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: CreateFacilityData): ValidationError | null => {
        if (data.dimensionUnit !== undefined && data.dimensionUnit !== null) {
          const validUnits = ['meters', 'feet'];
          if (!validUnits.includes(data.dimensionUnit)) {
            return { field: 'dimensionUnit', message: 'Invalid dimension unit', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createFacility',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check for duplicate facility name
        const existingFacility = await TenantAwareDB.getFacilityByName(validatedData.name, effectiveTenantId || undefined);
        if (existingFacility) {
          throw new BusinessRuleError('duplicate_name', 'A facility with this name already exists');
        }

        return TenantAwareDB.createFacility(validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facility',
      }
    );
  }

  /**
   * Update a facility
   */
  async updateFacility(
    id: string,
    data: UpdateFacilityData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.name !== undefined) {
          if (!data.name || data.name.trim().length === 0) {
            return { field: 'name', message: 'Facility name cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.name.length < 2 || data.name.length > 255) {
            return { field: 'name', message: 'Facility name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.type !== undefined) {
          const validTypes = ['field', 'court', 'pool', 'studio', 'other'];
          if (!validTypes.includes(data.type)) {
            return { field: 'type', message: 'Invalid facility type', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.address !== undefined) {
          if (!data.address || data.address.trim().length === 0) {
            return { field: 'address', message: 'Address cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.address.length > 1000) {
            return { field: 'address', message: 'Address must be no more than 1000 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.totalCapacity !== undefined && data.totalCapacity !== null) {
          if (data.totalCapacity < 0) {
            return { field: 'totalCapacity', message: 'Total capacity must be a positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.currentlyOccupied !== undefined && data.currentlyOccupied !== null) {
          if (data.currentlyOccupied < 0) {
            return { field: 'currentlyOccupied', message: 'Currently occupied must be a positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.length !== undefined && data.length !== null && data.length.length > 0) {
          const lengthValue = parseFloat(data.length);
          if (isNaN(lengthValue) || lengthValue <= 0) {
            return { field: 'length', message: 'Length must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.width !== undefined && data.width !== null && data.width.length > 0) {
          const widthValue = parseFloat(data.width);
          if (isNaN(widthValue) || widthValue <= 0) {
            return { field: 'width', message: 'Width must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateFacilityData): ValidationError | null => {
        if (data.dimensionUnit !== undefined && data.dimensionUnit !== null) {
          const validUnits = ['meters', 'feet'];
          if (!validUnits.includes(data.dimensionUnit)) {
            return { field: 'dimensionUnit', message: 'Invalid dimension unit', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateFacility',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if facility exists
        const existingFacility = await TenantAwareDB.getFacilityById(id, effectiveTenantId || undefined);
        if (!existingFacility) {
          throw new NotFoundError('Facility not found');
        }

        // Check for duplicate facility name (excluding current facility)
        if (validatedData.name) {
          const duplicateFacility = await TenantAwareDB.getFacilityByName(validatedData.name, effectiveTenantId || undefined);
          if (duplicateFacility && duplicateFacility.id !== id) {
            throw new BusinessRuleError('duplicate_name', 'A facility with this name already exists');
          }
        }

        // Validate capacity constraints
        if (validatedData.totalCapacity !== undefined && validatedData.currentlyOccupied !== undefined) {
          if (validatedData.currentlyOccupied > validatedData.totalCapacity) {
            throw new BusinessRuleError('capacity_exceeded', 'Currently occupied cannot exceed total capacity');
          }
        } else if (validatedData.totalCapacity !== undefined && existingFacility.currentlyOccupied) {
          if (existingFacility.currentlyOccupied > validatedData.totalCapacity) {
            throw new BusinessRuleError('capacity_exceeded', 'Total capacity cannot be less than currently occupied');
          }
        } else if (validatedData.currentlyOccupied !== undefined && existingFacility.totalCapacity) {
          if (validatedData.currentlyOccupied > existingFacility.totalCapacity) {
            throw new BusinessRuleError('capacity_exceeded', 'Currently occupied cannot exceed total capacity');
          }
        }

        return TenantAwareDB.updateFacility(id, validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'facility',
      }
    );
  }

  /**
   * Delete a facility
   */
  async deleteFacility(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteFacility',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if facility exists
        const existingFacility = await TenantAwareDB.getFacilityById(id, effectiveTenantId || undefined);
        if (!existingFacility) {
          throw new NotFoundError('Facility not found');
        }

        // Check for dependent training schedules
        const schedules = await TenantAwareDB.getTrainingSchedulesByFacilityId(id, effectiveTenantId || undefined);
        if (schedules && schedules.length > 0) {
          throw new BusinessRuleError('has_dependent_schedules', 'Cannot delete facility with existing training schedules');
        }

        await TenantAwareDB.deleteFacility(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'facility',
      }
    );
  }
}

// Factory function
export const facilityService = () => new FacilityService();
