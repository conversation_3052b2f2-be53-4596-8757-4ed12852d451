"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { imageCache } from '@/lib/image-cache';

interface UseCachedImageOptions {
  enabled?: boolean;
  priority?: boolean;
}

interface UseCachedImageReturn {
  src: string | null;
  isLoading: boolean;
  error: string | null;
  isFromCache: boolean;
  invalidate: () => void;
  retry: () => void;
  cacheImage: (imgElement: HTMLImageElement) => void;
}

export function useCachedImage(
  originalSrc: string | null | undefined,
  options: UseCachedImageOptions = {}
): UseCachedImageReturn {
  const { data: session, status } = useSession();
  const [src, setSrc] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFromCache, setIsFromCache] = useState(false);
  const { enabled = true, priority = false } = options;

  // Get tenant ID from session
  const tenantId = session?.tenantId;

  // Function to cache an image after it's successfully loaded by the browser
  const cacheImage = useCallback(async (imgElement: HTMLImageElement) => {
    if (!enabled || !originalSrc || !tenantId) return;

    try {
      // Convert the loaded image to a blob for caching
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      canvas.width = imgElement.naturalWidth;
      canvas.height = imgElement.naturalHeight;
      ctx.drawImage(imgElement, 0, 0);

      canvas.toBlob((blob) => {
        if (blob && originalSrc && tenantId) {
          const blobUrl = imageCache.set(originalSrc, tenantId, blob);
          // Don't update src here since the original image is already displayed
        }
      }, 'image/jpeg', 0.9);
    } catch (error) {
      console.warn('Failed to cache image:', error);
    }
  }, [enabled, originalSrc, tenantId]);

  const invalidate = useCallback(() => {
    if (originalSrc && tenantId) {
      imageCache.invalidate(originalSrc, tenantId);
      setSrc(null);
      setIsFromCache(false);
    }
  }, [originalSrc, tenantId]);

  const retry = useCallback(() => {
    if (originalSrc && tenantId && enabled) {
      setError(null);
      // Reset state and let the effect handle reloading
      setSrc(null);
      setIsFromCache(false);
    }
  }, [originalSrc, tenantId, enabled]);

  // Main effect to check cache and set src
  useEffect(() => {
    // Reset state when src changes
    setError(null);
    setIsFromCache(false);

    // Don't load if disabled or no src
    if (!enabled || !originalSrc) {
      setSrc(originalSrc || null);
      setIsLoading(false);
      return;
    }

    // Check if this is a secure image that might be cached
    const isSecureImage = originalSrc.includes('/api/secure-images/');

    if (isSecureImage && tenantId) {
      // Check cache first
      const cachedSrc = imageCache.get(originalSrc, tenantId);
      if (cachedSrc) {
        setSrc(cachedSrc);
        setIsFromCache(true);
        setIsLoading(false);
        return;
      }
    }

    // Use original src (either not cached or not a secure image)
    setSrc(originalSrc);
    setIsFromCache(false);
    setIsLoading(false);
  }, [originalSrc, enabled, tenantId]);

  return {
    src,
    isLoading,
    error,
    isFromCache,
    invalidate,
    retry,
    cacheImage,
  };
}
