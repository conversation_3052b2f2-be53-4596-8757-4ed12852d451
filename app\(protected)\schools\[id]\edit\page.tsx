import React from "react";
import { getSchoolById } from "@/lib/db/actions";
import { SchoolEditClient } from "./school-edit-client";
import { notFound } from "next/navigation";

export default async function EditSchoolPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const school = await getSchoolById(id);

  if (!school) {
    notFound();
  }

  return <SchoolEditClient school={school} />;
}