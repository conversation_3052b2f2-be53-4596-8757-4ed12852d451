import { Metada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { getPaymentById, getAthletes } from "@/lib/actions";
import EditPaymentClient from "@/components/payments/edit/EditPaymentClient";

export async function generateMetadata({ params }: {
  params: Promise<{ id: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  
  try {
    const payment = await getPaymentById(resolvedParams.id);
    
    if (!payment) {
      return {
        title: "Payment Not Found",
        description: "The requested payment could not be found.",
      };
    }

    const athleteName = payment.athlete ? `${payment.athlete.name} ${payment.athlete.surname}` : 'Unknown';
    
    return {
      title: `Edit Payment - ${athleteName}`,
      description: `Edit payment details for ${athleteName}`,
    };
  } catch (error) {
    return {
      title: "Edit Payment",
      description: "Edit payment details and information.",
    };
  }
}

interface Props {
  params: Promise<{ id: string }>;
}

export default async function EditPaymentPage({ params }: Props) {
  const resolvedParams = await params;
  
  try {
    const [payment, athletes] = await Promise.all([
      getPaymentById(resolvedParams.id),
      getAthletes()
    ]);
    
    if (!payment) {
      notFound();
    }

    return (
      <EditPaymentClient 
        payment={payment} 
        athletes={athletes}
      />
    );
  } catch (error) {
    console.error("Failed to load payment edit data:", error);
    notFound();
  }
}