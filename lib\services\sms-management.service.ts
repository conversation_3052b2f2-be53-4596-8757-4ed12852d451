import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { validators } from './validation';

export interface CreateSmsConfigurationData {
  pendingPaymentTemplate: string;
  overduePaymentTemplate: string;
  pendingReminderDays: number[]; // Days before due date
  overdueReminderDays: number[]; // Days after due date
}

export interface UpdateSmsConfigurationData {
  pendingPaymentTemplate?: string;
  overduePaymentTemplate?: string;
  pendingReminderDays?: number[];
  overdueReminderDays?: number[];
}

export interface SmsTemplateVariables {
  schoolName?: string;
  teamName?: string;
  athleteName?: string;
  parentName?: string;
  amount?: string;
  paymentDueDate?: string;
}

/**
 * SMS Management Service
 * Handles SMS configuration, template processing, and reminder scheduling
 */
export class SmsManagementService extends BaseService {
  constructor() {
    super('SmsManagementService');
  }

  /**
   * Get all SMS configurations
   */
  async getSmsConfigurations(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSmsConfigurations',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsConfigurations(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_configurations',
      }
    );
  }

  /**
   * Get active SMS configuration
   */
  async getActiveSmsConfiguration(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getActiveSmsConfiguration',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        const config = await TenantAwareDB.getActiveSmsConfiguration(effectiveTenantId || undefined);
        
        if (!config) {
          // Return default configuration if none exists
          // Note: These templates will be replaced with localized versions in the frontend
          return {
            pendingPaymentTemplate: 'Dear {{parentName}}, your child {{athleteName}}\'s payment of {{amount}} for {{schoolName}} is due on {{paymentDueDate}}. Please make your payment on time.',
            overduePaymentTemplate: 'Dear {{parentName}}, your child {{athleteName}}\'s payment of {{amount}} for {{schoolName}} was due on {{paymentDueDate}}. Please make your payment immediately.',
            pendingReminderDays: [-5, -3, -1],
            overdueReminderDays: [1, 3, 5],
            isActive: false,
            version: 0
          };
        }

        // Parse JSON strings back to arrays
        return {
          ...config,
          pendingReminderDays: JSON.parse(config.pendingReminderDays),
          overdueReminderDays: JSON.parse(config.overdueReminderDays)
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_configuration',
      }
    );
  }

  /**
   * Create SMS configuration
   */
  async createSmsConfiguration(
    data: CreateSmsConfigurationData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = this.createSmsConfigurationValidationFunctions();

    return this.executeWithValidation(
      'createSmsConfiguration',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Convert arrays to JSON strings for storage
        const configData = {
          pendingPaymentTemplate: validatedData.pendingPaymentTemplate,
          overduePaymentTemplate: validatedData.overduePaymentTemplate,
          pendingReminderDays: JSON.stringify(validatedData.pendingReminderDays),
          overdueReminderDays: JSON.stringify(validatedData.overdueReminderDays),
        };

        const result = await TenantAwareDB.createSmsConfiguration(
          configData,
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );

        // Parse JSON strings back to arrays for response
        return {
          ...result,
          pendingReminderDays: JSON.parse(result.pendingReminderDays),
          overdueReminderDays: JSON.parse(result.overdueReminderDays)
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_configuration',
      }
    );
  }

  /**
   * Update SMS configuration
   */
  async updateSmsConfiguration(
    id: string,
    data: UpdateSmsConfigurationData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = this.createUpdateSmsConfigurationValidationFunctions();

    return this.executeWithValidation(
      'updateSmsConfiguration',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Check if configuration exists
        const existingConfig = await TenantAwareDB.getSmsConfigurationById(id, effectiveTenantId || undefined);
        if (!existingConfig) {
          throw new NotFoundError('SMS configuration not found');
        }

        // Convert arrays to JSON strings for storage if provided
        const updateData: any = {};
        if (validatedData.pendingPaymentTemplate !== undefined) {
          updateData.pendingPaymentTemplate = validatedData.pendingPaymentTemplate;
        }
        if (validatedData.overduePaymentTemplate !== undefined) {
          updateData.overduePaymentTemplate = validatedData.overduePaymentTemplate;
        }
        if (validatedData.pendingReminderDays !== undefined) {
          updateData.pendingReminderDays = JSON.stringify(validatedData.pendingReminderDays);
        }
        if (validatedData.overdueReminderDays !== undefined) {
          updateData.overdueReminderDays = JSON.stringify(validatedData.overdueReminderDays);
        }

        const result = await TenantAwareDB.updateSmsConfiguration(
          id,
          updateData,
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );

        // Parse JSON strings back to arrays for response
        return {
          ...result,
          pendingReminderDays: JSON.parse(result.pendingReminderDays),
          overdueReminderDays: JSON.parse(result.overdueReminderDays)
        };
      },
      {
        userId,
        tenantId,
        resource: 'sms_configuration',
        metadata: { configurationId: id },
      }
    );
  }

  /**
   * Activate SMS configuration
   */
  async activateSmsConfiguration(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'activateSmsConfiguration',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        // Check if configuration exists
        const existingConfig = await TenantAwareDB.getSmsConfigurationById(id, effectiveTenantId || undefined);
        if (!existingConfig) {
          throw new NotFoundError('SMS configuration not found');
        }

        await TenantAwareDB.activateSmsConfiguration(
          id,
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );

        return true;
      },
      {
        userId,
        tenantId,
        resource: 'sms_configuration',
        metadata: { configurationId: id },
      }
    );
  }

  /**
   * Deactivate all SMS configurations
   */
  async deactivateAllSmsConfigurations(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deactivateAllSmsConfigurations',
      async () => {
        const effectiveUserId = userId || await getServerUserId();
        const effectiveTenantId = tenantId || await getServerTenantId();

        await TenantAwareDB.deactivateAllConfigurations(
          effectiveTenantId || undefined,
          effectiveUserId ? BigInt(effectiveUserId) : undefined
        );

        return true;
      },
      {
        userId,
        tenantId,
        resource: 'sms_configuration',
        metadata: { action: 'deactivate_all' },
      }
    );
  }

  /**
   * Process template with variables
   */
  processTemplate(template: string, variables: SmsTemplateVariables): string {
    let processedTemplate = template;

    // Replace all template variables
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      processedTemplate = processedTemplate.replace(new RegExp(placeholder, 'g'), value || '');
    });

    // Remove any remaining unreplaced variables (like paymentDueDate for combined payments)
    processedTemplate = processedTemplate.replace(/\{\{[^}]+\}\}/g, '');

    return processedTemplate;
  }

  /**
   * Validate template variables
   */
  validateTemplate(template: string): { isValid: boolean; invalidVariables: string[] } {
    const allowedVariables = ['schoolName', 'teamName', 'athleteName', 'parentName', 'amount', 'paymentDueDate'];
    const variableRegex = /\{\{(\w+)\}\}/g;
    const foundVariables: string[] = [];
    const invalidVariables: string[] = [];

    let match;
    while ((match = variableRegex.exec(template)) !== null) {
      const variable = match[1];
      foundVariables.push(variable);
      if (!allowedVariables.includes(variable)) {
        invalidVariables.push(variable);
      }
    }

    return {
      isValid: invalidVariables.length === 0,
      invalidVariables
    };
  }

  /**
   * Get reminder days for a payment based on its due date and status
   */
  getReminderDaysForPayment(
    paymentDueDate: Date,
    paymentStatus: 'pending' | 'overdue',
    configuration: any
  ): number[] {
    const today = new Date();
    const dueDate = new Date(paymentDueDate);
    const daysDifference = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (paymentStatus === 'pending') {
      // For pending payments, check if today matches any of the reminder days before due date
      return configuration.pendingReminderDays.filter((days: number) => daysDifference === Math.abs(days));
    } else {
      // For overdue payments, check if today matches any of the reminder days after due date
      return configuration.overdueReminderDays.filter((days: number) => Math.abs(daysDifference) === days);
    }
  }

  /**
   * Private validation functions
   */
  private createSmsConfigurationValidationFunctions(): Array<(data: CreateSmsConfigurationData) => ValidationError | null> {
    return [
      validators.required('pendingPaymentTemplate'),
      validators.required('overduePaymentTemplate'),
      validators.required('pendingReminderDays'),
      validators.required('overdueReminderDays'),
      (data: CreateSmsConfigurationData): ValidationError | null => {
        const pendingValidation = this.validateTemplate(data.pendingPaymentTemplate);
        if (!pendingValidation.isValid) {
          return {
            field: 'pendingPaymentTemplate',
            message: `Invalid template variables: ${pendingValidation.invalidVariables.join(', ')}`,
            code: 'INVALID_TEMPLATE_VARIABLES'
          };
        }
        return null;
      },
      (data: CreateSmsConfigurationData): ValidationError | null => {
        const overdueValidation = this.validateTemplate(data.overduePaymentTemplate);
        if (!overdueValidation.isValid) {
          return {
            field: 'overduePaymentTemplate',
            message: `Invalid template variables: ${overdueValidation.invalidVariables.join(', ')}`,
            code: 'INVALID_TEMPLATE_VARIABLES'
          };
        }
        return null;
      },
      (data: CreateSmsConfigurationData): ValidationError | null => {
        if (!Array.isArray(data.pendingReminderDays) || data.pendingReminderDays.length === 0) {
          return {
            field: 'pendingReminderDays',
            message: 'Pending reminder days must be a non-empty array',
            code: 'INVALID_ARRAY'
          };
        }
        return null;
      },
      (data: CreateSmsConfigurationData): ValidationError | null => {
        if (!Array.isArray(data.overdueReminderDays) || data.overdueReminderDays.length === 0) {
          return {
            field: 'overdueReminderDays',
            message: 'Overdue reminder days must be a non-empty array',
            code: 'INVALID_ARRAY'
          };
        }
        return null;
      },
    ];
  }

  private createUpdateSmsConfigurationValidationFunctions(): Array<(data: UpdateSmsConfigurationData) => ValidationError | null> {
    return [
      (data: UpdateSmsConfigurationData): ValidationError | null => {
        if (data.pendingPaymentTemplate !== undefined) {
          const validation = this.validateTemplate(data.pendingPaymentTemplate);
          if (!validation.isValid) {
            return {
              field: 'pendingPaymentTemplate',
              message: `Invalid template variables: ${validation.invalidVariables.join(', ')}`,
              code: 'INVALID_TEMPLATE_VARIABLES'
            };
          }
        }
        return null;
      },
      (data: UpdateSmsConfigurationData): ValidationError | null => {
        if (data.overduePaymentTemplate !== undefined) {
          const validation = this.validateTemplate(data.overduePaymentTemplate);
          if (!validation.isValid) {
            return {
              field: 'overduePaymentTemplate',
              message: `Invalid template variables: ${validation.invalidVariables.join(', ')}`,
              code: 'INVALID_TEMPLATE_VARIABLES'
            };
          }
        }
        return null;
      },
      (data: UpdateSmsConfigurationData): ValidationError | null => {
        if (data.pendingReminderDays !== undefined && (!Array.isArray(data.pendingReminderDays) || data.pendingReminderDays.length === 0)) {
          return {
            field: 'pendingReminderDays',
            message: 'Pending reminder days must be a non-empty array',
            code: 'INVALID_ARRAY'
          };
        }
        return null;
      },
      (data: UpdateSmsConfigurationData): ValidationError | null => {
        if (data.overdueReminderDays !== undefined && (!Array.isArray(data.overdueReminderDays) || data.overdueReminderDays.length === 0)) {
          return {
            field: 'overdueReminderDays',
            message: 'Overdue reminder days must be a non-empty array',
            code: 'INVALID_ARRAY'
          };
        }
        return null;
      },
    ];
  }
}
