import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import type { Team, School, Instructor } from "@/lib/types";

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Team name must be at least 2 characters.",
  }),
  description: z.string().optional(),
  schoolId: z.string({
    required_error: "Please select a school",
  }),
  instructorId: z.string({
    required_error: "Please select an instructor",
  }),
  branchId: z.string({
    required_error: "Please select a branch",
  }),
});

export type TeamFormData = z.infer<typeof formSchema>;

interface UseTeamFormProps {
  team: Team;
  schools: School[];
  instructors: Instructor[];
}

export function useTeamForm({ team, schools, instructors }: UseTeamFormProps) {
  const [selectedSchool, setSelectedSchool] = useState<string>(team.schoolId);

  const form = useForm<TeamFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: team.name,
      description: team.description || "",
      schoolId: team.schoolId,
      instructorId: team.instructorId,
      branchId: team.branchId,
    },
  });

  const selectedSchoolData = schools.find(s => s.id === selectedSchool);
  const branches = selectedSchoolData?.branches || [];
  
  const filteredInstructors = instructors.filter(instructor => 
    instructor.schools.includes(selectedSchool)
  );

  const handleSchoolChange = (schoolId: string) => {
    const school = schools.find(s => s.id === schoolId);
    if (school) {
      setSelectedSchool(schoolId);
      form.setValue("schoolId", schoolId);
      form.setValue("branchId", "");
      form.setValue("instructorId", "");
    }
  };

  return {
    form,
    selectedSchool,
    selectedSchoolData,
    branches,
    filteredInstructors,
    handleSchoolChange,
  };
}
