"use server";

import { itemService } from '../services/item.service';
import { revalidatePath } from 'next/cache';

// Items
export async function getItems() {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.getItems();

    if (result.success) {
      return result.data || [];
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get items");
  } catch (error) {
    console.error("Error getting items:", error);
    throw error;
  }
}

export async function getItemById(id: string) {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.getItemById(id);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || `Failed to get item with ID ${id}`);
  } catch (error) {
    console.error("Error getting item by ID:", error);
    throw error;
  }
}

export async function createItem(data: { 
  name: string; 
  description?: string; 
  price: string; 
  category: 'equipment' | 'clothing' | 'accessories' | 'other'; 
  stock: number;
  image?: string;
}) {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.createItem(data);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/items');
      return { success: true, data: result.data };
    }else{
      console.error("Service call failed:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create item' , errorType: 'general' };
    }
  } catch (error) {
    console.error("createItem error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to create item", errorType: 'general' };
  }
}

export async function updateItem(id: string, data: { 
  name?: string; 
  description?: string; 
  price?: string; 
  category?: 'equipment' | 'clothing' | 'accessories' | 'other'; 
  stock?: number;
  image?: string;
}) {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.updateItem(id, data);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/items');
      revalidatePath(`/items/${id}`);
      return { success: true, data: result.data };
    }else{
      console.error(`Failed to update item with ID ${id}. Service call failed:`, result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to update item' , errorType: 'general' };
    }
  } catch (error) {
    console.error("updateItem error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to update item", errorType: 'general' };
  }
}

export async function deleteItem(id: string) {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.deleteItem(id);

    if (result.success) {
      // Revalidate paths
      revalidatePath('/items');
      return { success: true };
    }else{
      console.error(`Failed to delete item with ID ${id}. Service call failed:`, result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to delete item' , errorType: 'general' };
    }
  } catch (error) {
    console.error("deleteItem error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to delete item", errorType: 'general' };
  }
}

export async function getLowStockItems(threshold: number = 5) {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.getLowStockItems(threshold);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get low stock items");
  } catch (error) {
    console.error("Error getting low stock items:", error);
    throw error;
  }
}

export async function getItemsByCategory(category: 'equipment' | 'clothing' | 'accessories' | 'other') {
  try {
    // Try using the service first
    const service = itemService();
    const result = await service.getItemsByCategory(category);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get items by category");
  } catch (error) {
    console.error("Error getting items by category:", error);
    throw error;
  }
}