import { db } from '.';
import * as schema from './schema';

// Constants for seeding
const TENANT_ID = '321151237678497795';
const USER_ID = BigInt('321151237679022083');

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // 1. Seed Branches (sport types - shared across all tenants)
    console.log('📋 Seeding branches...');
    const branchesData = [
      {
        name: 'Football',
        description: 'Association football (soccer) training and matches',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'Basketball',
        description: 'Basketball training, matches and tournaments',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'Tennis',
        description: 'Tennis lessons and court rentals',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'Swimming',
        description: 'Swimming lessons and water sports',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'Volleyball',
        description: 'Volleyball training and competitions',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'Martial Arts',
        description: 'Karate, Taekwondo, and other martial arts',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const branches = await db.insert(schema.branches).values(branchesData).returning();
    console.log(`✅ Created ${branches.length} branches`);

    // 2. Seed Schools
    console.log('🏫 Seeding schools...');
    const schoolsData = [
      {
        tenantId: TENANT_ID,
        name: 'Elite Sports Academy',
        foundedYear: 2020,
        logo: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg',
        address: '123 Champions Boulevard, Sports City, SC 12345',
        phone: '******-0100',
        email: '<EMAIL>',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Victory Athletic Center',
        foundedYear: 2018,
        logo: 'https://images.pexels.com/photos/2247179/pexels-photo-2247179.jpeg',
        address: '456 Winners Street, Athletic Town, AT 67890',
        phone: '******-0200',
        email: '<EMAIL>',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const schools = await db.insert(schema.schools).values(schoolsData).returning();
    console.log(`✅ Created ${schools.length} schools`);

    // 3. Seed Instructors
    console.log('👨‍🏫 Seeding instructors...');
    const instructorsData = [
      {
        tenantId: TENANT_ID,
        name: 'John',
        surname: 'Smith',
        email: '<EMAIL>',
        phone: '******-1001',
        nationalId: '12345678901',
        birthDate: '1985-03-15',
        address: '789 Coach Lane, Sports City, SC 12345',
        salary: '5000.00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Sarah',
        surname: 'Johnson',
        email: '<EMAIL>',
        phone: '******-1002',
        nationalId: '23456789012',
        birthDate: '1990-07-22',
        address: '456 Training Avenue, Sports City, SC 12345',
        salary: '4800.00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Michael',
        surname: 'Davis',
        email: '<EMAIL>',
        phone: '******-1003',
        nationalId: '34567890123',
        birthDate: '1988-11-10',
        address: '123 Victory Road, Athletic Town, AT 67890',
        salary: '5200.00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Lisa',
        surname: 'Wilson',
        email: '<EMAIL>',
        phone: '******-1004',
        nationalId: '45678901234',
        birthDate: '1992-05-18',
        address: '789 Champion Circle, Athletic Town, AT 67890',
        salary: '4600.00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const instructors = await db.insert(schema.instructors).values(instructorsData).returning();
    console.log(`✅ Created ${instructors.length} instructors`);

    // 4. Seed Facilities
    console.log('🏟️ Seeding facilities...');
    const facilitiesData = [
      {
        tenantId: TENANT_ID,
        name: 'Main Football Field',
        type: 'field' as const,
        address: '123 Champions Boulevard, Sports City, SC 12345',
        totalCapacity: 500,
        currentlyOccupied: 0,
        length: '100.00',
        width: '64.00',
        dimensionUnit: 'meters' as const,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Indoor Basketball Court A',
        type: 'court' as const,
        address: '123 Champions Boulevard, Sports City, SC 12345',
        totalCapacity: 200,
        currentlyOccupied: 0,
        length: '28.00',
        width: '15.00',
        dimensionUnit: 'meters' as const,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Olympic Swimming Pool',
        type: 'pool' as const,
        address: '456 Winners Street, Athletic Town, AT 67890',
        totalCapacity: 150,
        currentlyOccupied: 0,
        length: '50.00',
        width: '25.00',
        dimensionUnit: 'meters' as const,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Tennis Court 1',
        type: 'court' as const,
        address: '456 Winners Street, Athletic Town, AT 67890',
        totalCapacity: 50,
        currentlyOccupied: 0,
        length: '23.77',
        width: '8.23',
        dimensionUnit: 'meters' as const,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const facilities = await db.insert(schema.facilities).values(facilitiesData).returning();
    console.log(`✅ Created ${facilities.length} facilities`);

    // 5. Seed Teams
    console.log('⚽ Seeding teams...');
    const teamsData = [
      {
        tenantId: TENANT_ID,
        name: 'Elite U-16 Football Team',
        schoolId: schools[0].id,
        branchId: branches[0].id, // Football
        instructorId: instructors[0].id, // John Smith
        description: 'Under-16 competitive football team',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Elite Basketball Seniors',
        schoolId: schools[0].id,
        branchId: branches[1].id, // Basketball
        instructorId: instructors[1].id, // Sarah Johnson
        description: 'Senior basketball team for competitive play',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Victory Swimming Squad',
        schoolId: schools[1].id,
        branchId: branches[3].id, // Swimming
        instructorId: instructors[2].id, // Michael Davis
        description: 'Competitive swimming team',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Victory Tennis Club',
        schoolId: schools[1].id,
        branchId: branches[2].id, // Tennis
        instructorId: instructors[3].id, // Lisa Wilson
        description: 'Tennis training and tournament team',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const teams = await db.insert(schema.teams).values(teamsData).returning();
    console.log(`✅ Created ${teams.length} teams`);

    // 6. Seed Training Schedules
    console.log('📅 Seeding training schedules...');
    const schedulesData = [
      {
        tenantId: TENANT_ID,
        teamId: teams[0].id, // Elite U-16 Football Team
        facilityId: facilities[0].id, // Main Football Field
        dayOfWeek: 1, // Monday
        startTime: '16:00',
        endTime: '18:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        teamId: teams[0].id, // Elite U-16 Football Team
        facilityId: facilities[0].id, // Main Football Field
        dayOfWeek: 3, // Wednesday
        startTime: '16:00',
        endTime: '18:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        teamId: teams[1].id, // Elite Basketball Seniors
        facilityId: facilities[1].id, // Indoor Basketball Court A
        dayOfWeek: 2, // Tuesday
        startTime: '19:00',
        endTime: '21:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        teamId: teams[1].id, // Elite Basketball Seniors
        facilityId: facilities[1].id, // Indoor Basketball Court A
        dayOfWeek: 4, // Thursday
        startTime: '19:00',
        endTime: '21:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        teamId: teams[2].id, // Victory Swimming Squad
        facilityId: facilities[2].id, // Olympic Swimming Pool
        dayOfWeek: 1, // Monday
        startTime: '06:00',
        endTime: '08:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        teamId: teams[2].id, // Victory Swimming Squad
        facilityId: facilities[2].id, // Olympic Swimming Pool
        dayOfWeek: 5, // Friday
        startTime: '06:00',
        endTime: '08:00',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const schedules = await db.insert(schema.trainingSchedules).values(schedulesData).returning();
    console.log(`✅ Created ${schedules.length} training schedules`);

    // 7. Seed Athletes
    console.log('🏃‍♂️ Seeding athletes...');
    const athletesData = [
      {
        tenantId: TENANT_ID,
        name: 'Alex',
        surname: 'Thompson',
        nationalId: '55555555555',
        birthDate: '2008-03-15',
        status: 'active' as const,
        balance: '-150.00', // Has 1 pending payment of 150.00
        parentName: 'Robert',
        parentSurname: 'Thompson',
        parentPhone: '******-2001',
        parentEmail: '<EMAIL>',
        parentAddress: '123 Family Street, Sports City, SC 12345',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Emma',
        surname: 'Rodriguez',
        nationalId: '66666666666',
        birthDate: '2007-08-22',
        status: 'active' as const,
        balance: '-140.00', // Has 1 overdue payment of 140.00
        parentName: 'Maria',
        parentSurname: 'Rodriguez',
        parentPhone: '******-2002',
        parentEmail: '<EMAIL>',
        parentAddress: '456 Home Avenue, Sports City, SC 12345',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'James',
        surname: 'Kim',
        nationalId: '77777777777',
        birthDate: '2009-12-10',
        status: 'active' as const,
        balance: '0.00', // All payments completed, no outstanding
        parentName: 'David',
        parentSurname: 'Kim',
        parentPhone: '******-2003',
        parentEmail: '<EMAIL>',
        parentAddress: '789 Residence Road, Athletic Town, AT 67890',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Sofia',
        surname: 'Anderson',
        nationalId: '88888888888',
        birthDate: '2008-06-18',
        status: 'active' as const,
        balance: '0.00', // All payments completed, no outstanding
        parentName: 'Jennifer',
        parentSurname: 'Anderson',
        parentPhone: '******-2004',
        parentEmail: '<EMAIL>',
        parentAddress: '321 Parent Lane, Athletic Town, AT 67890',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Marcus',
        surname: 'Brown',
        nationalId: '99999999999',
        birthDate: '2010-01-25',
        status: 'inactive' as const,
        balance: '0.00', // No payments, no outstanding debt
        parentName: 'William',
        parentSurname: 'Brown',
        parentPhone: '******-2005',
        parentEmail: '<EMAIL>',
        parentAddress: '654 Guardian Street, Sports City, SC 12345',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const athletes = await db.insert(schema.athletes).values(athletesData).returning();
    console.log(`✅ Created ${athletes.length} athletes`);

    // 8. Seed Payment Plans
    console.log('💳 Seeding payment plans...');
    const paymentPlansData = [
      {
        tenantId: TENANT_ID,
        name: 'Monthly Football Training',
        monthlyValue: '150.00',
        assignDay: 1, // Assign on 1st of month
        dueDay: 15, // Due on 15th of month
        status: 'active' as const,
        description: 'Monthly fee for football training sessions',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Monthly Basketball Training',
        monthlyValue: '140.00',
        assignDay: 1, // Assign on 1st of month
        dueDay: 15, // Due on 15th of month
        status: 'active' as const,
        description: 'Monthly fee for basketball training sessions',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Monthly Swimming Training',
        monthlyValue: '133.33',
        assignDay: 1, // Assign on 1st of month
        dueDay: 10, // Due on 10th of month
        status: 'active' as const,
        description: 'Monthly swimming training sessions',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Monthly Tennis Lessons',
        monthlyValue: '180.00',
        assignDay: 1, // Assign on 1st of month
        dueDay: 20, // Due on 20th of month
        status: 'active' as const,
        description: 'Monthly tennis lessons and court time',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const paymentPlans = await db.insert(schema.paymentPlans).values(paymentPlansData).returning();
    console.log(`✅ Created ${paymentPlans.length} payment plans`);

    // 10. Seed Expenses
    console.log('💸 Seeding expenses...');
    const expensesData = [
      {
        tenantId: TENANT_ID,
        amount: '5000.00',
        date: '2024-01-31',
        category: 'salary' as const,
        description: 'January salary for John Smith',
        instructorId: instructors[0].id,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        amount: '4800.00',
        date: '2024-01-31',
        category: 'salary' as const,
        description: 'January salary for Sarah Johnson',
        instructorId: instructors[1].id,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        amount: '1200.00',
        date: '2024-01-15',
        category: 'rent' as const,
        description: 'Monthly facility rent for Main Football Field',
        facilityId: facilities[0].id,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        amount: '800.00',
        date: '2024-01-15',
        category: 'rent' as const,
        description: 'Monthly facility rent for Basketball Court A',
        facilityId: facilities[1].id,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        amount: '450.00',
        date: '2024-01-10',
        category: 'equipment' as const,
        description: 'New football training equipment',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        amount: '350.00',
        date: '2024-01-20',
        category: 'insurance' as const,
        description: 'Monthly facility insurance',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const expenses = await db.insert(schema.expenses).values(expensesData).returning();
    console.log(`✅ Created ${expenses.length} expenses`);

    // 11. Seed Items
    console.log('🛍️ Seeding items...');
    const itemsData = [
      {
        tenantId: TENANT_ID,
        name: 'Elite Football Jersey',
        description: 'Official team jersey for Elite Sports Academy',
        price: '45.00',
        category: 'clothing' as const,
        stock: 50,
        image: 'https://images.pexels.com/photos/32276066/pexels-photo-32276066/free-photo-of-young-soccer-player-in-action-on-the-field.jpeg',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Professional Football',
        description: 'FIFA approved match football',
        price: '35.00',
        category: 'equipment' as const,
        stock: 25,
        image: 'https://images.pexels.com/photos/8455350/pexels-photo-8455350.jpeg',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Basketball Shoes',
        description: 'High-performance basketball shoes',
        price: '120.00',
        category: 'equipment' as const,
        stock: 30,
        image: 'https://images.pexels.com/photos/9252069/pexels-photo-9252069.png',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Swimming Goggles',
        description: 'Professional swimming goggles',
        price: '25.00',
        category: 'equipment' as const,
        stock: 40,
        image: 'https://images.pexels.com/photos/8028456/pexels-photo-8028456.jpeg',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Tennis Racket',
        description: 'Professional tennis racket',
        price: '150.00',
        category: 'equipment' as const,
        stock: 15,
        image: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        name: 'Sports Water Bottle',
        description: 'Branded sports water bottle',
        price: '12.00',
        category: 'accessories' as const,
        stock: 100,
        image: 'https://images.pexels.com/photos/5274535/pexels-photo-5274535.jpeg',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const items = await db.insert(schema.items).values(itemsData).returning();
    console.log(`✅ Created ${items.length} items`);

    // 12. Seed Item Purchases
    console.log('🛒 Seeding item purchases...');
    const itemPurchasesData = [
      {
        tenantId: TENANT_ID,
        itemId: items[0].id, // Elite Football Jersey
        athleteId: athletes[0].id, // Alex Thompson
        quantity: 1,
        totalPrice: '45.00',
        status: 'completed' as const,
        purchaseDate: '2024-01-15',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        itemId: items[2].id, // Basketball Shoes
        athleteId: athletes[1].id, // Emma Rodriguez
        quantity: 1,
        totalPrice: '120.00',
        status: 'completed' as const,
        purchaseDate: '2024-01-20',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        itemId: items[3].id, // Swimming Goggles
        athleteId: athletes[2].id, // James Kim
        quantity: 1,
        totalPrice: '25.00',
        status: 'completed' as const,
        purchaseDate: '2024-01-25',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        itemId: items[4].id, // Tennis Racket
        athleteId: athletes[3].id, // Sofia Anderson
        quantity: 1,
        totalPrice: '150.00',
        status: 'pending' as const,
        purchaseDate: '2024-02-01',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        itemId: items[5].id, // Sports Water Bottle
        athleteId: athletes[0].id, // Alex Thompson
        quantity: 2,
        totalPrice: '24.00',
        status: 'completed' as const,
        purchaseDate: '2024-01-30',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const itemPurchases = await db.insert(schema.itemPurchases).values(itemPurchasesData).returning();
    console.log(`✅ Created ${itemPurchases.length} item purchases`);

    // 13. Seed Junction Tables
    console.log('🔗 Seeding relationships...');

    // School-Branch relationships
    const schoolBranchesData = [
      {
        schoolId: schools[0].id, // Elite Sports Academy
        branchId: branches[0].id, // Football
      },
      {
        schoolId: schools[0].id, // Elite Sports Academy
        branchId: branches[1].id, // Basketball
      },
      {
        schoolId: schools[1].id, // Victory Athletic Center
        branchId: branches[2].id, // Tennis
      },
      {
        schoolId: schools[1].id, // Victory Athletic Center
        branchId: branches[3].id, // Swimming
      },
    ];

    await db.insert(schema.schoolBranches).values(schoolBranchesData);
    console.log(`✅ Created ${schoolBranchesData.length} school-branch relationships`);

    // Instructor-Branch relationships
    const instructorBranchesData = [
      {
        instructorId: instructors[0].id, // John Smith
        branchId: branches[0].id, // Football
      },
      {
        instructorId: instructors[1].id, // Sarah Johnson
        branchId: branches[1].id, // Basketball
      },
      {
        instructorId: instructors[2].id, // Michael Davis
        branchId: branches[3].id, // Swimming
      },
      {
        instructorId: instructors[3].id, // Lisa Wilson
        branchId: branches[2].id, // Tennis
      },
    ];

    await db.insert(schema.instructorBranches).values(instructorBranchesData);
    console.log(`✅ Created ${instructorBranchesData.length} instructor-branch relationships`);

    // Instructor-School relationships
    const instructorSchoolsData = [
      {
        instructorId: instructors[0].id, // John Smith
        schoolId: schools[0].id, // Elite Sports Academy
      },
      {
        instructorId: instructors[1].id, // Sarah Johnson
        schoolId: schools[0].id, // Elite Sports Academy
      },
      {
        instructorId: instructors[2].id, // Michael Davis
        schoolId: schools[1].id, // Victory Athletic Center
      },
      {
        instructorId: instructors[3].id, // Lisa Wilson
        schoolId: schools[1].id, // Victory Athletic Center
      },
    ];

    await db.insert(schema.instructorSchools).values(instructorSchoolsData);
    console.log(`✅ Created ${instructorSchoolsData.length} instructor-school relationships`);

    // Athlete-Team relationships
    const athleteTeamsData = [
      {
        athleteId: athletes[0].id, // Alex Thompson
        teamId: teams[0].id, // Elite U-16 Football Team
        joinedAt: '2024-01-01',
      },
      {
        athleteId: athletes[1].id, // Emma Rodriguez
        teamId: teams[1].id, // Elite Basketball Seniors
        joinedAt: '2024-01-01',
      },
      {
        athleteId: athletes[2].id, // James Kim
        teamId: teams[2].id, // Victory Swimming Squad
        joinedAt: '2024-01-01',
      },
      {
        athleteId: athletes[3].id, // Sofia Anderson
        teamId: teams[3].id, // Victory Tennis Club
        joinedAt: '2024-01-01',
      },
      {
        athleteId: athletes[4].id, // Marcus Brown
        teamId: teams[0].id, // Elite U-16 Football Team
        joinedAt: '2024-01-01',
        leftAt: '2024-01-31', // Left the team
      },
    ];

    await db.insert(schema.athleteTeams).values(athleteTeamsData);
    console.log(`✅ Created ${athleteTeamsData.length} athlete-team relationships`);

    // Athlete Payment Plan assignments
    const athletePaymentPlansData = [
      {
        tenantId: TENANT_ID,
        athleteId: athletes[0].id, // John Doe
        planId: paymentPlans[0].id, // Monthly Football Training
        teamId: teams[0].id, // Team Alpha
        status: 'active' as const,
        assignedDate: '2024-01-01',
        lastPaymentDate: null,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[1].id, // Jane Smith
        planId: paymentPlans[1].id, // Monthly Basketball Training
        teamId: teams[1].id, // Team Beta
        status: 'active' as const,
        assignedDate: '2024-01-01',
        lastPaymentDate: null,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[2].id, // Mike Johnson
        planId: paymentPlans[2].id, // Monthly Swimming Training
        teamId: teams[2].id, // Team Gamma
        status: 'active' as const,
        assignedDate: '2024-01-01',
        lastPaymentDate: null,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[3].id, // Sarah Wilson
        planId: paymentPlans[3].id, // Monthly Tennis Lessons
        teamId: teams[3].id, // Team Delta
        status: 'active' as const,
        assignedDate: '2024-01-01',
        lastPaymentDate: null,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const athletePaymentPlans = await db.insert(schema.athletePaymentPlans).values(athletePaymentPlansData).returning();
    console.log(`✅ Created ${athletePaymentPlans.length} athlete payment plan assignments`);

    // 9. Seed Payments
    console.log('💰 Seeding payments...');
    const paymentsData = [
      {
        tenantId: TENANT_ID,
        athleteId: athletes[0].id, // Alex Thompson
        athletePaymentPlanId: athletePaymentPlans[0].id, // Monthly Football Training assignment
        amount: '150.00',
        date: '2024-01-15',
        dueDate: '2024-01-15',
        status: 'completed' as const,
        type: 'fee' as const,
        description: 'January football training fee',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[1].id, // Emma Rodriguez
        athletePaymentPlanId: athletePaymentPlans[1].id, // Monthly Basketball Training assignment
        amount: '140.00',
        date: '2024-01-10',
        dueDate: '2024-01-15',
        status: 'completed' as const,
        type: 'fee' as const,
        description: 'January basketball training fee',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[1].id, // Emma Rodriguez
        athletePaymentPlanId: athletePaymentPlans[1].id, // Monthly Basketball Training assignment
        amount: '140.00',
        date: '2024-02-10',
        dueDate: '2024-02-15',
        status: 'overdue' as const,
        type: 'fee' as const,
        description: 'February basketball training fee',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[2].id, // James Kim
        athletePaymentPlanId: athletePaymentPlans[2].id, // Monthly Swimming Training assignment
        amount: '133.33',
        date: '2024-01-01',
        dueDate: '2024-01-10',
        status: 'completed' as const,
        type: 'fee' as const,
        description: 'January swimming training fee',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[3].id, // Sofia Anderson
        athletePaymentPlanId: athletePaymentPlans[3].id, // Monthly Tennis Lessons assignment
        amount: '180.00',
        date: '2024-01-20',
        dueDate: '2024-01-20',
        status: 'completed' as const,
        type: 'fee' as const,
        description: 'January tennis lessons',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        tenantId: TENANT_ID,
        athleteId: athletes[0].id, // Alex Thompson
        athletePaymentPlanId: athletePaymentPlans[0].id, // Monthly Football Training assignment
        amount: '150.00',
        date: '2024-02-01',
        dueDate: '2024-02-15',
        status: 'pending' as const,
        type: 'fee' as const,
        description: 'February football training fee',
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const payments = await db.insert(schema.payments).values(paymentsData).returning();
    console.log(`✅ Created ${payments.length} payments`);

    // 12. Seed SMS Pricing Tiers (global - not tenant-specific)
    console.log('💰 Seeding SMS pricing tiers...');
    const smsPricingTiersData = [
      {
        name: 'starter',
        description: 'Starter tier - up to 100 credits',
        minCredits: 1,
        maxCredits: 100,
        pricePerCredit: 10, // $0.10 per credit
        currency: 'USD',
        isActive: true,
        sortOrder: 1,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'standard',
        description: 'Standard tier - 101-500 credits',
        minCredits: 101,
        maxCredits: 500,
        pricePerCredit: 8, // $0.08 per credit
        currency: 'USD',
        isActive: true,
        sortOrder: 2,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'professional',
        description: 'Professional tier - 501-1000 credits',
        minCredits: 501,
        maxCredits: 1000,
        pricePerCredit: 6, // $0.06 per credit
        currency: 'USD',
        isActive: true,
        sortOrder: 3,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
      {
        name: 'enterprise',
        description: 'Enterprise tier - 1000+ credits',
        minCredits: 1001,
        maxCredits: null, // unlimited
        pricePerCredit: 5, // $0.05 per credit
        currency: 'USD',
        isActive: true,
        sortOrder: 4,
        createdBy: USER_ID,
        updatedBy: USER_ID,
      },
    ];

    const smsPricingTiers = await db.insert(schema.smsPricingTiers).values(smsPricingTiersData).returning();
    console.log(`✅ Created ${smsPricingTiers.length} SMS pricing tiers`);

    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - ${branches.length} branches`);
    console.log(`   - ${schools.length} schools`);
    console.log(`   - ${instructors.length} instructors`);
    console.log(`   - ${facilities.length} facilities`);
    console.log(`   - ${teams.length} teams`);
    console.log(`   - ${schedules.length} training schedules`);
    console.log(`   - ${athletes.length} athletes`);
    console.log(`   - ${paymentPlans.length} payment plans`);
    console.log(`   - ${payments.length} payments`);
    console.log(`   - ${expenses.length} expenses`);
    console.log(`   - ${items.length} items`);
    console.log(`   - ${itemPurchases.length} item purchases`);
    console.log(`   - ${smsPricingTiers.length} SMS pricing tiers`);
    console.log(`   - Multiple relationship records`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

// Run the seeding function
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seedDatabase;
