# Image Caching System

The SecureImage component now includes a sophisticated client-side caching mechanism to reduce server requests and improve performance for authenticated images.

## Overview

The caching system consists of three main components:

1. **ImageCache** (`lib/image-cache.ts`) - Core caching engine
2. **useCachedImage** (`hooks/use-cached-image.ts`) - React hook for image caching
3. **SecureImage** (`components/ui/secure-image.tsx`) - Enhanced component with caching support

## Features

### 🚀 Performance Benefits
- **Reduced Server Load**: Cached images don't require repeated API calls
- **Faster Loading**: Cached images load instantly from memory
- **Bandwidth Savings**: No re-downloading of previously loaded images

### 🔒 Security & Isolation
- **Tenant Isolation**: Each tenant's images are cached separately
- **Authentication Aware**: Only caches authenticated requests
- **Secure Storage**: Uses blob URLs that are automatically cleaned up

### 🧠 Smart Memory Management
- **Automatic Expiration**: Images expire after 30 minutes by default
- **Size Limits**: Maximum 100 images cached at once
- **Memory Cleanup**: Automatic cleanup of expired entries and blob URLs
- **LRU Eviction**: Oldest images are removed when cache is full

## Usage

### Basic Usage

The caching is enabled by default for all SecureImage components:

```tsx
import { SecureImage } from '@/components/ui/secure-image';

function MyComponent() {
  return (
    <SecureImage
      src="/api/secure-images/tenant-123/image.jpg"
      alt="My Image"
      width={200}
      height={200}
    />
  );
}
```

### Disabling Cache

You can disable caching for specific images:

```tsx
<SecureImage
  src="/api/secure-images/tenant-123/image.jpg"
  alt="My Image"
  enableCache={false} // Disable caching
  width={200}
  height={200}
/>
```

### Cache Management

Use the utility functions for cache management:

```tsx
import {
  clearTenantImageCache,
  clearAllImageCache,
  getImageCacheStats,
  invalidateImage,
  preloadImages,
} from '@/lib/image-cache-utils';

// Clear cache for current tenant
clearTenantImageCache('tenant-123');

// Clear all cached images
clearAllImageCache();

// Get cache statistics
const stats = getImageCacheStats();
console.log(`Cache size: ${stats.size}/${stats.maxSize}`);

// Invalidate specific image
invalidateImage('/api/secure-images/tenant-123/image.jpg', 'tenant-123');

// Preload images for better performance
await preloadImages([
  { src: '/api/secure-images/tenant-123/image1.jpg', tenantId: 'tenant-123' },
  { src: '/api/secure-images/tenant-123/image2.jpg', tenantId: 'tenant-123' },
]);
```

## How It Works

### 1. Cache Check
When a SecureImage component loads:
1. Check if image is already cached
2. If cached and valid, use blob URL immediately
3. If not cached, use original src and let browser handle authentication

### 2. Image Loading & Caching
For cache misses:
1. Image loads normally through browser (with authentication cookies)
2. On successful load, image is converted to blob and cached
3. Subsequent loads use the cached blob URL

### 3. Cache Storage
Images are stored as:
```typescript
{
  blobUrl: string;        // blob:// URL for the image
  timestamp: number;      // When cached
  tenantId: string;       // Tenant isolation
  originalSrc: string;    // Original image URL
}
```

### 4. Automatic Cleanup
- **Expiration**: Images older than 30 minutes are removed
- **Size Limit**: When cache exceeds 100 images, oldest are removed
- **Memory Management**: Blob URLs are properly revoked to prevent memory leaks
- **Page Unload**: All cache is cleared when page unloads

## Configuration

### Cache Settings

Default settings can be modified in `lib/image-cache.ts`:

```typescript
export const imageCache = new ImageCache({
  maxAge: 30 * 60 * 1000, // 30 minutes
  maxSize: 100,           // 100 images
});
```

### Hook Options

The `useCachedImage` hook accepts options:

```typescript
const { src, isLoading, error, isFromCache } = useCachedImage(originalSrc, {
  enabled: true,    // Enable/disable caching
  priority: false,  // High priority loading
});
```

## Performance Considerations

### Memory Usage
- Each cached image uses memory for the blob data
- Automatic cleanup prevents excessive memory usage
- Monitor cache size with `getImageCacheStats()`

### Network Efficiency
- First load: Normal network request + small caching overhead
- Subsequent loads: Instant from cache
- Best for images that are viewed multiple times
- No interference with browser's authentication mechanisms

### Cache Invalidation
- Images automatically expire after 30 minutes
- Manual invalidation available for immediate updates
- Tenant switching clears relevant cache

## Debugging

### Development Tools

In development mode, use the debug utilities:

```typescript
import { useImageCacheDebug } from '@/lib/image-cache-utils';

function DebugComponent() {
  const debug = useImageCacheDebug();
  
  if (debug) {
    console.log('Cache stats:', debug.stats);
    // debug.clearAll(), debug.clearTenant(), etc.
  }
}
```

### Visual Indicators

Cached images show a tooltip "Loaded from cache" when hovered, making it easy to verify caching is working.

## Browser Compatibility

The caching system uses modern browser APIs:
- **Blob URLs**: Supported in all modern browsers
- **Fetch API**: Used for authenticated requests
- **Map**: For efficient cache storage

## Security Notes

- Cache is isolated per tenant for security
- Blob URLs are scoped to the current page
- No persistent storage - cache is memory-only
- Authentication is required for all cached requests
- Cache is cleared on page unload for security
