import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';

export class PaymentService extends BaseService {
  constructor() {
    super('PaymentService');
  }

  async getPayments(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getPayments',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPayments(resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentsPaginated(
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: Record<string, string>;
    },
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getPaymentsPaginated',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentsPaginated(
          options.page,
          options.limit,
          options.search,
          options.sortBy || 'date',
          options.sortOrder || 'desc',
          options.filters || {}
        );
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentById(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getPaymentById',
      'Payment',
      id,
      () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return TenantAwareDB.getPaymentById(id, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async createPayment(
    data: {
      athleteId: string;
      athletePaymentPlanId?: string | null;
      amount: string;
      date: string;
      dueDate: string;
      status: "pending" | "completed" | "overdue" | "cancelled";
      type: "fee" | "equipment" | "other";
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.createPayment(data, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async updatePayment(
    id: string,
    data: {
      athleteId?: string;
      athletePaymentPlanId?: string | null;
      amount?: string;
      date?: string;
      dueDate?: string;
      status?: "pending" | "completed" | "overdue" | "cancelled";
      type?: "fee" | "equipment" | "other";
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'updatePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePayment(id, data, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async deletePayment(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeDatabaseOperation(
      'deletePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        await TenantAwareDB.deletePayment(id, resolvedTenantId);
        return true;
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async processPayment(
    id: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePayment(id, { 
          status: "completed",
          date: new Date().toISOString().split('T')[0]
        }, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }
}
