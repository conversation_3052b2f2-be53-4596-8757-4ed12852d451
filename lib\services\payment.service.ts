import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';
import { updateAthleteBalance } from '../balance-calculator';

export class PaymentService extends BaseService {
  constructor() {
    super('PaymentService');
  }

  async getPayments(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getPayments',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPayments(resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentsPaginated(
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: Record<string, string>;
    },
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getPaymentsPaginated',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentsPaginated(
          options.page,
          options.limit,
          options.search,
          options.sortBy || 'date',
          options.sortOrder || 'desc',
          options.filters || {}
        );
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentById(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getPaymentById',
      'Payment',
      id,
      () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return TenantAwareDB.getPaymentById(id, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async createPayment(
    data: {
      athleteId: string;
      athletePaymentPlanId?: string | null;
      amount: string;
      date: string;
      dueDate: string;
      status: "pending" | "completed" | "overdue" | "cancelled";
      type: "fee" | "equipment" | "other";
      method: "cash" | "bank_transfer" | "credit_card" | null;
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.createPayment(data, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async updatePayment(
    id: string,
    data: {
      athleteId?: string;
      athletePaymentPlanId?: string | null;
      amount?: string;
      date?: string;
      dueDate?: string;
      status?: "pending" | "completed" | "overdue" | "cancelled";
      type?: "fee" | "equipment" | "other";
      method?: "cash" | "bank_transfer" | "credit_card" | null;
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'updatePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePayment(id, data, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async deletePayment(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string,
    userId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeDatabaseOperation(
      'deletePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Delete payment and get details for balance update
        const deleteResult = await TenantAwareDB.deletePayment(id, resolvedTenantId);
        const { paymentDetails } = deleteResult;

        // Update athlete balance if the deleted payment affects the balance
        // Only pending and overdue payments affect the athlete's outstanding balance
        // Cancelled payments don't affect balance, completed payments don't affect balance
        if (paymentDetails.status === 'pending' || paymentDetails.status === 'overdue') {
          await updateAthleteBalance(
            paymentDetails.athleteId,
            resolvedTenantId!,
            userIdBigInt
          );
        }

        return true;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async processPayment(
    id: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePayment(id, { 
          status: "completed",
          date: new Date().toISOString().split('T')[0]
        }, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }
}
