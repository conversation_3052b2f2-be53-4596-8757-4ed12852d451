"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Trash2, School as SchoolIcon } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { SecureImage } from "@/components/ui/secure-image";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { School } from "@/lib/types";
import { deleteSchool } from "@/lib/actions";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

interface SchoolListClientProps {
  schools: School[];
}

export function SchoolListClient({ schools }: SchoolListClientProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const { toast } = useToast();
  const [deletingSchool, setDeletingSchool] = useState<string | null>(null);

  const handleDeleteSchool = async (schoolId: string) => {
    try {
      setDeletingSchool(schoolId);
      const result = await deleteSchool(schoolId);
      if(result.success){
        toast({
          title: t('schools.messages.deleteSuccess'),
          description: t('schools.messages.deleteSuccessDescription'),
        });

        // Refresh the page to show updated school list
        router.refresh();
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'schools.messages.deleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to delete school:", error);
      toast({
        title: t('schools.messages.deleteError'),
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    } finally {
      setDeletingSchool(null);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {schools.map((school) => (
        <Card key={school.id} className="overflow-hidden">
          <div className="h-48 relative bg-muted">
            <SecureImage
              src={school.logo || ""}
              alt={school.name}
              fill
              className="object-contain p-4"
              placeholderIcon={SchoolIcon}
            />
          </div>
          <CardHeader>
            <CardTitle>{school.name}</CardTitle>
            <CardDescription>{t('schools.details.foundedYear')}: {school.foundedYear}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 mb-4">
              {school.branches.map((branch) => (
                <span 
                  key={branch.id} 
                  className="bg-secondary text-secondary-foreground text-xs rounded-full px-2 py-1"
                >
                  {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                </span>
              ))}
            </div>
            <p className="text-sm text-muted-foreground">
              {t('schools.messages.instructorCount', { count: school.instructors.length })}
            </p>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button variant="outline" asChild>
                <Link href={`/schools/${school.id}`}>{t('schools.actions.viewDetails')}</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href={`/schools/${school.id}/edit`}>{t('common.actions.edit')}</Link>
              </Button>
            </div>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  disabled={deletingSchool === school.id}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>{t('schools.actions.deleteConfirmTitle')}</AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('schools.actions.deleteConfirmMessage', { name: school.name })}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => handleDeleteSchool(school.id)}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    disabled={deletingSchool === school.id}
                  >
                    {deletingSchool === school.id ? t('common.actions.deleting') : t('common.actions.delete')}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardFooter>
        </Card>
      ))}
      
      <Link href="/schools/new" className="block">
        <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
              <PlusCircle className="h-12 w-12 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground text-center">
              {t('schools.actions.addNew')}
            </p>
          </CardContent>
        </Card>
      </Link>
    </div>
  );
}
