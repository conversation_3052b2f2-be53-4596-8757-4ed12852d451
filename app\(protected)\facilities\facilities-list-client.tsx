"use client";

import React, { useState } from "react";
import Link from "next/link";
import { PlusCircle, MapPin, <PERSON>cil, Trash, MoreHorizontal, Users, Ruler, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Facility } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { deleteFacility } from "@/lib/db/actions/facilities";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface FacilitiesListClientProps {
  initialData: Facility[];
}

function FacilityCard({ facility }: { facility: Facility }) {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  
  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteFacility(facility.id);
      toast.success(t('common.messages.deleteSuccess'));
      router.refresh();
    } catch (error) {
      console.error("Error deleting facility:", error);
      toast.error(t('common.messages.deleteError'));
    } finally {
      setIsDeleting(false);
    }
  };

  const getCapacityDisplay = () => {
    const capacity = facility.capacity?.total || facility.totalCapacity;
    return capacity ? `${capacity} ${t('facilities.details.people')}` : t('common.notAssigned');
  };

  const getDimensionsDisplay = () => {
    // Try the computed capacity.dimensions first, then fallback to direct properties
    if (facility.capacity?.dimensions?.length && facility.capacity?.dimensions?.width) {
      return `${facility.capacity.dimensions.length} × ${facility.capacity.dimensions.width} ${facility.capacity.dimensions.unit}`;
    } else if (facility.length && facility.width && facility.dimensionUnit) {
      return `${facility.length} × ${facility.width} ${facility.dimensionUnit}`;
    }
    return null;
  };

  const totalCapacity = facility.capacity?.total ?? facility.totalCapacity ?? 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">{facility.name}</CardTitle>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">{t('facilities.actions.openMenu')}</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={`/facilities/${facility.id}`} className="flex items-center">
                <Eye className="mr-2 h-4 w-4" />
                {t('facilities.actions.view')}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={`/facilities/${facility.id}/edit`} className="flex items-center">
                <Pencil className="mr-2 h-4 w-4" />
                {t('facilities.actions.edit')}
              </Link>
            </DropdownMenuItem>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  className="text-destructive focus:text-destructive"
                  onSelect={(e) => e.preventDefault()}
                >
                  <Trash className="mr-2 h-4 w-4" />
                  {t('facilities.actions.delete')}
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t('facilities.deleteDialog.title')}
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {t('facilities.deleteDialog.description')}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>
                    {t('common.actions.cancel')}
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isDeleting 
                      ? t('common.actions.deleting') 
                      : t('common.actions.delete')
                    }
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="mr-2 h-4 w-4" />
            {facility.address}
          </div>
          <div className="flex items-center">
            <span className="text-sm font-medium capitalize bg-secondary text-secondary-foreground rounded-full px-2 py-1">
              {t(`facilities.types.${facility.type}`)}
            </span>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-sm">
            <div className="flex items-center">
              <Users className="mr-2 h-4 w-4" />
              <span className="text-muted-foreground">
                {getCapacityDisplay()}
              </span>
            </div>
            {getDimensionsDisplay() && (
              <div className="flex items-center text-muted-foreground">
                <Ruler className="mr-2 h-4 w-4" />
                {getDimensionsDisplay()}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function FacilitiesListClient({ initialData }: FacilitiesListClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('facilities.title')}</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {initialData.map((facility) => (
          <FacilityCard key={facility.id} facility={facility} />
        ))}
        
        <Link href="/facilities/new" className="block">
          <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
                <PlusCircle className="h-12 w-12 text-muted-foreground" />
              </div>
              <p className="text-sm text-muted-foreground text-center">
                {t('facilities.actions.addNew')}
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
