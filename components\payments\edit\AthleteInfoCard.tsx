import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Phone, Mail, CreditCard } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface Athlete {
  id: string;
  name: string;
  surname: string;
  parentEmail?: string | null;
  parentPhone?: string | null;
  nationalId?: string | null;
}

interface AthleteInfoCardProps {
  athlete: Athlete | null;
}

export function AthleteInfoCard({ athlete }: AthleteInfoCardProps) {
  const { t } = useSafeTranslation();

  if (!athlete) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {t("payments.details.athleteInfo")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="font-medium">{t("athletes.details.fullName")}</span>
          <span>{athlete.name} {athlete.surname}</span>
        </div>
        
        {athlete.nationalId && (
          <div className="flex items-center justify-between">
            <span className="font-medium">{t("athletes.details.nationalId")}</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <CreditCard className="h-3 w-3" />
              {athlete.nationalId}
            </Badge>
          </div>
        )}
        
        {athlete.parentPhone && (
          <div className="flex items-center justify-between">
            <span className="font-medium">{t("athletes.details.parentPhone")}</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {athlete.parentPhone}
            </Badge>
          </div>
        )}
        
        {athlete.parentEmail && (
          <div className="flex items-center justify-between">
            <span className="font-medium">{t("athletes.details.parentEmail")}</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <Mail className="h-3 w-3" />
              {athlete.parentEmail}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
