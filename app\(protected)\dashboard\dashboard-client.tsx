"use client";

import { Suspense } from "react";
import { SkeletonDashboardCard } from "@/components/ui/skeleton-dashboard-card";
import { DashboardWidgets } from "@/components/dashboard/dashboard-widgets";
import {
  FinancialOverviewChart,
  ExpenseBreakdownChart,
  IncomeSourcesChart,
  OverduePaymentsList
} from "@/components/dashboard/charts";

interface DashboardClientProps {
  financialData: any;
  overdueAthletes: any[];
}

export default function DashboardClient({ financialData, overdueAthletes }: DashboardClientProps) {
  return (
    <div className="space-y-6">
      <Suspense fallback={
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
          <SkeletonDashboardCard />
        </div>
      }>
        <DashboardWidgets />
      </Suspense>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="col-span-1 md:col-span-2">
          <FinancialOverviewChart data={financialData} />
        </div>
        
        <div className="space-y-6">
          <OverduePaymentsList athletes={overdueAthletes} />
          
          <div className="grid grid-cols-1 gap-6">
            <ExpenseBreakdownChart data={financialData} />
            <IncomeSourcesChart data={financialData} />
          </div>
        </div>
      </div>
    </div>
  );
}
