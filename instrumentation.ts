export async function register() {
  // This function is called when the server starts up
  // It only runs once per server instance and not during build
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    console.log('🚀 Server instrumentation: Initializing payment scheduler...');
    
    // Dynamically import to avoid loading database modules during build
    const { initPaymentScheduler } = await import('@/lib/scheduler/init');
    initPaymentScheduler();
  }
}
