import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreatePaymentPlanData {
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}

export interface UpdatePaymentPlanData {
  name?: string;
  monthlyValue?: string;
  assignDay?: number;
  dueDay?: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}

export class PaymentPlanService extends BaseService {
  constructor() {
    super('PaymentPlanService');
  }

  /**
   * Get all payment plans
   */
  async getPaymentPlans(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getPaymentPlans',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getPaymentPlans(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlans',
      }
    );
  }

  /**
   * Get payment plan by ID
   */
  async getPaymentPlanById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getPaymentPlanById',
      'PaymentPlan',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getPaymentPlanById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlan',
      }
    );
  }

  /**
   * Create a new payment plan
   */
  async createPaymentPlan(
    data: CreatePaymentPlanData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'Payment plan name is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 3 || data.name.length > 100) {
          return { field: 'name', message: 'Payment plan name must be between 3 and 100 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (!data.monthlyValue || data.monthlyValue.trim().length === 0) {
          return { field: 'monthlyValue', message: 'Monthly value is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const value = parseFloat(data.monthlyValue);
        if (isNaN(value) || value <= 0) {
          return { field: 'monthlyValue', message: 'Monthly value must be a positive number', code: 'INVALID_FORMAT' };
        }
        if (value > 100000) {
          return { field: 'monthlyValue', message: 'Monthly value cannot exceed 100,000', code: 'INVALID_RANGE' };
        }
        return null;
      },
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (data.assignDay < 1 || data.assignDay > 31) {
          return { field: 'assignDay', message: 'Assign day must be between 1 and 31', code: 'INVALID_RANGE' };
        }
        return null;
      },
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (data.dueDay < 1 || data.dueDay > 31) {
          return { field: 'dueDay', message: 'Due day must be between 1 and 31', code: 'INVALID_RANGE' };
        }
        return null;
      },
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (data.status && !['active', 'inactive'].includes(data.status)) {
          return { field: 'status', message: 'Status must be either active or inactive', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreatePaymentPlanData): ValidationError | null => {
        if (!data.selectedBranches || data.selectedBranches.length === 0) {
          return { field: 'selectedBranches', message: 'At least one branch must be selected', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createPaymentPlan',
      data,
      validationFunctions,
      async (validatedData) => {
        // Check for duplicate plan name
        const existingPlan = await this.findPaymentPlanByName(validatedData.name, tenantId);
        if (existingPlan) {
          throw new BusinessRuleError(
            'unique_plan_name',
            `Payment plan with name '${validatedData.name}' already exists`,
            undefined,
            'A payment plan with this name already exists. Please choose a different name.'
          );
        }

        // Validate that assign day is before due day
        if (validatedData.assignDay >= validatedData.dueDay) {
          throw new BusinessRuleError(
            'assign_before_due',
            'Assign day must be before due day',
            undefined,
            'The assignment day must be earlier than the due day in the month.'
          );
        }

        // Create the payment plan
        const planData = {
          name: validatedData.name,
          monthlyValue: parseFloat(validatedData.monthlyValue).toString(),
          assignDay: validatedData.assignDay,
          dueDay: validatedData.dueDay,
          status: validatedData.status || 'active',
          description: validatedData.description || null,
        };

        const result = await TenantAwareDB.createPaymentPlan(planData);

        // Handle branch relationships
        if (validatedData.selectedBranches && validatedData.selectedBranches.length > 0) {
          await TenantAwareDB.updatePaymentPlanBranches(result.id, validatedData.selectedBranches);
        }

        return result;
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlan',
        metadata: { operation: 'create' },
      }
    );
  }

  /**
   * Update a payment plan
   */
  async updatePaymentPlan(
    id: string,
    data: UpdatePaymentPlanData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdatePaymentPlanData): ValidationError | null => {
        if (data.name !== undefined) {
          if (!data.name || data.name.trim().length === 0) {
            return { field: 'name', message: 'Payment plan name cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.name.length < 3 || data.name.length > 100) {
            return { field: 'name', message: 'Payment plan name must be between 3 and 100 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdatePaymentPlanData): ValidationError | null => {
        if (data.monthlyValue !== undefined) {
          if (!data.monthlyValue || data.monthlyValue.trim().length === 0) {
            return { field: 'monthlyValue', message: 'Monthly value cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const value = parseFloat(data.monthlyValue);
          if (isNaN(value) || value <= 0) {
            return { field: 'monthlyValue', message: 'Monthly value must be a positive number', code: 'INVALID_FORMAT' };
          }
          if (value > 100000) {
            return { field: 'monthlyValue', message: 'Monthly value cannot exceed 100,000', code: 'INVALID_RANGE' };
          }
        }
        return null;
      },
      (data: UpdatePaymentPlanData): ValidationError | null => {
        if (data.assignDay !== undefined && (data.assignDay < 1 || data.assignDay > 31)) {
          return { field: 'assignDay', message: 'Assign day must be between 1 and 31', code: 'INVALID_RANGE' };
        }
        return null;
      },
      (data: UpdatePaymentPlanData): ValidationError | null => {
        if (data.dueDay !== undefined && (data.dueDay < 1 || data.dueDay > 31)) {
          return { field: 'dueDay', message: 'Due day must be between 1 and 31', code: 'INVALID_RANGE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updatePaymentPlan',
      data,
      validationFunctions,
      async (validatedData) => {
        // Check if payment plan exists
        const existingPlan = await this.getPaymentPlanById(id, userId, tenantId);
        if (!existingPlan.success) {
          throw new NotFoundError('PaymentPlan', id);
        }

        // Validate business rules
        const currentPlan = existingPlan.data;
        const newAssignDay = validatedData.assignDay ?? currentPlan.assignDay;
        const newDueDay = validatedData.dueDay ?? currentPlan.dueDay;

        if (newAssignDay >= newDueDay) {
          throw new BusinessRuleError(
            'assign_before_due',
            'Assign day must be before due day',
            undefined,
            'The assignment day must be earlier than the due day in the month.'
          );
        }

        // Check for duplicate name if name is being updated
        if (validatedData.name && validatedData.name !== currentPlan.name) {
          const planWithSameName = await this.findPaymentPlanByName(validatedData.name, tenantId);
          if (planWithSameName && planWithSameName.id !== id) {
            throw new BusinessRuleError(
              'unique_plan_name',
              `Another payment plan with name '${validatedData.name}' already exists`,
              undefined,
              'A payment plan with this name already exists. Please choose a different name.'
            );
          }
        }

        // Prepare update data
        const updateData: any = {};
        if (validatedData.name) updateData.name = validatedData.name;
        if (validatedData.monthlyValue) updateData.monthlyValue = parseFloat(validatedData.monthlyValue).toString();
        if (validatedData.assignDay !== undefined) updateData.assignDay = validatedData.assignDay;
        if (validatedData.dueDay !== undefined) updateData.dueDay = validatedData.dueDay;
        if (validatedData.status) updateData.status = validatedData.status;
        if (validatedData.description !== undefined) updateData.description = validatedData.description;

        const result = await TenantAwareDB.updatePaymentPlan(id, updateData);

        // Handle branch relationships if provided
        if (validatedData.selectedBranches !== undefined) {
          await TenantAwareDB.updatePaymentPlanBranches(id, validatedData.selectedBranches);
        }

        return result;
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlan',
        metadata: { operation: 'update', paymentPlanId: id },
      }
    );
  }

  /**
   * Delete a payment plan
   */
  async deletePaymentPlan(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deletePaymentPlan',
      async () => {
        // Check if payment plan exists
        const existingPlan = await this.getPaymentPlanById(id, userId, tenantId);
        if (!existingPlan.success) {
          throw new NotFoundError('PaymentPlan', id);
        }

        // Check for business rule violations
        const hasActiveAssignments = await this.checkPaymentPlanHasActiveAssignments(id, tenantId);
        if (hasActiveAssignments) {
          throw new BusinessRuleError(
            'cannot_delete_plan_with_assignments',
            'Cannot delete payment plan with active assignments',
            undefined,
            'This payment plan cannot be deleted because it has active assignments to athletes. Please remove all assignments first.'
          );
        }

        await TenantAwareDB.deletePaymentPlan(id);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlan',
        metadata: { operation: 'delete', paymentPlanId: id },
      }
    );
  }

  /**
   * Validate payment plan data (for use in other operations)
   */
  async validatePaymentPlanData(
    data: Partial<CreatePaymentPlanData>
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    if (!data.name) {
      errors.push('Payment plan name is required');
    } else if (data.name.length < 3 || data.name.length > 100) {
      errors.push('Payment plan name must be between 3 and 100 characters');
    }

    if (!data.monthlyValue) {
      errors.push('Monthly value is required');
    } else {
      const value = parseFloat(data.monthlyValue);
      if (isNaN(value) || value <= 0) {
        errors.push('Monthly value must be a positive number');
      }
    }

    if (data.assignDay !== undefined && (data.assignDay < 1 || data.assignDay > 31)) {
      errors.push('Assign day must be between 1 and 31');
    }

    if (data.dueDay !== undefined && (data.dueDay < 1 || data.dueDay > 31)) {
      errors.push('Due day must be between 1 and 31');
    }

    if (data.assignDay !== undefined && data.dueDay !== undefined && data.assignDay >= data.dueDay) {
      errors.push('Assign day must be before due day');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get payments by plan ID
   */
  async getPaymentsByPlanId(
    planId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getPaymentsByPlanId',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getPaymentsByPlanId(planId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlanPayments',
        metadata: { planId },
      }
    );
  }

  /**
   * Get payment plan statistics
   */
  async getPaymentPlanStats(
    planId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getPaymentPlanStats',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getPaymentPlanStats(planId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlanStats',
        metadata: { planId },
      }
    );
  }

  /**
   * Get count of assigned athletes for a payment plan
   */
  async getPaymentPlanAssignedAthletesCount(
    planId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<number>> {
    return this.executeOperation(
      'getPaymentPlanAssignedAthletesCount',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Use direct database access for count query
        const { db } = await import('@/src/db');
        const { athletePaymentPlans } = await import('@/src/db/schema');
        const { eq, and, count } = await import('drizzle-orm');
        
        const result = await db
          .select({ count: count() })
          .from(athletePaymentPlans)
          .where(
            and(
              eq(athletePaymentPlans.planId, planId),
              eq(athletePaymentPlans.isActive, true)
            )
          );

        return result[0]?.count || 0;
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlanAssignedCount',
        metadata: { planId },
      }
    );
  }

  /**
   * Private helper methods
   */
  private async findPaymentPlanByName(
    name: string,
    tenantId?: string
  ): Promise<any | null> {
    try {
      // This would need to be implemented in TenantAwareDB
      return null; // Placeholder
    } catch (error) {
      return null;
    }
  }

  private async checkPaymentPlanHasActiveAssignments(
    planId: string,
    tenantId?: string
  ): Promise<boolean> {
    try {
      // This would need to be implemented to check for active assignments
      return false; // Placeholder
    } catch (error) {
      return false;
    }
  }
}
