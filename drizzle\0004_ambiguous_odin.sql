CREATE TABLE "payment_plan_branches" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"payment_plan_id" uuid NOT NULL,
	"branch_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "payment_plan_branches_payment_plan_id_branch_id_unique" UNIQUE("payment_plan_id","branch_id")
);
--> statement-breakpoint
ALTER TABLE "payment_plan_branches" ADD CONSTRAINT "payment_plan_branches_payment_plan_id_payment_plans_id_fk" FOREIGN KEY ("payment_plan_id") REFERENCES "public"."payment_plans"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_plan_branches" ADD CONSTRAINT "payment_plan_branches_branch_id_branches_id_fk" FOREIGN KEY ("branch_id") REFERENCES "public"."branches"("id") ON DELETE cascade ON UPDATE no action;