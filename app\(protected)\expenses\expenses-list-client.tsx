"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ExpensesDataTable } from "@/components/expenses/expenses-data-table";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Expense } from "@/lib/types";

interface ExpensesListClientProps {
  initialData: Expense[];
}

export function ExpensesListClient({ initialData }: ExpensesListClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{t("expenses.title")}</CardTitle>
            <Button asChild>
              <Link href="/expenses/new">
                <PlusCircle className="mr-2 h-4 w-4" />
                {t("expenses.new")}
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <ExpensesDataTable data={initialData} />
        </CardContent>
      </Card>
    </div>
  );
}
