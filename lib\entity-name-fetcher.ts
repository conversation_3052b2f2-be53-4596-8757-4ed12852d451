"use server";

import { getTeamById } from './db/actions/teams';
import { getAthleteById } from './db/actions/athletes';
import { getInstructorById } from './db/actions/instructors';
import { getFacilityById } from './db/actions/facilities';
import { getSchoolById } from './db/actions/schools';
import { getItemById } from './db/actions/items';
import { getPaymentById } from './db/actions/payments';
import { getPaymentPlanById } from './db/actions/payment-plans';
import { getExpenseById } from './db/actions/expenses';

/**
 * Server-side function to fetch entity names for breadcrumbs
 * This function fetches the actual entity from the database and returns a display name
 */
export async function fetchEntityName(entityType: string, entityId: string): Promise<string> {
  try {
    switch (entityType) {
      case 'teams': {
        const team = await getTeamById(entityId);
        return team?.name || `Team #${entityId.slice(0, 8)}`;
      }
      
      case 'athletes': {
        const athlete = await getAthleteById(entityId);
        if (athlete?.name && athlete?.surname) {
          return `${athlete.name} ${athlete.surname}`;
        }
        return `Athlete #${entityId.slice(0, 8)}`;
      }
      
      case 'instructors': {
        const instructor = await getInstructorById(entityId);
        if (instructor?.name && instructor?.surname) {
          return `${instructor.name} ${instructor.surname}`;
        }
        return `Instructor #${entityId.slice(0, 8)}`;
      }
      
      case 'facilities': {
        const facility = await getFacilityById(entityId);
        return facility?.name || `Facility #${entityId.slice(0, 8)}`;
      }
      
      case 'schools': {
        const school = await getSchoolById(entityId);
        return school?.name || `School #${entityId.slice(0, 8)}`;
      }
      
      case 'items': {
        const item = await getItemById(entityId);
        return item?.name || `Item #${entityId.slice(0, 8)}`;
      }

      case 'plans': {
        const plan = await getPaymentPlanById(entityId);
        return plan?.name || `Plan #${entityId.slice(0, 8)}`;
      }
      
      case 'payments': {
        const payment = await getPaymentById(entityId);
        if (payment?.description) {
          return payment.description;
        }
        // If no description, show amount and date
        if (payment?.amount && payment?.date) {
          const amount = parseFloat(payment.amount);
          const date = new Date(payment.date).toLocaleDateString();
          return `Payment $${amount.toFixed(2)} - ${date}`;
        }
        return `Payment #${entityId.slice(0, 8)}`;
      }
      
      case 'expenses': {
        const expense = await getExpenseById(entityId);
        if (expense?.description) {
          return expense.description;
        }
        // If no description, show amount and category
        if (expense?.amount && expense?.category) {
          const amount = parseFloat(expense.amount);
          return `${expense.category.charAt(0).toUpperCase() + expense.category.slice(1)} $${amount.toFixed(2)}`;
        }
        return `Expense #${entityId.slice(0, 8)}`;
      }
      
      default:
        return `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} #${entityId.slice(0, 8)}`;
    }
  } catch (error) {
    console.error(`Failed to fetch ${entityType} name for ID ${entityId}:`, error);
    // Return fallback name
    return `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} #${entityId.slice(0, 8)}`;
  }
}
