import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { FacilityAvailability } from "@/components/teams/facility-availability";
import type { Facility } from "@/lib/types";
import type { TrainingScheduleForm } from "@/hooks/teams/useScheduleManagement";

interface FacilityAvailabilityCardProps {
  selectedFacilityId: string;
  facilities: Facility[];
  selectedSchedule?: TrainingScheduleForm;
  teamId: string;
  currentTeamSchedules?: TrainingScheduleForm[];
}

export function FacilityAvailabilityCard({
  selectedFacilityId,
  facilities,
  selectedSchedule,
  teamId,
  currentTeamSchedules = [],
}: FacilityAvailabilityCardProps) {
  const { t } = useSafeTranslation();

  if (!selectedFacilityId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t("teams.schedule.facilityAvailability")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            {t('teams.schedule.selectFacilityToViewAvailability')}
          </p>
        </CardContent>
      </Card>
    );
  }

  const facility = facilities.find(f => f.id === selectedFacilityId);
  
  // Create a display title for the selected schedule
  const scheduleDisplayTitle = selectedSchedule 
    ? `${t(`teams.days.${['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][selectedSchedule.dayOfWeek]}`)} ${selectedSchedule.startTime}-${selectedSchedule.endTime}`
    : '';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {t("teams.schedule.facilityAvailability")}
          {scheduleDisplayTitle && (
            <span className="text-sm font-normal text-muted-foreground">
              - {scheduleDisplayTitle}
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <FacilityAvailability
          facilityId={selectedFacilityId}
          facilityName={facility?.name || ""}
          selectedDay={selectedSchedule?.dayOfWeek}
          selectedStartTime={selectedSchedule?.startTime}
          selectedEndTime={selectedSchedule?.endTime}
          excludeTeamId={teamId}
          currentTeamSchedules={currentTeamSchedules}
        />
      </CardContent>
    </Card>
  );
}
