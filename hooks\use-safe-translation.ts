import { useTranslation } from 'react-i18next';
import { useEffect, useState, useMemo, useCallback } from 'react';

// Minimal fallback translations for critical keys
const fallbacks: Record<string, string> = {
  'common.loading': 'Loading...',
  'common.table.previous': 'Previous',
  'common.table.next': 'Next',
  'common.table.noResults': 'No results.',
  'common.table.searchPlaceholder': 'Search...',
};

export function useSafeTranslation() {
  const { t, i18n } = useTranslation();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Helper function to determine namespace from key
  const getNamespaceAndKey = (key: string): { namespace: string; localKey: string } => {
    // If key already includes namespace (contains ':'), split it
    if (key.includes(':')) {
      const [namespace, ...keyParts] = key.split(':');
      return { namespace, localKey: keyParts.join(':') };
    }
    
    // Determine namespace based on key prefix
    if (key.startsWith('nav.') || key.startsWith('common.')) {
      return { namespace: 'shared', localKey: key };
    } else if (key.startsWith('auth.')) {
      return { namespace: 'auth', localKey: key };
    } else if (key.startsWith('dashboard.')) {
      return { namespace: 'dashboard', localKey: key };
    } else if (key.startsWith('athletes.')) {
      return { namespace: 'athletes', localKey: key };
    } else if (key.startsWith('instructors.')) {
      return { namespace: 'instructors', localKey: key };
    } else if (key.startsWith('teams.')) {
      return { namespace: 'teams', localKey: key };
    } else if (key.startsWith('schools.')) {
      return { namespace: 'schools', localKey: key };
    } else if (key.startsWith('facilities.')) {
      return { namespace: 'facilities', localKey: key };
    } else if (key.startsWith('items.')) {
      return { namespace: 'items', localKey: key };
    } else if (key.startsWith('expenses.')) {
      return { namespace: 'expenses', localKey: key };
    } else if (key.startsWith('payments.')) {
      return { namespace: 'payments', localKey: key };
    } else if( key.startsWith('sms.')) {
      return { namespace: 'sms', localKey: key };
    } else if (key.startsWith('errors.')) {
      return { namespace: 'errors', localKey: key };
    }
    
    // Default to shared namespace
    return { namespace: 'shared', localKey: key };
  };

  // Return a safe t function that provides fallbacks
  const safeT = useCallback((key: string, options?: any): string => {
    if (!isClient) {
      return fallbacks[key] || key;
    }
    
    const { namespace, localKey } = getNamespaceAndKey(key);
    
    // Check if translation is loaded and not just the key
    const translation = t(localKey, { ...options, ns: namespace }) as string;
    if (translation === localKey && fallbacks[key]) {
      return fallbacks[key];
    }
    
    return translation;
  }, [isClient, t]); // Memoize the function to prevent unnecessary re-renders

  return { t: safeT, i18n, isClient };
}
