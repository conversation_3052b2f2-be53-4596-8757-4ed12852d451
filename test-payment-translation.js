// Test script to verify payment translation functionality
const { getPaymentTranslation } = require('./lib/server-i18n');

console.log('Testing payment translation functionality...');

// Test English translations
console.log('\n=== English Translations ===');
console.log('Initial Balance:', getPaymentTranslation('descriptions.initialBalance', 'en'));
console.log('Initial Balance from Import:', getPaymentTranslation('descriptions.initialBalanceFromImport', 'en'));
console.log('Prorated Balance:', getPaymentTranslation('descriptions.proratedBalance', 'en', { planName: 'Test Plan' }));
console.log('Prorated Balance Generic:', getPaymentTranslation('descriptions.proratedBalanceGeneric', 'en'));

// Test Turkish translations
console.log('\n=== Turkish Translations ===');
console.log('Initial Balance:', getPaymentTranslation('descriptions.initialBalance', 'tr'));
console.log('Initial Balance from Import:', getPaymentTranslation('descriptions.initialBalanceFromImport', 'tr'));
console.log('Prorated Balance:', getPaymentTranslation('descriptions.proratedBalance', 'tr', { planName: 'Test Planı' }));
console.log('Prorated Balance Generic:', getPaymentTranslation('descriptions.proratedBalanceGeneric', 'tr'));

// Test fallback behavior
console.log('\n=== Fallback Tests ===');
console.log('Non-existent key (should return key):', getPaymentTranslation('descriptions.nonExistent', 'en'));
console.log('Non-existent locale (should fallback to English):', getPaymentTranslation('descriptions.initialBalance', 'fr'));
