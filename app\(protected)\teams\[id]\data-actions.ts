"use server";

import { getServerTenantId, getServerUserId } from "@/lib/tenant-utils-server";
import { getAvailablePaymentPlansForTeam, checkAthleteInTeam } from "@/lib/db/actions/athlete-team-management";
import { getAthletes } from "@/lib/db/actions";

/**
 * Server action to get available athletes for team assignment
 * Following architecture: Client → Server Action → DB Layer
 */
export async function getAvailableAthletesAction() {
  try {
    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const athletes = await getAthletes();

    return {
      success: true,
      data: athletes
    };
  } catch (error) {
    console.error('Error in getAvailableAthletesAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch athletes'
    };
  }
}

/**
 * Server action to get available payment plans for a team
 * Following architecture: Client → Server Action → DB Layer
 */
export async function getTeamPaymentPlansAction(teamBranchId: string) {
  try {
    // Validate input
    if (!teamBranchId?.trim()) {
      return {
        success: false,
        error: 'Team branch ID is required'
      };
    }

    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const paymentPlans = await getAvailablePaymentPlansForTeam(teamBranchId);

    return {
      success: true,
      data: paymentPlans
    };
  } catch (error) {
    console.error('Error in getTeamPaymentPlansAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch payment plans'
    };
  }
}

/**
 * Server action to check if athlete is already in team
 * Following architecture: Client → Server Action → DB Layer
 */
export async function checkAthleteInTeamAction(athleteId: string, teamId: string) {
  try {
    // Validate inputs
    if (!athleteId?.trim() || !teamId?.trim()) {
      return {
        success: false,
        error: 'Athlete ID and Team ID are required'
      };
    }

    // Get tenant context for security
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    if (!tenantId || !userId) {
      return {
        success: false,
        error: 'Unauthorized access'
      };
    }

    // Call DB layer
    const isInTeam = await checkAthleteInTeam(athleteId, teamId);

    return {
      success: true,
      data: isInTeam
    };
  } catch (error) {
    console.error('Error in checkAthleteInTeamAction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check athlete team status'
    };
  }
}
