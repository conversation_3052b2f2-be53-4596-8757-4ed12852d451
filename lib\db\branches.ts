import { eq } from 'drizzle-orm';
import { TenantAwareDBBase } from './base';
import * as schema from '@/src/db/schema';

/**
 * Database operations for Branches
 * Note: Branches are global and not tenant-specific
 */
export class BranchesDB extends TenantAwareDBBase {
  static async getBranches() {
    return this.database.select({
      id: schema.branches.id,
      name: schema.branches.name,
      description: schema.branches.description,
    }).from(schema.branches)
    .orderBy(schema.branches.name);
  }

  static async getBranchById(id: string) {
    const result = await this.database.select({
      id: schema.branches.id,
      name: schema.branches.name,
      description: schema.branches.description,
    }).from(schema.branches)
    .where(eq(schema.branches.id, id));
    
    return result[0] || null;
  }
}
