"use server";

import { revalidatePath } from 'next/cache';
import { paymentPlanService } from '../../services';

export async function createPaymentPlan(values: {
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}) {
  try {
    const service = paymentPlanService();
    const result = await service.createPaymentPlan(values);

    if (!result.success) {
      console.error("createPaymentPlan error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to create payment plan");
    }

    revalidatePath('/payment-plans');
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Error creating payment plan:', error);
    throw error;
  }
}

export async function updatePaymentPlan(id: string, values: {
  name?: string;
  monthlyValue?: string;
  assignDay?: number;
  dueDay?: number;
  status?: 'active' | 'inactive';
  description?: string;
  selectedBranches?: string[];
}) {
  try {
    const service = paymentPlanService();
    const result = await service.updatePaymentPlan(id, values);

    if (!result.success) {
      console.error("updatePaymentPlan error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to update payment plan with ID ${id}`);
    }

    revalidatePath('/payment-plans');
    revalidatePath(`/payment-plans/${id}`);
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Error updating payment plan:', error);
    throw error;
  }
}

export async function getPaymentPlanAssignedAthletesCount(id: string): Promise<number> {
  const service = paymentPlanService();
  const result = await service.getPaymentPlanAssignedAthletesCount(id);

  if (!result.success) {
    console.error('Error getting payment plan assigned athletes count:', result.error);
    return 0;
  }

  return result.data || 0;
}

export async function deletePaymentPlan(id: string) {
  try {
    const service = paymentPlanService();
    const result = await service.deletePaymentPlan(id);

    if (!result.success) {
      console.error("deletePaymentPlan error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to delete payment plan with ID ${id}`);
    }

    revalidatePath('/payment-plans');
    return { success: true };
  } catch (error) {
    console.error('Error deleting payment plan:', error);
    throw error;
  }
}

export async function getPaymentPlans() {
  try {
    const service = paymentPlanService();
    const result = await service.getPaymentPlans();

    if (!result.success) {
      console.error("getPaymentPlans error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get payment plans");
    }

    return result.data || [];
  } catch (error) {
    console.error('Error getting payment plans:', error);
    throw error;
  }
}

export async function getPaymentPlanById(id: string) {
  try {
    const service = paymentPlanService();
    const result = await service.getPaymentPlanById(id);

    if (!result.success) {
      console.error("getPaymentPlanById error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to get payment plan with ID ${id}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error getting payment plan by ID:', error);
    throw error;
  }
}

export async function getPaymentsByPlanId(planId: string) {
  const service = paymentPlanService();
  const result = await service.getPaymentsByPlanId(planId);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get payments by plan ID');
  }

  return result.data;
}

export async function getPaymentPlanStats(planId: string) {
  const service = paymentPlanService();
  const result = await service.getPaymentPlanStats(planId);

  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get payment plan stats');
  }

  return result.data;
}

export async function validatePaymentPlanData(data: {
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
}): Promise<{ isValid: boolean; errors: string[] }> {
  try {
    const service = paymentPlanService();
    return await service.validatePaymentPlanData(data);
  } catch (error) {
    console.error('Error validating payment plan data:', error);
    
    // Fallback validation if service fails
    const errors: string[] = [];

    // Validate name
    if (!data.name || data.name.trim().length < 2) {
      errors.push('Plan name must be at least 2 characters long');
    }

    // Validate monthly value
    const monthlyValue = parseFloat(data.monthlyValue);
    if (isNaN(monthlyValue) || monthlyValue <= 0) {
      errors.push('Monthly value must be a positive number');
    }

    // Validate assign day
    if (data.assignDay < 1 || data.assignDay > 31) {
      errors.push('Assign day must be between 1 and 31');
    }

    // Validate due day
    if (data.dueDay < 1 || data.dueDay > 31) {
      errors.push('Due day must be between 1 and 31');
    }

    // Validate due day is after assign day
    if (data.dueDay < data.assignDay) {
      errors.push('Due day cannot be before assign day');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
