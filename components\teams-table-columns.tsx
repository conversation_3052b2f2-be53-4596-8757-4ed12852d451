'use client';

import { ColumnDef } from "@tanstack/react-table";
import { Team } from "@/lib/types";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Eye, MoreHorizontal, Pencil, Trash } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useMemo, useState } from "react";
import { deleteTeam } from "@/lib/actions/teams";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";

const ActionsCell = ({ team }: { team: Team }) => {
  const { t } = useSafeTranslation();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteTeam(team.id);
      toast({
        title: t('teams.messages.deleteSuccess'),
        description: t('teams.messages.teamDeleted'),
      });
      router.refresh();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting team:', error);
      toast({
        title: t('teams.messages.deleteError'),
        description: error instanceof Error ? error.message : t('teams.messages.deleteFailed'),
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">{t('teams.actions.openMenu')}</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{t('common.actionsHeader')}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link href={`/teams/${team.id}`} className="flex items-center">
              <Eye className="mr-2 h-4 w-4" />
              {t('teams.actions.viewDetails')}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href={`/teams/${team.id}/edit`} className="flex items-center">
              <Pencil className="mr-2 h-4 w-4" />
              {t('teams.actions.edit')}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem 
            className="text-destructive"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash className="mr-2 h-4 w-4" />
            {t('teams.actions.delete')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('teams.actions.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('teams.messages.deleteConfirmation', { name: team.name })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? t('common.actions.deleting') : t('common.actions.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

const TrainingScheduleCell = ({ schedule }: { schedule?: any[] }) => {
  const { t } = useSafeTranslation();
  
  if (!schedule || schedule.length === 0) {
    return <span className="text-muted-foreground text-sm">-</span>;
  }
  
  return (
    <div className="space-y-1">
      {schedule.map((item) => (
        <div key={item.id} className="text-sm">
          {getDayName(item.dayOfWeek, t)}: {item.startTime} - {item.endTime}
        </div>
      ))}
    </div>
  );
};

const AthletesCell = ({ athletes }: { athletes?: any[] }) => {
  const { t } = useSafeTranslation();
  
  const athleteCount = athletes ? athletes.length : 0;
  
  return (
    <Badge variant="secondary">
      {t('teams.messages.athleteCount', { count: athleteCount })}
    </Badge>
  );
};

export const useTeamColumns = (): ColumnDef<Team>[] => {
  const { t } = useSafeTranslation();

  return useMemo(() => [
    {
      accessorKey: "name",
      header: t('teams.details.name'),
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-muted-foreground">
            {row.original.branch?.name ? t(`common.branches.${row.original.branch.name}`, { ns: 'shared' }) : '-'}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "trainingSchedule",
      header: t('teams.details.schedule'),
      cell: ({ row }) => <TrainingScheduleCell schedule={row.original.trainingSchedule} />,
    },
    {
      accessorKey: "athletes",
      header: t('teams.details.athletes'),
      cell: ({ row }) => <AthletesCell athletes={row.original.athletes} />,
    },
    {
      id: "actions",
      header: t('common.actionsHeader'),
      cell: ({ row }) => <ActionsCell team={row.original} />,
    },
  ], [t]);
};

function getDayName(day: number, t: any): string {
  const dayMap: { [key: number]: string } = {
    0: t('teams.days.sunday'),
    1: t('teams.days.monday'),
    2: t('teams.days.tuesday'),
    3: t('teams.days.wednesday'),
    4: t('teams.days.thursday'),
    5: t('teams.days.friday'),
    6: t('teams.days.saturday'),
  };
  return dayMap[day] || day.toString();
}