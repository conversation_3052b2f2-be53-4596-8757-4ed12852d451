import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import type { UseFormReturn } from "react-hook-form";
import type { TeamFormData } from "@/hooks/teams/useTeamForm";
import type { School, Instructor, Branch } from "@/lib/types";

interface TeamBasicFormProps {
  form: UseFormReturn<TeamFormData>;
  schools: School[];
  branches: Branch[];
  filteredInstructors: Instructor[];
  selectedSchool: string;
  onSchoolChange: (schoolId: string) => void;
  onSubmit: (data: TeamFormData) => void;
  loading: boolean;
}

export function TeamBasicForm({
  form,
  schools,
  branches,
  filteredInstructors,
  selectedSchool,
  onSchoolChange,
  onSubmit,
  loading,
}: TeamBasicFormProps) {
  const { t } = useSafeTranslation();

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('teams.details.name')}</FormLabel>
              <FormControl>
                <Input placeholder={t('teams.placeholders.enterName')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('common.details.description')} ({t('common.optional')})</FormLabel>
              <FormControl>
                <Input placeholder={t('common.placeholders.enterDescription')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="schoolId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('teams.details.school')}</FormLabel>
              <Select
                onValueChange={onSchoolChange}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('teams.placeholders.selectSchool')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {schools.map((school) => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="branchId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('teams.details.branch')}</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!selectedSchool}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('teams.placeholders.selectBranch')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {branches
                    .filter(branch => branch.id !== null)
                    .map((branch) => (
                    <SelectItem key={branch.id} value={branch.id!}>
                      {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="instructorId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('teams.details.instructor')}</FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!selectedSchool}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('teams.placeholders.selectInstructor')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {filteredInstructors.map((instructor) => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      {instructor.name} {instructor.surname}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
