"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useAthleteForm } from "@/hooks/athletes/useAthleteForm";
import { usePaymentPlanManagement } from "@/hooks/athletes/usePaymentPlanManagement";
import { useAthleteOperations } from "@/hooks/athletes/useAthleteOperations";
import { AthleteBasicForm } from "./AthleteBasicForm";
import { PaymentPlanManager } from "./PaymentPlanManager";
import { AthleteFormActions } from "./AthleteFormActions";
import type { AthleteTeam } from "@/hooks/athletes/usePaymentPlanManagement";

interface Athlete {
  id: string;
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  parentName: string | null;
  parentSurname: string | null;
  parentPhone: string | null;
  parentEmail: string | null;
  parentAddress: string | null;
}

interface EditAthleteClientProps {
  athlete: Athlete;
  athleteTeams: AthleteTeam[];
}

export function EditAthleteClient({ 
  athlete, 
  athleteTeams 
}: EditAthleteClientProps) {
  const { t } = useSafeTranslation();
  
  const { formData, errors, updateField, validateForm, setFormData } = useAthleteForm({
    athlete,
  });

  const {
    originalPaymentPlans,
    paymentPlanChanges,
    handlePaymentPlanChange,
  } = usePaymentPlanManagement({ athleteId: athlete.id });

  const { isSaving, saveAthlete, navigateToList } = useAthleteOperations({
    athleteId: athlete.id,
  });

  const handleFormDataChange = (newFormData: typeof formData) => {
    setFormData(newFormData);
  };

  const handleSave = async () => {
    const isFormValid = validateForm();
    if (!isFormValid) return;
    
    const success = await saveAthlete(
      formData,
      athleteTeams,
      originalPaymentPlans,
      paymentPlanChanges
    );
    
    if (success) {
      navigateToList();
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {t('athletes.editPage.title', { name: `${athlete.name} ${athlete.surname}` })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <AthleteBasicForm
            formData={formData}
            onFormDataChange={handleFormDataChange}
            errors={errors}
          />

          <PaymentPlanManager
            athleteId={athlete.id}
            athleteTeams={athleteTeams}
            paymentPlanChanges={paymentPlanChanges}
            onPaymentPlanChangesUpdate={handlePaymentPlanChange}
          />

          <AthleteFormActions
            isValid={Object.keys(errors).length === 0}
            isSaving={isSaving}
            onSave={handleSave}
            onCancel={navigateToList}
          />
        </CardContent>
      </Card>
    </div>
  );
}
