"use client";

import React, { useState } from "react";
import Link from "next/link";
import { More<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash, Eye } from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import type { Expense } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { deleteExpense } from "@/lib/db/actions/expenses";
import { toast } from "sonner";

const ExpenseActionsCell = ({ expense }: { expense: Expense }) => {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteExpense(expense.id);
      toast.success(t('expenses.messages.deleteSuccess'));
      router.refresh();
    } catch (error) {
      console.error('Error deleting expense:', error);
      toast.error(t('expenses.messages.deleteError'));
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">{t("expenses.actions.openMenu")}</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t("common.actionsHeader")}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/expenses/${expense.id}`} className="flex items-center">
            <Eye className="mr-2 h-4 w-4" />
            {t("expenses.actions.view")}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/expenses/${expense.id}/edit`} className="flex items-center">
            <Pencil className="mr-2 h-4 w-4" />
            {t("expenses.actions.edit")}
          </Link>
        </DropdownMenuItem>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onSelect={(e) => e.preventDefault()}
            >
              <Trash className="mr-2 h-4 w-4" />
              {t("expenses.actions.delete")}
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t('expenses.deleteDialog.title')}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t('expenses.deleteDialog.description')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>
                {t('common.actions.cancel')}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting 
                  ? t('common.actions.deleting') 
                  : t('common.actions.delete')
                }
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export function ExpensesDataTable({ data }: { data: Expense[] }) {
  const { t } = useSafeTranslation();

  const columns = [
    {
      accessorKey: "date",
      header: t("expenses.details.date"),
      cell: ({ row }: { row: { original: Expense } }) => format(new Date(row.original.date), "dd/MM/yyyy"),
    },
    {
      accessorKey: "amount",
      header: t("expenses.details.amount"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="font-medium">{parseFloat(row.original.amount).toFixed(2)} {t('common.currency')}</div>
      ),
    },
    {
      accessorKey: "category",
      header: t("expenses.details.category"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="capitalize">{t(`expenses.categories.${row.original.category}`)}</div>
      ),
    },
    {
      accessorKey: "description",
      header: t("expenses.details.description"),
    },
    {
      accessorKey: "instructor",
      header: t("expenses.details.instructor"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="text-sm">
          {row.original.instructor ? `${row.original.instructor.name} ${row.original.instructor.surname}` : "-"}
        </div>
      ),
    },
    {
      accessorKey: "facility",
      header: t("expenses.details.facility"),
      cell: ({ row }: { row: { original: Expense } }) => (
        <div className="text-sm">
          {row.original.facility ? row.original.facility.name : "-"}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }: { row: { original: Expense } }) => (
        <ExpenseActionsCell expense={row.original} />
      ),
    },
  ];

  return (
    <DataTable
      columns={columns}
      data={data}
      searchKey="description"
      searchPlaceholder={t("expenses.placeholders.searchExpenses")}
    />
  );
}