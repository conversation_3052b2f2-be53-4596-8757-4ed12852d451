"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { CreditCard, Users, AlertTriangle, CheckCircle } from "lucide-react";
import { toast } from "sonner";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { checkPaymentPlanConflicts, bulkAssignPaymentPlan } from "@/lib/db/actions/teams";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Athlete {
  id: string;
  name: string;
  surname: string;
  status: string;
}

interface ConflictInfo {
  athleteId: string;
  athleteName: string;
  athleteSurname: string;
  currentPlanName: string;
  currentPlanId: string;
  athleteStatus: string;
}

interface InactiveAthleteInfo {
  athleteId: string;
  athleteName: string;
  athleteSurname: string;
  status: string;
}

interface TeamPaymentPlanBulkAssignmentProps {
  teamId: string;
  teamName: string;
  athletes: Athlete[];
  availablePaymentPlans: PaymentPlan[];
  teamBranchId: string;
  onAssignmentComplete?: () => void;
}

export function TeamPaymentPlanBulkAssignment({
  teamId,
  teamName,
  athletes,
  availablePaymentPlans,
  teamBranchId,
  onAssignmentComplete
}: TeamPaymentPlanBulkAssignmentProps) {
  const { t } = useSafeTranslation();
  const [selectedPlanId, setSelectedPlanId] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [conflicts, setConflicts] = useState<ConflictInfo[]>([]);
  const [inactiveAthletes, setInactiveAthletes] = useState<InactiveAthleteInfo[]>([]);

  // Filter payment plans based on team's branch
  const filteredPaymentPlans = availablePaymentPlans.filter(plan => 
    plan.status === 'active' && 
    (plan.branches?.some(branch => branch.id === teamBranchId) || !plan.branches?.length)
  );

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  const handleAssignPaymentPlan = async () => {
    if (!selectedPlanId) {
      toast.error(t('payments.plans.assignments.selectRequired'));
      return;
    }

    setIsLoading(true);

    try {
      // Check for conflicts first
      const result = await checkPaymentPlanConflicts(teamId, selectedPlanId, athletes.map(a => a.id));

      // If there are conflicts or inactive athletes, show warning dialog
      if ((result.conflicts && result.conflicts.length > 0) || 
          (result.inactiveAthletes && result.inactiveAthletes.length > 0)) {
        setConflicts(result.conflicts);
        setInactiveAthletes(result.inactiveAthletes);
        setIsConfirmDialogOpen(true);
      } else {
        // No conflicts or inactive athletes, proceed directly
        await performBulkAssignment();
      }
    } catch (error) {
      console.error("Error checking conflicts:", error);
      toast.error(t('common.error'));
    } finally {
      setIsLoading(false);
    }
  };

  const performBulkAssignment = async (forceAssign = false) => {
    setIsLoading(true);

    try {
      const result = await bulkAssignPaymentPlan(
        teamId,
        selectedPlanId,
        athletes.map(a => a.id),
        forceAssign
      );

      toast.success(t('teams.paymentPlan.bulkAssignSuccess', { 
        count: result.assignedCount,
        updated: result.updatedCount 
      }));
      
      setIsDialogOpen(false);
      setIsConfirmDialogOpen(false);
      setSelectedPlanId("");
      setConflicts([]);
      setInactiveAthletes([]);
      onAssignmentComplete?.();
    } catch (error) {
      console.error("Error assigning payment plan:", error);
      toast.error(t('teams.paymentPlan.bulkAssignError'));
    } finally {
      setIsLoading(false);
    }
  };

  const selectedPlan = filteredPaymentPlans.find(plan => plan.id === selectedPlanId);

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {t('teams.paymentPlan.bulkAssignment')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              {t('teams.paymentPlan.bulkAssignmentDescription', { count: athletes.length })}
            </p>
            
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button 
                  className="w-full" 
                  disabled={athletes.length === 0}
                  size="lg"
                >
                  <Users className="h-4 w-4 mr-2" />
                  {t('teams.paymentPlan.assignToAllAthletes')}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('teams.paymentPlan.selectPaymentPlan')}</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>{t('teams.details.team')}</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <div className="font-medium">{teamName}</div>
                      <div className="text-sm text-muted-foreground">
                        {t('teams.paymentPlan.athletesCount', { count: athletes.length })}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>{t('payments.plans.plan')} *</Label>
                    <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.plans.select')} />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredPaymentPlans.map((plan) => (
                          <SelectItem key={plan.id} value={plan.id}>
                            {plan.name} - {formatCurrency(plan.monthlyValue)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedPlan && (
                    <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
                      <div className="text-sm space-y-1">
                        <div><strong>{t('payments.plans.monthlyValue')}:</strong> {formatCurrency(selectedPlan.monthlyValue)}</div>
                        <div><strong>{t('payments.plans.assignDay')}:</strong> {selectedPlan.assignDay}{t('common.dayOfMonth')}</div>
                        <div><strong>{t('payments.plans.dueDay')}:</strong> {selectedPlan.dueDay}{t('common.dayOfMonth')}</div>
                      </div>
                    </div>
                  )}

                  <div className="p-3 bg-amber-50 dark:bg-amber-950/20 rounded-md">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm text-amber-800 dark:text-amber-200">
                        {t('teams.paymentPlan.assignmentWarning')}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      {t('common.actions.cancel')}
                    </Button>
                    <Button
                      type="button"
                      onClick={handleAssignPaymentPlan}
                      disabled={isLoading || !selectedPlanId}
                    >
                      {isLoading ? t('common.actions.checking') : t('common.actions.assign')}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialog for Conflicts */}
      <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <AlertDialogContent className="max-w-2xl">
          <AlertDialogHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <AlertDialogTitle>
                {conflicts.length > 0 && inactiveAthletes.length > 0 
                  ? t('teams.paymentPlan.conflictsAndInactiveDetected')
                  : conflicts.length > 0 
                    ? t('teams.paymentPlan.conflictsDetected')
                    : t('teams.paymentPlan.inactiveAthletesDetected')
                }
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription asChild>
              <div className="space-y-4">
                {conflicts.length > 0 && (
                  <div>
                    <p className="mb-3">{t('teams.paymentPlan.conflictsDescription')}</p>
                    
                    <div className="max-h-40 overflow-y-auto">
                      <div className="space-y-2">
                        {conflicts.map((conflict) => (
                          <div key={conflict.athleteId} className="p-3 bg-orange-50 dark:bg-orange-950/20 rounded-md">
                            <div className="font-medium">
                              {conflict.athleteName} {conflict.athleteSurname}
                              {conflict.athleteStatus && conflict.athleteStatus !== 'active' && (
                                <span className="ml-2 text-xs bg-gray-500 text-white px-2 py-1 rounded">
                                  {t(`common.status.${conflict.athleteStatus}`, { ns: 'shared' })}
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {t('teams.paymentPlan.currentPlan')}: {conflict.currentPlanName}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {inactiveAthletes.length > 0 && (
                  <div>
                    <p className="mb-3">{t('teams.paymentPlan.inactiveAthletesDescription')}</p>
                    
                    <div className="max-h-40 overflow-y-auto">
                      <div className="space-y-2">
                        {inactiveAthletes.map((athlete) => (
                          <div key={athlete.athleteId} className="p-3 bg-amber-50 dark:bg-amber-950/20 rounded-md">
                            <div className="font-medium">
                              {athlete.athleteName} {athlete.athleteSurname}
                              {athlete.status && (
                                <span className="ml-2 text-xs bg-gray-500 text-white px-2 py-1 rounded">
                                  {t(`common.status.${athlete.status}`, { ns: 'shared' })}
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {t('teams.paymentPlan.willBeAssignedAsInactive')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-md">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                      {t('teams.paymentPlan.proceedDescription')}
                    </div>
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => performBulkAssignment(true)}
              disabled={isLoading}
            >
              {isLoading ? t('common.actions.saving') : t('teams.paymentPlan.proceedAnyway')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
