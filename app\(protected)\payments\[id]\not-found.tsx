import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, Search } from "lucide-react";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" asChild>
          <Link href="/payments">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Payments
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Payment Not Found
          </h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Payment Not Found
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            The payment you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          
          <div className="flex gap-2 pt-4">
            <Button asChild>
              <Link href="/payments">
                Back to Payments
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/payments/new">
                Record New Payment
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
