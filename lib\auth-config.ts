import { AuthOptions } from "next-auth";
import axios from "axios";
import { extractTenantId, extractUserId } from "@/lib/middleware-tenant-utils";

async function refreshAccessToken(token: any) {
  try {
    const url = `${process.env.ZITADEL_ISSUER}/oauth/v2/token`;
    
    const response = await axios.post(url, new URLSearchParams({
      client_id: process.env.ZITADEL_CLIENT_ID!,
      grant_type: "refresh_token",
      refresh_token: token.refreshToken,
    }), {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });

    const refreshedTokens = response.data;

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      idToken: refreshedTokens.id_token,
      expiresAt: Date.now() / 1000 + refreshedTokens.expires_in,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken, // Fall back to old refresh token
    };
  } catch (error) {
    console.error("Error refreshing access token", error);

    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

export const authOptions: AuthOptions = {
  providers: [
    {
      id: "zitadel",
      name: "Zitadel",
      type: "oauth",
      version: "2.0",
      wellKnown: `${process.env.ZITADEL_ISSUER}/.well-known/openid-configuration`,
      authorization: {
        params: {
          scope: "openid email profile offline_access urn:zitadel:iam:org:project:roles",
          code_challenge_method: "S256",
        },
      },
      clientId: process.env.ZITADEL_CLIENT_ID!,
      client: {
        token_endpoint_auth_method: "none",
      },
      idToken: true,
      checks: ["pkce", "state"],
      profile(profile) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
        };
      },
    },
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      // Initial sign in
      if (account && profile && account.id_token) {
        // Parse the ID token to extract tenant and user information
        const idTokenPayload = JSON.parse(
          Buffer.from(account.id_token.split('.')[1], 'base64').toString()
        );
        
        return {
          ...token,
          accessToken: account.access_token,
          idToken: account.id_token,
          refreshToken: account.refresh_token,
          expiresAt: account.expires_at,
          tenantId: extractTenantId({ payload: idTokenPayload } as any),
          userId: extractUserId({ payload: idTokenPayload } as any),
        };
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token.expiresAt as number) * 1000) {
        return token;
      }

      // Access token has expired, try to update it
      return refreshAccessToken(token);
    },
    async session({ session, token }) {
      if (token) {
        session.accessToken = token.accessToken as string;
        session.idToken = token.idToken as string;
        session.error = token.error as string;
        session.tenantId = token.tenantId as string;
        session.userId = token.userId as string;
      }
      return session;
    },
  },
  events: {
    async signOut({ token }) {
      if (token?.accessToken) {
        try {
          await axios.post(`${process.env.ZITADEL_ISSUER}/oauth/v2/revoke`, new URLSearchParams({
            client_id: process.env.ZITADEL_CLIENT_ID!,
            token: token.accessToken as string,
          }), {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          });
        } catch (error) {
          console.error("Error revoking token on sign out", error);
        }
      }
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/signin",
  },
  session: {
    strategy: "jwt",
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.session-token"
        : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
};
