import { useState } from "react";

export interface AthleteFormData {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  parent: {
    name: string;
    surname: string;
    phone: string;
    email: string;
    address: string;
  };
}

interface Athlete {
  id: string;
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  parentName: string | null;
  parentSurname: string | null;
  parentPhone: string | null;
  parentEmail: string | null;
  parentAddress: string | null;
}

interface UseAthleteFormProps {
  athlete: Athlete;
}

export function useAthleteForm({ athlete }: UseAthleteFormProps) {
  const [formData, setFormData] = useState<AthleteFormData>({
    name: athlete.name,
    surname: athlete.surname,
    nationalId: athlete.nationalId,
    birthDate: athlete.birthDate.split('T')[0], // Ensure format is YYYY-MM-DD
    parent: {
      name: athlete.parentName || "",
      surname: athlete.parentSurname || "",
      phone: athlete.parentPhone || "",
      email: athlete.parentEmail || "",
      address: athlete.parentAddress || "",
    },
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateField = (field: string, value: string) => {
    if (field.startsWith('parent.')) {
      const parentField = field.replace('parent.', '');
      setFormData(prev => ({
        ...prev,
        parent: {
          ...prev.parent,
          [parentField]: value,
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    if (!formData.surname.trim()) {
      newErrors.surname = "Surname is required";
    }
    if (!formData.nationalId.trim()) {
      newErrors.nationalId = "National ID is required";
    }
    if (!formData.birthDate) {
      newErrors.birthDate = "Birth date is required";
    }

    // Validate birth date is not in the future
    if (formData.birthDate && new Date(formData.birthDate) > new Date()) {
      newErrors.birthDate = "Birth date cannot be in the future";
    }

    // Email validation if provided
    if (formData.parent.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.parent.email)) {
      newErrors['parent.email'] = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      name: athlete.name,
      surname: athlete.surname,
      nationalId: athlete.nationalId,
      birthDate: athlete.birthDate.split('T')[0],
      parent: {
        name: athlete.parentName || "",
        surname: athlete.parentSurname || "",
        phone: athlete.parentPhone || "",
        email: athlete.parentEmail || "",
        address: athlete.parentAddress || "",
      },
    });
    setErrors({});
  };

  return {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
    setFormData,
  };
}
