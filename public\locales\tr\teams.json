{"teams": {"title": "Takımlar", "new": "<PERSON><PERSON>", "edit": "Takım <PERSON>", "details": {"name": "Takım Adı", "team": "Takım", "selectTeam": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "school": "<PERSON><PERSON>", "branch": "Branş", "instructor": "Eğitmen", "schedule": "Antrenman Programı", "athletes": "Sporcular", "moreAthletes": "daha fazla sporcu", "viewAllAthletes": "Tüm Sporcuları Görüntüle", "noAthletes": "Bu takımda henüz sporcu yok", "addAthletes": "<PERSON><PERSON><PERSON>", "noSchedule": "Henüz antrenman programı ayarlanmadı", "setupSchedule": "Program Ayarla", "schoolAndInstructor": "Okul ve Eğitmen", "downloadSchedule": "Programı İndir"}, "actions": {"viewDetails": "Detayları Görüntüle", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "openMenu": "Menüyü a<PERSON>", "addNew": "<PERSON><PERSON>"}, "messages": {"athleteCount": "{{count}} sporcu", "athleteCount_other": "{{count}} sporcu", "updateSuccess": "Tak<PERSON>m başarı<PERSON> gü<PERSON>llendi", "updateSuccessDetail": "Takım bilgileri ve antrenman programları başarıyla kaydedildi", "updateError": "Tak<PERSON>m güncellenirken hata oluştu", "updateErrorDetail": "Lütfen tekrar deneyin veya destek ile iletişime geçin", "conflictError": "Zaman çakışmaları olan programı kaydedemezsiniz", "conflictErrorDetail": "Kaydetmeden önce tüm program çakışmalarını çözün", "loadError": "Tak<PERSON>m verileri yüklenir<PERSON> hata oluştu", "manageTeams": "Takımları Yönet"}, "placeholders": {"enterName": "Tak<PERSON>m adını girin", "selectSchool": "Bir okul seçin", "selectBranch": "B<PERSON> branş seçin", "selectInstructor": "Bir eğit<PERSON> se<PERSON>", "searchTeams": "Takım ara..."}, "schedule": {"addSlot": "<PERSON><PERSON>", "remove": "Kaldır", "day": "<PERSON><PERSON><PERSON>", "startTime": "Başlangıç <PERSON>", "endTime": "Bitiş Saati", "facility": "<PERSON><PERSON>", "facilityAvailability": "<PERSON><PERSON>", "conflictWarning": "Bu zaman dilimi başka bir takımın programı ile çakışıyor", "internalConflictWarning": "Bu zaman dilimi bu takımın başka bir antrenman seansı ile çakışıyor", "selectFacilityToViewAvailability": "Uygunluğu görüntülemek için bir tesis seçin", "conflictDetected": "Program Çakışması Tespit Edildi", "conflictWith": "{{team}} takımının antrenman programı ile çakışıyor", "schedule": "Program", "noSchedulesThisDay": "<PERSON>u gün için program yok", "existingSchedules": "Mevcut programlar", "occupied": "Do<PERSON>", "weeklyOverview": "Haftalık Genel Bakış", "available": "<PERSON><PERSON><PERSON><PERSON>", "availableTimeSlots": "<PERSON><PERSON><PERSON><PERSON>"}, "days": {"monday": "<PERSON><PERSON><PERSON>", "tuesday": "Salı", "wednesday": "Çarşamba", "thursday": "Perşembe", "friday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON><PERSON><PERSON>", "sunday": "Pazar"}, "paymentPlan": {"bulkAssignment": "Takım Ödeme Planı Ataması", "bulkAssignmentDescription": "Bu takımdaki tüm {{count}} sporcuya ödeme planı ata", "assignToAllAthletes": "Tüm Sporculara Ödeme Planı Ata", "selectPaymentPlan": "Takım İçin Ödeme Planı Seç", "athletesCount": "{{count}} sporcu", "assignmentWarning": "Bu işlem seçilen ödeme planını takımdaki tüm sporculara atayacaktır. Bu takım için mevcut aktif planları olan sporcuların mevcut planları değiştirilecektir.", "conflictsDetected": "Ödeme Planı Çakışmaları Tespit Edildi", "conflictsAndInactiveDetected": "Ödeme Planı Çakışmaları ve Pasif Sporcular Tespit Edildi", "inactiveAthletesDetected": "Pasif Sporcular Tespit Edildi", "conflictsDescription": "Bazı sporcuların bu takım için zaten farklı ödeme planları atanmış:", "inactiveAthletesDescription": "Bazı sporcular pasif durumda ve pasif ödeme planı ataması alacaklar:", "willBeAssignedAsInactive": "Ödeme planı pasif o<PERSON>ak atanacak", "currentPlan": "Mevcut plan", "proceedDescription": "<PERSON><PERSON>, bu spor<PERSON>ın bu takım için mevcut ödeme planları devre dışı bırakılacak ve seçilen plan ile değiştirilecektir.", "proceedAnyway": "<PERSON><PERSON>", "bulkAssignSuccess": "Ödeme planı {{count}} sporcuya başarıyla atandı ({{updated}} mevcut plan güncellendi)", "bulkAssignError": "Takıma ödeme planı atanamadı"}, "management": {"addAthlete": "<PERSON><PERSON><PERSON>", "removeAthlete": "<PERSON><PERSON><PERSON>", "addAthleteToTeam": "Takıma Sporcu Ekle", "removeAthleteFromTeam": "Takımdan Sporcu <PERSON>ı<PERSON>", "addAthleteDescription": "{{teamName}} takımına sporcu ekle", "removeAthleteConfirmation": "{{athleteName}} adlı sporcuyu {{teamName}} takımından çıkarmak istediğinizden emin misiniz?", "selectAthlete": "Lütfen bir sporcu seçin", "selectAthleteToAdd": "Eklenecek sporcuyu seçin", "selectPaymentPlan": "Lütfen bir ödeme planı seçin", "assignPaymentPlan": "Sporcuya ödeme planı ata", "removePaymentPlans": "Ödeme planlarını kaldır", "noAvailableAthletes": "Eklenebilecek sporcu yok", "noAvailablePaymentPlans": "Bu takım için kullanılabilir ödeme planı yok", "noPaymentPlansForBranch": "Bu takımın branşı için yapılandırılmış ödeme planı yok", "athleteAlreadyInTeam": "S<PERSON><PERSON> zaten bu takımda", "athleteAddedSuccess": "{{athleteName}} {{teamName}} takımına eklendi", "athleteRemovedSuccess": "{{athleteName}} {{teamName}} takımından ç<PERSON>ldı", "withPaymentPlan": "{{planName}} ödeme planı ile", "paymentPlansDeactivated": "ve {{count}} ödeme planı devre dışı bırakıldı", "addAthleteError": "Sporcu takıma eklenirken hata oluştu", "removeAthleteError": "Sporcu takı<PERSON>ı<PERSON> hata oluştu", "activePaymentPlansForTeam": "Bu takım için aktif ödeme planları", "paymentPlansWillBeDeactivated": "Bu ödeme planları devre dışı bırakılacak", "noActivePaymentPlans": "Bu takım için aktif ödeme planı yok"}}}