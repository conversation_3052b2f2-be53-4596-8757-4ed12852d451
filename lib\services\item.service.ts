import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateItemData {
  name: string;
  description?: string;
  price: string;
  category: 'equipment' | 'clothing' | 'accessories' | 'other';
  stock?: number;
  image?: string;
}

export interface UpdateItemData {
  name?: string;
  description?: string;
  price?: string;
  category?: 'equipment' | 'clothing' | 'accessories' | 'other';
  stock?: number;
  image?: string;
}

export class ItemService extends BaseService {
  constructor() {
    super('ItemService');
  }

  /**
   * Get all items
   */
  async getItems(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getItems',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getItems(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'items',
      }
    );
  }

  /**
   * Get item by ID
   */
  async getItemById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getItemById',
      'Item',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getItemById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'item',
      }
    );
  }

  /**
   * Create a new item
   */
  async createItem(
    data: CreateItemData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateItemData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'Item name is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 2 || data.name.length > 255) {
          return { field: 'name', message: 'Item name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateItemData): ValidationError | null => {
        if (!data.price || data.price.trim().length === 0) {
          return { field: 'price', message: 'Price is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const priceValue = parseFloat(data.price);
        if (isNaN(priceValue) || priceValue < 0) {
          return { field: 'price', message: 'Price must be a valid positive number', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateItemData): ValidationError | null => {
        if (!data.category) {
          return { field: 'category', message: 'Category is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const validCategories = ['equipment', 'clothing', 'accessories', 'other'];
        if (!validCategories.includes(data.category)) {
          return { field: 'category', message: 'Invalid item category', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateItemData): ValidationError | null => {
        if (data.description && data.description.length > 1000) {
          return { field: 'description', message: 'Description must be no more than 1000 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateItemData): ValidationError | null => {
        if (data.stock !== undefined && data.stock !== null && data.stock < 0) {
          return { field: 'stock', message: 'Stock must be a positive number', code: 'INVALID_VALUE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createItem',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check for duplicate item name
        const existingItem = await TenantAwareDB.getItemByName(validatedData.name, effectiveTenantId || undefined);
        if (existingItem) {
          throw new BusinessRuleError('duplicate_name', 'An item with this name already exists');
        }

        return TenantAwareDB.createItem(validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'item',
      }
    );
  }

  /**
   * Update an item
   */
  async updateItem(
    id: string,
    data: UpdateItemData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateItemData): ValidationError | null => {
        if (data.name !== undefined) {
          if (!data.name || data.name.trim().length === 0) {
            return { field: 'name', message: 'Item name cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.name.length < 2 || data.name.length > 255) {
            return { field: 'name', message: 'Item name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateItemData): ValidationError | null => {
        if (data.price !== undefined) {
          if (!data.price || data.price.trim().length === 0) {
            return { field: 'price', message: 'Price cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const priceValue = parseFloat(data.price);
          if (isNaN(priceValue) || priceValue < 0) {
            return { field: 'price', message: 'Price must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateItemData): ValidationError | null => {
        if (data.category !== undefined) {
          const validCategories = ['equipment', 'clothing', 'accessories', 'other'];
          if (!validCategories.includes(data.category)) {
            return { field: 'category', message: 'Invalid item category', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateItemData): ValidationError | null => {
        if (data.description !== undefined && data.description && data.description.length > 1000) {
          return { field: 'description', message: 'Description must be no more than 1000 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: UpdateItemData): ValidationError | null => {
        if (data.stock !== undefined && data.stock !== null && data.stock < 0) {
          return { field: 'stock', message: 'Stock must be a positive number', code: 'INVALID_VALUE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateItem',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if item exists
        const existingItem = await TenantAwareDB.getItemById(id, effectiveTenantId || undefined);
        if (!existingItem) {
          throw new NotFoundError('Item not found');
        }

        // Check for duplicate item name (excluding current item)
        if (validatedData.name) {
          const duplicateItem = await TenantAwareDB.getItemByName(validatedData.name, effectiveTenantId || undefined);
          if (duplicateItem && duplicateItem.id !== id) {
            throw new BusinessRuleError('duplicate_name', 'An item with this name already exists');
          }
        }

        return TenantAwareDB.updateItem(id, validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'item',
      }
    );
  }

  /**
   * Delete an item
   */
  async deleteItem(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteItem',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if item exists
        const existingItem = await TenantAwareDB.getItemById(id, effectiveTenantId || undefined);
        if (!existingItem) {
          throw new NotFoundError('Item not found');
        }

        // Check for pending purchases
        const pendingPurchases = await TenantAwareDB.getPendingPurchasesByItemId(id, effectiveTenantId || undefined);
        if (pendingPurchases && pendingPurchases.length > 0) {
          throw new BusinessRuleError('has_pending_purchases', 'Cannot delete item with pending purchases');
        }

        await TenantAwareDB.deleteItem(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'item',
      }
    );
  }

  /**
   * Update item stock
   */
  async updateItemStock(
    id: string,
    newStock: number,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (): ValidationError | null => {
        if (newStock < 0) {
          return { field: 'stock', message: 'Stock must be a positive number', code: 'INVALID_VALUE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateItemStock',
      { stock: newStock },
      validationFunctions,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if item exists
        const existingItem = await TenantAwareDB.getItemById(id, effectiveTenantId || undefined);
        if (!existingItem) {
          throw new NotFoundError('Item not found');
        }

        return TenantAwareDB.updateItem(id, { stock: newStock }, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'item',
        metadata: { operation: 'stock_update', newStock },
      }
    );
  }

  /**
   * Get items by category
   */
  async getItemsByCategory(
    category: 'equipment' | 'clothing' | 'accessories' | 'other',
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getItemsByCategory',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getItemsByCategory(category, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'items',
        metadata: { category },
      }
    );
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(
    threshold: number = 5,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getLowStockItems',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getLowStockItems(threshold, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'items',
        metadata: { threshold },
      }
    );
  }
}

// Factory function
export const itemService = () => new ItemService();
