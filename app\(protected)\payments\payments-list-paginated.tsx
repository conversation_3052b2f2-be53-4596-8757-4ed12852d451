"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, FileText, CreditCard, Search, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { createPaymentColumns } from "@/components/payments-table-columns";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Payment, PaymentPlan } from "@/lib/types";

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface PaymentsListPaginatedProps {
  initialData: Payment[];
  initialPagination: PaginationData;
  plans: PaymentPlan[];
  initialSearchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    tab?: string;
    status?: string;
    type?: string;
    fromDate?: string;
    toDate?: string;
  };
}

export function PaymentsListPaginated({ 
  initialData, 
  initialPagination, 
  plans,
  initialSearchParams 
}: PaymentsListPaginatedProps) {
  const { t } = useSafeTranslation();
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [payments, setPayments] = useState<Payment[]>(initialData);
  const [loading] = useState(false);

  // Update payments when initialData changes (when server component re-renders)
  useEffect(() => {
    console.log('Client received data:', {
      dataLength: initialData.length,
      pagination: initialPagination
    });
    
    // Force the component to use the full dataset received from the server
    setPayments([...initialData]);
  }, [initialData, initialPagination]);

  // Read URL params for server-side values (read-only)
  const currentSearchParams = searchParams;
  const search = currentSearchParams.get('search') || "";
  const sortBy = currentSearchParams.get('sortBy') || "date";
  const sortOrder = (currentSearchParams.get('sortOrder') as 'asc' | 'desc') || "desc";
  const currentLimit = currentSearchParams.get('limit') || "10";
  const activeTab = currentSearchParams.get('tab') || "payments";
  
  // Local state for UI interactions - initialize once from URL, never sync back
  const [localSearch, setLocalSearch] = useState(() => initialSearchParams.search || "");
  const [filters, setFilters] = useState(() => ({
    status: initialSearchParams.status || "",
    type: initialSearchParams.type || "",
    fromDate: initialSearchParams.fromDate || "",
    toDate: initialSearchParams.toDate || "",
  }));
  const [showFilters, setShowFilters] = useState(() => 
    !!(initialSearchParams.status || initialSearchParams.type || 
       initialSearchParams.fromDate || initialSearchParams.toDate)
  );

  // Calculate if search is valid (empty or has at least 3 characters)
  const isSearchValid = useMemo(() => {
    return localSearch.trim().length === 0 || localSearch.trim().length >= 3;
  }, [localSearch]);

  const paymentColumns = useMemo(() => createPaymentColumns(t), [t]);

  // Function to update URL with new search params
  const updateURL = useCallback((newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove undefined values and update params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Only reset to page 1 when filters/search/sort/limit change, but NOT when page is explicitly being changed
    const shouldResetPage = !('page' in newParams) && !('tab' in newParams);
    if (shouldResetPage) {
      params.set('page', '1');
    }

    const finalURL = `/payments?${params.toString()}`;
    router.push(finalURL);
  }, [router, searchParams]);

  // Track the previous search value to only trigger on actual changes
  const [prevLocalSearch, setPrevLocalSearch] = useState(localSearch);
  
  useEffect(() => {
    // Only trigger if localSearch actually changed (not just component re-render)
    if (localSearch !== prevLocalSearch) {
      setPrevLocalSearch(localSearch);
      
      const shouldTriggerSearch = localSearch.trim().length === 0 || localSearch.trim().length >= 3;
      
      if (shouldTriggerSearch) {
        const timeoutId = setTimeout(() => {
          updateURL({ search: localSearch.trim() || undefined });
        }, 300); // 300ms debounce
        
        return () => clearTimeout(timeoutId);
      }
    }
  }, [localSearch, prevLocalSearch, updateURL]);

  const handlePageChange = (newPage: number) => {
    updateURL({ page: newPage.toString() });
  };

  const handleLimitChange = (newLimit: string) => {
    updateURL({ limit: newLimit });
  };

  const handleTabChange = (value: string) => {
    updateURL({ tab: value });
  };

  const handleSearch = () => {
    // Manual search - trigger immediate search
    updateURL({ search: localSearch.trim() || undefined });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(filters)
        .filter(([_, value]) => value.trim() !== "")
        .map(([key, value]) => [key, value === 'all' ? '' : value])
    );
    updateURL({
      status: cleanFilters.status || undefined,
      type: cleanFilters.type || undefined,
      fromDate: cleanFilters.fromDate || undefined,
      toDate: cleanFilters.toDate || undefined,
    });
  };

  const handleClearFilters = () => {
    setFilters({
      status: "",
      type: "",
      fromDate: "",
      toDate: "",
    });
    updateURL({
      search: undefined,
      status: undefined,
      type: undefined,
      fromDate: undefined,
      toDate: undefined,
    });
  };

  const handleSortChange = (field: string) => {
    const newSortOrder = sortBy === field ? (sortOrder === 'asc' ? 'desc' : 'asc') : 'asc';
    updateURL({ 
      sortBy: field, 
      sortOrder: newSortOrder 
    });
  };

  const handleSortOrderChange = (newSortOrder: 'asc' | 'desc') => {
    updateURL({ sortOrder: newSortOrder });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('payments.title')}</h1>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/payments/new">
              <CreditCard className="mr-2 h-4 w-4" />
              {t('payments.recordPayment')}
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/payments/plans/new">
              <FileText className="mr-2 h-4 w-4" />
              {t('payments.newPaymentPlan')}
            </Link>
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="payments" onValueChange={handleTabChange} value={activeTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="payments">{t('payments.tabPayments')}</TabsTrigger>
          <TabsTrigger value="plans">{t('payments.tabPlans')}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="payments">
          <Card>
            <CardContent className="pt-6">
              {/* Search and Filters */}
              <div className="space-y-4 mb-6">
                {/* Search Bar */}
                <div className="flex gap-2">
                  <div className="flex-1 relative">
                    <Input
                      placeholder={t('payments.placeholders.searchPayments')}
                      value={localSearch}
                      onChange={(e) => setLocalSearch(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      className={!isSearchValid ? "border-yellow-500 focus:border-yellow-500" : ""}
                    />
                    {localSearch.trim().length > 0 && localSearch.trim().length < 3 && (
                      <div className="absolute top-full left-0 mt-1 text-xs text-yellow-600 dark:text-yellow-400">
                        {t('payments.placeholders.searchMinChars')}
                      </div>
                    )}
                  </div>
                  <Button 
                    onClick={handleSearch} 
                    variant="outline"
                    disabled={!isSearchValid}
                  >
                    <Search className="h-4 w-4 mr-2" />
                    {t('common.actions.search')}
                  </Button>
                  <Button 
                    onClick={() => setShowFilters(!showFilters)} 
                    variant="outline"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {t('common.actions.filter')}
                  </Button>
                </div>

                {/* Column Filters */}
                {showFilters && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
                    <div className="space-y-2">
                      <Label htmlFor="statusFilter">{t('payments.statusLabel')}</Label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) => handleFilterChange('status', value)}
                      >
                        <SelectTrigger id="statusFilter">
                          <SelectValue placeholder={t('payments.placeholders.filterByStatus')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{t('common.all')}</SelectItem>
                          <SelectItem value="pending">{t('payments.status.pending')}</SelectItem>
                          <SelectItem value="completed">{t('payments.status.completed')}</SelectItem>
                          <SelectItem value="overdue">{t('payments.status.overdue')}</SelectItem>
                          <SelectItem value="cancelled">{t('payments.status.cancelled')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="typeFilter">{t('payments.typeLabel')}</Label>
                      <Select
                        value={filters.type}
                        onValueChange={(value) => handleFilterChange('type', value)}
                      >
                        <SelectTrigger id="typeFilter">
                          <SelectValue placeholder={t('payments.placeholders.filterByType')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{t('common.all')}</SelectItem>
                          <SelectItem value="fee">{t('payments.types.fee')}</SelectItem>
                          <SelectItem value="equipment">{t('payments.types.equipment')}</SelectItem>
                          <SelectItem value="other">{t('payments.types.other')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="fromDateFilter">{t('payments.fromDate')}</Label>
                      <Input
                        id="fromDateFilter"
                        type="date"
                        value={filters.fromDate}
                        onChange={(e) => handleFilterChange('fromDate', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="toDateFilter">{t('payments.toDate')}</Label>
                      <Input
                        id="toDateFilter"
                        type="date"
                        value={filters.toDate}
                        onChange={(e) => handleFilterChange('toDate', e.target.value)}
                      />
                    </div>
                    <div className="flex gap-2 md:col-span-2 lg:col-span-5">
                      <Button onClick={handleApplyFilters} size="sm">
                        {t('common.actions.apply')}
                      </Button>
                      <Button onClick={handleClearFilters} variant="outline" size="sm">
                        {t('common.actions.clear')}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Sort and Page Size Controls */}
                <div className="flex flex-wrap gap-4 items-center">
                  <div className="flex items-center space-x-2">
                    <Label>{t('common.sortBy')}:</Label>
                    <Select 
                      value={sortBy} 
                      onValueChange={(field) => handleSortChange(field)}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="date">{t('payments.date')}</SelectItem>
                        <SelectItem value="dueDate">{t('payments.dueDate')}</SelectItem>
                        <SelectItem value="amount">{t('payments.amount')}</SelectItem>
                        <SelectItem value="status">{t('payments.statusLabel')}</SelectItem>
                        <SelectItem value="type">{t('payments.typeLabel')}</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select 
                      value={sortOrder} 
                      onValueChange={(value) => handleSortOrderChange(value as 'asc' | 'desc')}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="asc">{t('common.ascending')}</SelectItem>
                        <SelectItem value="desc">{t('common.descending')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Label>{t('common.pageSize')}:</Label>
                    <Select value={currentLimit} onValueChange={handleLimitChange}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Data Table */}
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
                  ))}
                </div>
              ) : (
                <>
                  <DataTable 
                    columns={paymentColumns} 
                    data={payments}
                    showPagination={false}
                    initialPageSize={parseInt(currentLimit)}
                  />
                  
                  {/* Debug info */}
                  <div className="text-xs text-muted-foreground mt-2">
                    Displaying {payments.length} records (from total {initialPagination.total})
                  </div>

                  {/* Pagination Controls */}
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-muted-foreground">
                      {t('common.pagination.showing', {
                        start: initialPagination.total === 0 ? 0 : (initialPagination.page - 1) * initialPagination.limit + 1,
                        end: Math.min(initialPagination.page * initialPagination.limit, initialPagination.total),
                        total: initialPagination.total
                      })}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(initialPagination.page - 1)}
                        disabled={!initialPagination.hasPreviousPage}
                      >
                        <ChevronLeft className="h-4 w-4 mr-1" />
                        {t('common.pagination.previous')}
                      </Button>
                      
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: Math.min(5, initialPagination.totalPages) }, (_, i) => {
                          let pageNum : number;
                          if (initialPagination.totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (initialPagination.page <= 3) {
                            pageNum = i + 1;
                          } else if (initialPagination.page >= initialPagination.totalPages - 2) {
                            pageNum = initialPagination.totalPages - 4 + i;
                          } else {
                            pageNum = initialPagination.page - 2 + i;
                          }
                          
                          return (
                            <Button
                              key={pageNum}
                              variant={initialPagination.page === pageNum ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              className="w-8 h-8 p-0"
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(initialPagination.page + 1)}
                        disabled={!initialPagination.hasNextPage}
                      >
                        {t('common.pagination.next')}
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="plans">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              return (
                <Card key={plan.id}>
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>
                      {t('payments.plans.monthlyValue')}: {plan.monthlyValue} {t('common.currency')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Monthly Value */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.monthlyValue')}:</span>
                        <span className="font-medium">{plan.monthlyValue} {t('common.currency')}</span>
                      </div>
                      
                      {/* Payment Schedule */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.assignDay')}:</span>
                        <span className="font-medium">{plan.assignDay}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.dueDay')}:</span>
                        <span className="font-medium">{plan.dueDay}</span>
                      </div>
                      
                      {/* Status */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.statusLabel')}:</span>
                        <span className="capitalize">{t(`payments.planEdit.status.${plan.status}`, plan.status)}</span>
                      </div>
                      
                      {/* Description */}
                      {plan.description && (
                        <div className="mt-2">
                          <span className="text-sm text-muted-foreground">{plan.description}</span>
                        </div>
                      )}
                      
                      {/* Branches */}
                      {plan.branches && plan.branches.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4">
                          {plan.branches.map((branch) => (
                            <span
                              key={branch.id}
                              className="bg-secondary text-secondary-foreground text-xs rounded-full px-2 py-1"
                            >
                              {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}`}>{t('payments.actions.view')}</Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}/edit`}>{t('payments.actions.edit')}</Link>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
            
            <Link href="/payments/plans/new" className="block">
              <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
                    <PlusCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    {t('payments.createPlan')}
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
