/**
 * Payment Scheduler - <PERSON>les automatic payment creation and overdue status updates
 * This file can be imported by a cron job or scheduled task system
 */

import { createScheduledPayments, updateOverduePayments } from '@/lib/payment-plan-utils';
import { db } from '@/src/db';
import { schools } from '@/src/db/schema';

/**
 * Scheduler function that processes all payments status
 * This should be called by a cron job daily. Check if the payment is due based on the assign day.
 */
export async function runUpdatePaymentStatus() {
  try {
    console.log('Payment scheduler started at:', new Date().toISOString());
    
    // Get all active tenants
    const allTenants = await db
      .selectDistinct({ tenantId: schools.tenantId })
      .from(schools);

    console.log(`Processing ${allTenants.length} tenant(s)`);

    const results = {
      tenantsProcessed: 0,
      paymentsCreated: 0,
      paymentsMarkedOverdue: 0,
      errors: [] as string[],
    };

    // Process each tenant
    for (const tenant of allTenants) {
      try {
        console.log(`Processing tenant: ${tenant.tenantId}`);
        
        // System user ID for automated operations
        const systemUserId = BigInt(0);

        // Update overdue payments
        const overduePayments = await updateOverduePayments(tenant.tenantId, systemUserId);
        results.paymentsMarkedOverdue += overduePayments.length;
        
        if (overduePayments.length > 0) {
          console.log(`Marked ${overduePayments.length} payments as overdue for tenant ${tenant.tenantId}`);
        }
        
        results.tenantsProcessed++;
        
      } catch (error) {
        const errorMessage = `Error processing tenant ${tenant.tenantId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMessage);
        results.errors.push(errorMessage);
      }
    }

    console.log('Payment scheduler completed at:', new Date().toISOString());
    console.log('Results:', results);
    
    return results;
    
  } catch (error) {
    console.error('Payment scheduler failed:', error);
    throw error;
  }
}

/**
 * Function to create payments for a specific tenant (useful for testing)
 */
export async function runPaymentCreationForTenant(tenantId: string) {
  const systemUserId = BigInt(0);
  return createScheduledPayments(tenantId, systemUserId);
}

/**
 * Function to update overdue payments for a specific tenant (useful for testing)
 */
export async function runOverdueUpdateForTenant(tenantId: string) {
  const systemUserId = BigInt(0);
  return updateOverduePayments(tenantId, systemUserId);
}

// Example usage in a Next.js API route:
// export async function GET() {
//   try {
//     const results = await runPaymentScheduler();
//     return Response.json(results);
//   } catch (error) {
//     return Response.json({ error: 'Scheduler failed' }, { status: 500 });
//   }
// }
