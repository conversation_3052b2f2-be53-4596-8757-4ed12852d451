import { eq, and, desc, asc, gte, lte, like, SQL, or, sql } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';
import { formatDateToLocalString } from '../utils/date-formatter';

export class ExpensesDB extends TenantAwareDBBase {

  static async getExpensesPaginated(
    options: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      category?: string;
      fromDate?: string;
      toDate?: string;
      instructorId?: string;
      facilityId?: string;
    } = {},
    tenantId?: string
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'date',
      sortOrder = 'desc',
      category = '',
      fromDate = '',
      toDate = '',
      instructorId = '',
      facilityId = '',
    } = options;

    // Calculate offset
    const offset = (page - 1) * limit;
    
    // Build conditions
    const conditions: SQL[] = [eq(schema.expenses.tenantId, filter.tenantId)];
    
    // Search
    if (search) {
      conditions.push(like(schema.expenses.description, `%${search}%`));
    }
    
    // Category filter
    if (category) {
      conditions.push(eq(schema.expenses.category, category as any));
    }
    
    // Date range filter
    if (fromDate) {
      conditions.push(gte(schema.expenses.date, fromDate));
    }
    
    if (toDate) {
      conditions.push(lte(schema.expenses.date, toDate));
    }
    
    // Instructor filter
    if (instructorId) {
      conditions.push(eq(schema.expenses.instructorId, instructorId));
    }
    
    // Facility filter
    if (facilityId) {
      conditions.push(eq(schema.expenses.facilityId, facilityId));
    }
    
    // Determine sort column and direction
    let orderByClause;
    if (sortBy === 'amount') {
      orderByClause = sortOrder === 'asc' ? asc(schema.expenses.amount) : desc(schema.expenses.amount);
    } else if (sortBy === 'category') {
      orderByClause = sortOrder === 'asc' ? asc(schema.expenses.category) : desc(schema.expenses.category);
    } else if (sortBy === 'description') {
      orderByClause = sortOrder === 'asc' ? asc(schema.expenses.description) : desc(schema.expenses.description);
    } else {
      // Default to date
      orderByClause = sortOrder === 'asc' ? asc(schema.expenses.date) : desc(schema.expenses.date);
    }
    
    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)`.mapWith(Number) })
      .from(schema.expenses)
      .where(and(...conditions));
    
    const total = totalCountResult[0]?.count || 0;
    
    // Execute main query with pagination
    const result = await db.select({
      id: schema.expenses.id,
      tenantId: schema.expenses.tenantId,
      amount: schema.expenses.amount,
      date: schema.expenses.date,
      category: schema.expenses.category,
      description: schema.expenses.description,
      createdAt: schema.expenses.createdAt,
      updatedAt: schema.expenses.updatedAt,
      createdBy: schema.expenses.createdBy,
      updatedBy: schema.expenses.updatedBy,
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
        type: schema.facilities.type,
      }
    })
    .from(schema.expenses)
    .leftJoin(schema.instructors, eq(schema.expenses.instructorId, schema.instructors.id))
    .leftJoin(schema.facilities, eq(schema.expenses.facilityId, schema.facilities.id))
    .where(and(...conditions))
    .orderBy(orderByClause)
    .limit(limit)
    .offset(offset);
    
    const expenses = result.map(expense => ({
      ...expense,
      instructor: expense.instructor?.id ? expense.instructor : null,
      facility: expense.facility?.id ? expense.facility : null,
    }));
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    
    return {
      data: expenses,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }
  
  static async getExpenses(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.expenses.id,
      tenantId: schema.expenses.tenantId,
      amount: schema.expenses.amount,
      date: schema.expenses.date,
      category: schema.expenses.category,
      description: schema.expenses.description,
      createdAt: schema.expenses.createdAt,
      updatedAt: schema.expenses.updatedAt,
      createdBy: schema.expenses.createdBy,
      updatedBy: schema.expenses.updatedBy,
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
        type: schema.facilities.type,
      }
    })
    .from(schema.expenses)
    .leftJoin(schema.instructors, eq(schema.expenses.instructorId, schema.instructors.id))
    .leftJoin(schema.facilities, eq(schema.expenses.facilityId, schema.facilities.id))
    .where(eq(schema.expenses.tenantId, filter.tenantId))
    .orderBy(desc(schema.expenses.date));
    
    return result.map(expense => ({
      ...expense,
      instructor: expense.instructor?.id ? expense.instructor : null,
      facility: expense.facility?.id ? expense.facility : null,
    }));
  }

  static async getExpenseById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.expenses.id,
      tenantId: schema.expenses.tenantId,
      amount: schema.expenses.amount,
      date: schema.expenses.date,
      category: schema.expenses.category,
      description: schema.expenses.description,
      instructorId: schema.expenses.instructorId,
      facilityId: schema.expenses.facilityId,
      createdAt: schema.expenses.createdAt,
      updatedAt: schema.expenses.updatedAt,
      createdBy: schema.expenses.createdBy,
      updatedBy: schema.expenses.updatedBy,
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
        type: schema.facilities.type,
      }
    })
    .from(schema.expenses)
    .leftJoin(schema.instructors, eq(schema.expenses.instructorId, schema.instructors.id))
    .leftJoin(schema.facilities, eq(schema.expenses.facilityId, schema.facilities.id))
    .where(and(
      eq(schema.expenses.id, id),
      eq(schema.expenses.tenantId, filter.tenantId)
    ));
    
    if (!result[0]) return null;
    
    const expense = result[0];
    return {
      ...expense,
      instructor: expense.instructor?.id ? expense.instructor : null,
      facility: expense.facility?.id ? expense.facility : null,
    };
  }

  static async createExpense(
    data: Omit<typeof schema.expenses.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.expenses, data, tenantId, userId);
  }

  static async updateExpense(
    id: string, 
    data: Partial<typeof schema.expenses.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.expenses, id, data, tenantId, userId);
  }

  static async deleteExpense(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.expenses, id, tenantId);
  }

  static async getExpensesByCategory(category: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      id: schema.expenses.id,
      tenantId: schema.expenses.tenantId,
      amount: schema.expenses.amount,
      date: schema.expenses.date,
      category: schema.expenses.category,
      description: schema.expenses.description,
      createdAt: schema.expenses.createdAt,
      updatedAt: schema.expenses.updatedAt,
      createdBy: schema.expenses.createdBy,
      updatedBy: schema.expenses.updatedBy,
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
        type: schema.facilities.type,
      }
    })
    .from(schema.expenses)
    .leftJoin(schema.instructors, eq(schema.expenses.instructorId, schema.instructors.id))
    .leftJoin(schema.facilities, eq(schema.expenses.facilityId, schema.facilities.id))
    .where(and(
      eq(schema.expenses.tenantId, filter.tenantId),
      eq(schema.expenses.category, category as any)
    ))
    .orderBy(desc(schema.expenses.date));
    
    return result.map(expense => ({
      ...expense,
      instructor: expense.instructor?.id ? expense.instructor : null,
      facility: expense.facility?.id ? expense.facility : null,
    }));
  }

  static async getExpensesByDateRange(startDate: string | Date, endDate: string | Date, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Convert dates to strings if they're Date objects
    const startDateStr = startDate instanceof Date ? formatDateToLocalString(startDate) : startDate;
    const endDateStr = endDate instanceof Date ? formatDateToLocalString(endDate) : endDate;
    
    const result = await db.select({
      id: schema.expenses.id,
      tenantId: schema.expenses.tenantId,
      amount: schema.expenses.amount,
      date: schema.expenses.date,
      category: schema.expenses.category,
      description: schema.expenses.description,
      createdAt: schema.expenses.createdAt,
      updatedAt: schema.expenses.updatedAt,
      createdBy: schema.expenses.createdBy,
      updatedBy: schema.expenses.updatedBy,
      instructor: {
        id: schema.instructors.id,
        name: schema.instructors.name,
        surname: schema.instructors.surname,
      },
      facility: {
        id: schema.facilities.id,
        name: schema.facilities.name,
        type: schema.facilities.type,
      }
    })
    .from(schema.expenses)
    .leftJoin(schema.instructors, eq(schema.expenses.instructorId, schema.instructors.id))
    .leftJoin(schema.facilities, eq(schema.expenses.facilityId, schema.facilities.id))
    .where(and(
      eq(schema.expenses.tenantId, filter.tenantId),
      gte(schema.expenses.date, startDateStr),
      lte(schema.expenses.date, endDateStr)
    ))
    .orderBy(desc(schema.expenses.date));
    
    return result.map(expense => ({
      ...expense,
      instructor: expense.instructor?.id ? expense.instructor : null,
      facility: expense.facility?.id ? expense.facility : null,
    }));
  }
}
