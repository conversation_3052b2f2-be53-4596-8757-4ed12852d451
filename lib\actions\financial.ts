"use server";

import { financialService } from '../services';
import { getServerTenantId } from '../tenant-utils-server';

// Financial Summary
export async function getFinancialSummary() {
  try {
    const tenantId = await getServerTenantId();
    const result = await financialService().getFinancialSummary(tenantId || undefined);
    
    if (!result.success) {
      console.error("getFinancialSummary error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get financial summary");
    }
    
    return result.data;
  } catch (error) {
    console.error("getFinancialSummary error:", error);
    throw error;
  }
}