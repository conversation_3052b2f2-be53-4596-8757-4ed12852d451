"use client";

import type { LucideIcon } from "lucide-react"; // Changed from specific icon import to type import
import Link from "next/link";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface DashboardCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  bgColor?: string;
  textColor?: string;
}

export function DashboardCard({
  title,
  description,
  icon: Icon,
  href,
  bgColor = "bg-card",
  textColor = "text-card-foreground",
}: DashboardCardProps) {
  return (
    <Link href={href}>
      <motion.div
        whileHover={{ scale: 1.03 }}
        className={cn(
          "relative p-6 rounded-lg shadow-md transition-all duration-200",
          bgColor,
          textColor
        )}
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <Icon className="h-6 w-6" />
        </div>
        <p className="text-sm opacity-90">{description}</p>
      </motion.div>
    </Link>
  );
}

