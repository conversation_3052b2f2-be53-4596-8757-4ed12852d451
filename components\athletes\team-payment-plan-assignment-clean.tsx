"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Trash2, Plus } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { assignPaymentPlanAction, deactivatePaymentPlanAction } from "./team-payment-plan-actions";
import {useToast} from "@/hooks/use-toast";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Team {
  id: string;
  name: string;
  branchId: string;
  branchName?: string;
  schoolId: string;
  schoolName?: string;
}

interface AthleteTeam {
  teamId: string;
  teamName: string;
  branchName: string;
  branchId: string;
  schoolName: string;
  schoolId: string;
  instructorName: string;
  instructorSurname: string;
  joinedAt: string;
  leftAt?: string | null;
}

interface AthletePaymentPlan {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

interface TeamPaymentPlanAssignmentProps {
  athleteId: string;
  athleteTeams: AthleteTeam[];
  availableTeams: Team[];
  athletePaymentPlans: AthletePaymentPlan[];
  availablePaymentPlans: PaymentPlan[];
}

export function TeamPaymentPlanAssignment({ 
  athleteId,
  athleteTeams,
  availableTeams,
  athletePaymentPlans,
  availablePaymentPlans
}: TeamPaymentPlanAssignmentProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [selectedTeamId, setSelectedTeamId] = useState("");
  const [selectedPlanId, setSelectedPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleAssignPaymentPlan = async () => {
    if (!selectedPlanId || !selectedTeamId) {
      toast({
        title: t('common.error'),
        description: t('payments.plans.assignments.selectRequired'),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Check if athlete is already in the team
      const isAthleteInTeam = athleteTeams.some(team => team.teamId === selectedTeamId);
      
      const result = await assignPaymentPlanAction(
        athleteId,
        selectedPlanId,
        selectedTeamId,
        isAthleteInTeam
      );

      if (result.success) {
        toast({
          title: t('common.success'),
          description: t('payments.plans.assignments.success'),
        });
        setIsDialogOpen(false);
        setSelectedPlanId("");
        setSelectedTeamId("");
      } else {
        toast({
          title: t('common.error'),
          description: result.error || t('payments.plans.assignments.error'),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error assigning payment plan:", error);
      toast({
        title: t('common.error'),
        description: t('payments.plans.assignments.error'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeactivatePaymentPlan = async (assignmentId: string) => {
    try {
      const result = await deactivatePaymentPlanAction(assignmentId, athleteId);
      
      if (result.success) {
        toast({
          title: t('common.success'),
          description: t('payments.plans.assignments.deactivated'),
        });
      } else {
        toast({
          title: t('common.error'),
          description: result.error || t('common.error'),
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deactivating payment plan:", error);
      toast({
        title: t('common.error'),
        description: t('common.error'),
        variant: "destructive",
      });
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  // Filter payment plans based on selected team's branch
  const filteredPaymentPlans = selectedTeamId 
    ? availablePaymentPlans.filter(plan => {
        const selectedTeam = availableTeams.find(t => t.id === selectedTeamId);
        return plan.branches?.some(branch => branch.id === selectedTeam?.branchId) || !plan.branches?.length;
      })
    : availablePaymentPlans;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">{t('payments.plans.assignments.title')}</h3>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              {t('payments.plans.assignments.assign')}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t('payments.plans.assignments.assignToTeam')}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>{t('teams.details.team')} *</Label>
                <Select value={selectedTeamId} onValueChange={setSelectedTeamId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('teams.details.selectTeam')} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTeams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name} ({t(`common.branches.${team.branchName}`, { ns: 'shared' })})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>{t('payments.plans.plan')} *</Label>
                <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('payments.plans.select')} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredPaymentPlans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id}>
                        {plan.name} - {formatCurrency(plan.monthlyValue)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  {t('common.actions.cancel')}
                </Button>
                <Button
                  onClick={handleAssignPaymentPlan}
                  disabled={isLoading || !selectedPlanId || !selectedTeamId}
                >
                  {isLoading ? t('common.actions.saving') : t('common.actions.assign')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Current Payment Plan Assignments */}
      <div className="space-y-3">
        {athletePaymentPlans.length === 0 ? (
          <Card>
            <CardContent className="py-6 text-center text-muted-foreground">
              {t('payments.plans.assignments.noAssignments')}
            </CardContent>
          </Card>
        ) : (
          athletePaymentPlans.map((plan) => (
            <Card key={plan.id}>
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{plan.planName}</h4>
                      <Badge variant={plan.isActive ? "default" : "secondary"}>
                        {plan.isActive ? t('common.active') : t('common.inactive')}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(plan.monthlyValue)} • {t('payments.plans.assignDay')}: {plan.assignDay} • {t('payments.plans.dueDay')}: {plan.dueDay}
                    </div>
                    {plan.teamName && (
                      <div className="text-sm text-muted-foreground">
                        {t('teams.details.team')}: {plan.teamName}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground">
                      {t('payments.plans.assignments.assignedDate')}: {new Date(plan.assignedDate).toLocaleDateString()}
                    </div>
                  </div>
                  {plan.isActive && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeactivatePaymentPlan(plan.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
