{"instructors": {"title": "Instructors", "details": {"title": "Instructor <PERSON>", "personalInfo": "Personal Information", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "surname": "Surname", "email": "Email", "phone": "Phone", "nationalId": "National ID", "birthDate": "Birth Date", "address": "Address", "salary": "Salary", "specializations": "Specializations", "schools": "Schools", "branches": "Branches", "quickStats": "Quick Stats"}, "form": {"title": "Create New Instructor", "editTitle": "Edit Instructor", "description": "Add a new instructor to your organization", "name": "Name", "surname": "Surname", "email": "Email", "phone": "Phone", "nationalId": "National ID", "birthDate": "Birth Date", "address": "Address", "salary": "Salary", "branches": "Branches", "schools": "Schools", "namePlaceholder": "Enter instructor's name", "surnamePlaceholder": "Enter instructor's surname", "emailPlaceholder": "Enter instructor's email", "phonePlaceholder": "Enter instructor's phone number", "nationalIdPlaceholder": "Enter national ID", "birthDatePlaceholder": "Select birth date", "addressPlaceholder": "Enter address", "salaryPlaceholder": "Enter salary amount", "selectBranches": "Select branches...", "selectSchools": "Select schools..."}, "actions": {"add": "Add Instructor", "addNew": "Add Instructor", "create": "Create Instructor", "edit": "Edit", "update": "Update Instructor", "delete": "Delete", "createInstructor": "Create Instructor", "openMenu": "Open menu"}, "messages": {"manageInstructors": "Manage your instructors and their assignments", "allInstructors": "All Instructors", "instructorsList": "A list of all instructors in your organization", "createSuccess": "Instructor created successfully", "createError": "Failed to create instructor", "loadError": "Failed to load instructors", "schoolCount": "{{count}} school", "schoolCount_other": "{{count}} schools", "updateSuccess": "Success", "instructorUpdated": "Instructor updated successfully", "updateError": "Failed to update instructor", "updateFailed": "Failed to update instructor", "deleteSuccess": "Success", "instructorDeleted": "Instructor deleted successfully", "deleteError": "Failed to delete instructor", "deleteConfirmTitle": "Are you absolutely sure?", "deleteConfirmDescription": "This action cannot be undone. This will permanently delete {{name}} and remove all related data.", "deleting": "Deleting..."}, "placeholders": {"searchInstructors": "Search instructors...", "searchMinChars": "Type at least 3 characters to search"}}}