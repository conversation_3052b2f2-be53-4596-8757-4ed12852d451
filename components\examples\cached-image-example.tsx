"use client";

/**
 * Example component demonstrating the SecureImage caching functionality
 * 
 * This component shows:
 * - Basic usage with caching enabled (default)
 * - Cache management utilities
 * - Performance monitoring
 * - Manual cache control
 */

import { useState } from 'react';
import { SecureImage } from '@/components/ui/secure-image';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  getImageCacheStats,
  clearAllImageCache,
  clearTenantImageCache,
  invalidateImage,
  preloadImages,
  useImageCacheDebug,
} from '@/lib/image-cache-utils';
import { useSession } from 'next-auth/react';

interface CachedImageExampleProps {
  sampleImages?: string[];
}

export function CachedImageExample({ 
  sampleImages = [
    '/api/secure-images/tenant-123/sample1.jpg',
    '/api/secure-images/tenant-123/sample2.jpg',
    '/api/secure-images/tenant-123/sample3.jpg',
  ]
}: CachedImageExampleProps) {
  const { data: session } = useSession();
  const [selectedImage, setSelectedImage] = useState(sampleImages[0]);
  const [cacheStats, setCacheStats] = useState(getImageCacheStats());
  const [isPreloading, setIsPreloading] = useState(false);
  
  // Debug utilities (only available in development)
  const debug = useImageCacheDebug();

  const refreshStats = () => {
    setCacheStats(getImageCacheStats());
  };

  const handleClearAll = () => {
    clearAllImageCache();
    refreshStats();
  };

  const handleClearTenant = () => {
    if (session?.tenantId) {
      clearTenantImageCache(session.tenantId);
      refreshStats();
    }
  };

  const handleInvalidateImage = () => {
    if (session?.tenantId) {
      invalidateImage(selectedImage, session.tenantId);
      refreshStats();
    }
  };

  const handlePreloadImages = async () => {
    if (!session?.tenantId) return;
    
    setIsPreloading(true);
    try {
      await preloadImages(
        sampleImages.map(src => ({ src, tenantId: session.tenantId! }))
      );
      refreshStats();
    } catch (error) {
      console.error('Failed to preload images:', error);
    } finally {
      setIsPreloading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>SecureImage with Caching</CardTitle>
          <CardDescription>
            Demonstration of client-side image caching for authenticated images
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Image Display */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Image Display</h3>
              
              {/* Main Image */}
              <div className="relative aspect-square border rounded-lg overflow-hidden">
                <SecureImage
                  src={selectedImage}
                  alt="Sample cached image"
                  fill
                  className="object-cover"
                  priority
                />
              </div>

              {/* Image Selection */}
              <div className="flex gap-2">
                {sampleImages.map((src, index) => (
                  <Button
                    key={src}
                    variant={selectedImage === src ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedImage(src)}
                  >
                    Image {index + 1}
                  </Button>
                ))}
              </div>

              {/* Cache-disabled example */}
              <div className="space-y-2">
                <h4 className="font-medium">Without Cache (for comparison)</h4>
                <div className="relative aspect-video border rounded-lg overflow-hidden">
                  <SecureImage
                    src={selectedImage}
                    alt="Non-cached image"
                    fill
                    className="object-cover"
                    enableCache={false} // Disable caching
                  />
                </div>
                <p className="text-sm text-muted-foreground">
                  This image loads fresh from server every time
                </p>
              </div>
            </div>

            {/* Cache Management */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Cache Management</h3>
              
              {/* Cache Statistics */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Cache Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span>Cached Images:</span>
                    <Badge variant="secondary">
                      {cacheStats.size} / {cacheStats.maxSize}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Max Age:</span>
                    <Badge variant="outline">
                      {Math.round(cacheStats.maxAge / 1000 / 60)} min
                    </Badge>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={refreshStats}
                    className="w-full"
                  >
                    Refresh Stats
                  </Button>
                </CardContent>
              </Card>

              {/* Cache Actions */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">Cache Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreloadImages}
                    disabled={isPreloading}
                    className="w-full"
                  >
                    {isPreloading ? 'Preloading...' : 'Preload All Images'}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleInvalidateImage}
                    className="w-full"
                  >
                    Invalidate Current Image
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearTenant}
                    className="w-full"
                  >
                    Clear Tenant Cache
                  </Button>
                  
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleClearAll}
                    className="w-full"
                  >
                    Clear All Cache
                  </Button>
                </CardContent>
              </Card>

              {/* Debug Information (Development only) */}
              {debug && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base">Debug Tools</CardTitle>
                    <CardDescription>
                      Available in development mode only
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={debug.debug}
                      className="w-full"
                    >
                      Log Cache Debug Info
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How It Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">1. First Load</h4>
              <p className="text-sm text-muted-foreground">
                Image is fetched from server and stored in cache as blob URL
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">2. Subsequent Loads</h4>
              <p className="text-sm text-muted-foreground">
                Image loads instantly from cache without server request
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">3. Automatic Cleanup</h4>
              <p className="text-sm text-muted-foreground">
                Cache expires after 30 minutes and cleans up memory automatically
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
