import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Search, Package2 } from "lucide-react";

export default function ItemNotFound() {
  return (
    <div className="container mx-auto py-6">
      <Button variant="ghost" className="mb-6" asChild>
        <Link href="/items">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Items
        </Link>
      </Button>

      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
              <Package2 className="h-8 w-8 text-muted-foreground" />
            </div>
            <CardTitle>Item Not Found</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              The item you&apos;re looking for doesn&apos;t exist or may have been deleted.
            </p>
            <div className="flex justify-center space-x-2">
              <Link href="/items">
                <Button variant="outline" className="flex items-center space-x-2">
                  <Search className="h-4 w-4" />
                  <span>Browse Items</span>
                </Button>
              </Link>
              <Link href="/items/new">
                <Button className="flex items-center space-x-2">
                  <Package2 className="h-4 w-4" />
                  <span>Add New Item</span>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
