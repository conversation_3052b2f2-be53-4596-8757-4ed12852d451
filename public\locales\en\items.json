{"items": {"title": "Items", "new": "New Item", "edit": "<PERSON>em", "addItem": "Add Item", "details": {"title": "<PERSON><PERSON>", "name": "Item Name", "price": "Price", "stock": "Stock", "category": "Category", "description": "Description", "image": "Item Image", "information": "Item Information"}, "actions": {"createItem": "Create <PERSON><PERSON>", "creating": "Creating...", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "sellItem": "<PERSON>ll", "viewDetails": "View Details", "changeImage": "Change Image", "openMenu": "Open menu", "addNew": "Add a new item"}, "messages": {"createSuccess": "Item created successfully", "createError": "Failed to create item", "updateSuccess": "Item updated successfully", "updateError": "Failed to update item", "deleteSuccess": "Item deleted successfully", "deleteError": "Failed to delete item", "deleteConfirm": "Are you sure you want to delete this item?", "hasBeenDeleted": "has been deleted"}, "errors": {"requiredFields": "Please fill in all required fields", "pageError": "Something went wrong", "pageErrorDescription": "We encountered an error while loading the item sale page. This could be due to a network issue or the item might not be available.", "errorDetails": "<PERSON><PERSON><PERSON> (Development)", "tryAgain": "Try again", "backToItems": "Back to Items", "pageNotFound": "Page Not Found", "pageNotFoundDescription": "The page you're looking for doesn't exist or has been moved."}, "placeholders": {"enterName": "Enter item name", "enterDescription": "Enter item description", "enterPrice": "0.00", "name": "Enter item name", "description": "Enter item description", "category": "Select category", "enterStock": "Enter stock quantity", "uploadImage": "Click to upload or drag and drop", "imageFormats": "PNG, JPG or WEBP (MAX. 5MB)", "chooseAthlete": "Choose an athlete"}, "categories": {"equipment": "Equipment", "clothing": "Clothing", "accessories": "Accessories", "other": "Other"}, "status": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "sell": {"title": "<PERSON><PERSON>", "itemDetails": "<PERSON><PERSON>", "saleInformation": "Sale Information", "selectAthlete": "Select Athlete", "quantity": "Quantity", "unitPrice": "Unit Price", "total": "Total", "processing": "Processing...", "completeSale": "Complete Sale", "availableStock": "Available Stock", "paymentStatus": "Payment Status", "selectPaymentStatus": "Select payment status", "billingDate": "Billing Date", "dueDate": "Due Date", "selectBillingDate": "Select billing date", "selectDueDate": "Select due date", "errors": {"invalidData": "Please fill in all required fields", "saleError": "Error processing sale"}, "messages": {"saleCompleted": "Sale completed successfully"}}, "paymentStatus": {"pending": "Pending", "completed": "Completed", "overdue": "Overdue"}}}