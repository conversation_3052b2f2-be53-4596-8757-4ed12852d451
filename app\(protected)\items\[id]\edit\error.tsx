"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, RefreshCw, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const { t } = useSafeTranslation();

  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Edit item page error:", error);
  }, [error]);

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Link href="/items">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold tracking-tight">{t('items.edit')}</h1>
          </div>
        </div>

        {/* Error card */}
        <Card className="border-destructive/50">
          <CardHeader className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
              <AlertCircle className="h-6 w-6 text-destructive" />
            </div>
            <CardTitle className="text-destructive">
              {t('common.errors.loadingFailed')}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              {t('items.errors.loadItemFailed')}
            </p>
            {error.message && (
              <p className="text-sm text-muted-foreground font-mono bg-muted p-2 rounded">
                {error.message}
              </p>
            )}
            <div className="flex justify-center space-x-2">
              <Button
                onClick={reset}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>{t('common.actions.retry')}</span>
              </Button>
              <Link href="/items">
                <Button>
                  {t('common.actions.backToItems')}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
