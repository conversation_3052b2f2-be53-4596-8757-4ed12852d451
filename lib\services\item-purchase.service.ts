import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { updateAthleteBalance } from '../balance-calculator';

export interface CreateItemPurchaseData {
  itemId: string;
  athleteId: string;
  quantity: number;
  totalPrice: string;
  purchaseDate: string;
  status?: 'pending' | 'completed' | 'cancelled';
}

export interface CreateItemSaleData {
  itemId: string;
  athleteId: string;
  quantity: number;
  paymentStatus?: 'pending' | 'completed' | 'overdue';
}

export class ItemPurchaseService extends BaseService {
  constructor() {
    super('ItemPurchaseService');
  }

  /**
   * Get all item purchases
   */
  async getItemPurchases(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getItemPurchases',
      async () => {
        return TenantAwareDB.getItemPurchases();
      },
      {
        userId,
        tenantId,
        resource: 'itemPurchases',
      }
    );
  }

  /**
   * Create a new item purchase
   */
  async createItemPurchase(
    data: CreateItemPurchaseData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateItemPurchaseData): ValidationError | null => {
        if (!data.itemId || data.itemId.trim().length === 0) {
          return { field: 'itemId', message: 'Item is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: CreateItemPurchaseData): ValidationError | null => {
        if (!data.athleteId || data.athleteId.trim().length === 0) {
          return { field: 'athleteId', message: 'Athlete is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: CreateItemPurchaseData): ValidationError | null => {
        if (!data.quantity || data.quantity <= 0) {
          return { field: 'quantity', message: 'Quantity must be greater than 0', code: 'INVALID_VALUE' };
        }
        if (data.quantity > 100) {
          return { field: 'quantity', message: 'Quantity cannot exceed 100', code: 'INVALID_RANGE' };
        }
        return null;
      },
      (data: CreateItemPurchaseData): ValidationError | null => {
        if (!data.totalPrice || data.totalPrice.trim().length === 0) {
          return { field: 'totalPrice', message: 'Total price is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const price = parseFloat(data.totalPrice);
        if (isNaN(price) || price < 0) {
          return { field: 'totalPrice', message: 'Total price must be a valid positive number', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (data: CreateItemPurchaseData): ValidationError | null => {
        if (!data.purchaseDate || data.purchaseDate.trim().length === 0) {
          return { field: 'purchaseDate', message: 'Purchase date is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createItemPurchase',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        const effectiveUserId = userId || await getServerUserId();

        // Check if item exists
        const item = await TenantAwareDB.getItemById(validatedData.itemId, effectiveTenantId || undefined);
        if (!item) {
          throw new NotFoundError('Item', validatedData.itemId);
        }

        // Check if athlete exists
        const athlete = await TenantAwareDB.getAthleteById(validatedData.athleteId, effectiveTenantId || undefined);
        if (!athlete) {
          throw new NotFoundError('Athlete', validatedData.athleteId);
        }

        // Check stock availability
        if (item.stock < validatedData.quantity) {
          throw new BusinessRuleError(
            'insufficient_stock',
            `Insufficient stock. Available: ${item.stock}, Requested: ${validatedData.quantity}`,
            undefined,
            `Only ${item.stock} items available in stock.`
          );
        }

        // Create the purchase record
        const purchase = await TenantAwareDB.createItemPurchase({
          itemId: validatedData.itemId,
          athleteId: validatedData.athleteId,
          quantity: validatedData.quantity,
          totalPrice: validatedData.totalPrice,
          purchaseDate: validatedData.purchaseDate,
          status: validatedData.status || 'pending'
        }, effectiveTenantId || undefined, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);

        // Update item stock (decrease by quantity)
        await TenantAwareDB.updateItemStock(
          validatedData.itemId, 
          -validatedData.quantity, 
          effectiveTenantId || undefined, 
          effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined
        );

        // If this is a pending purchase, create a payment record and update balance
        if (validatedData.status === 'pending' || !validatedData.status) {
          // Create payment record for pending purchase
          await TenantAwareDB.createPayment({
            athleteId: validatedData.athleteId,
            itemPurchaseId: purchase.id,
            amount: validatedData.totalPrice,
            type: 'equipment',
            description: `Purchase: ${validatedData.quantity}x ${item.name}`,
            status: 'pending',
            date: validatedData.purchaseDate,
            dueDate: validatedData.purchaseDate // Due immediately for equipment purchases
          }, effectiveTenantId || undefined, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);

          // Update athlete balance
          if (effectiveTenantId) {
            await updateAthleteBalance(validatedData.athleteId, effectiveTenantId, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);
          }
        }

        return purchase;
      },
      {
        userId,
        tenantId,
        resource: 'itemPurchase',
        metadata: { operation: 'create' },
      }
    );
  }

  /**
   * Create an item sale with automatic pricing and payment handling
   */
  async createItemSale(
    data: CreateItemSaleData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateItemSaleData): ValidationError | null => {
        if (!data.itemId || data.itemId.trim().length === 0) {
          return { field: 'itemId', message: 'Item is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: CreateItemSaleData): ValidationError | null => {
        if (!data.athleteId || data.athleteId.trim().length === 0) {
          return { field: 'athleteId', message: 'Athlete is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: CreateItemSaleData): ValidationError | null => {
        if (!data.quantity || data.quantity <= 0) {
          return { field: 'quantity', message: 'Quantity must be greater than 0', code: 'INVALID_VALUE' };
        }
        if (data.quantity > 100) {
          return { field: 'quantity', message: 'Quantity cannot exceed 100', code: 'INVALID_RANGE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createItemSale',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        const effectiveUserId = userId || await getServerUserId();

        // Check if item exists
        const item = await TenantAwareDB.getItemById(validatedData.itemId, effectiveTenantId || undefined);
        if (!item) {
          throw new NotFoundError('Item', validatedData.itemId);
        }

        // Check if athlete exists
        const athlete = await TenantAwareDB.getAthleteById(validatedData.athleteId, effectiveTenantId || undefined);
        if (!athlete) {
          throw new NotFoundError('Athlete', validatedData.athleteId);
        }

        // Check stock availability
        if (item.stock < validatedData.quantity) {
          throw new BusinessRuleError(
            'insufficient_stock',
            `Insufficient stock. Available: ${item.stock}, Requested: ${validatedData.quantity}`,
            undefined,
            `Only ${item.stock} items available in stock.`
          );
        }

        const totalPrice = (parseFloat(item.price) * validatedData.quantity).toFixed(2);
        const purchaseDate = new Date().toISOString().split('T')[0];

        // Create the purchase record
        const purchase = await TenantAwareDB.createItemPurchase({
          itemId: validatedData.itemId,
          athleteId: validatedData.athleteId,
          quantity: validatedData.quantity,
          totalPrice: totalPrice,
          purchaseDate: purchaseDate,
          status: 'completed'
        }, effectiveTenantId || undefined, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);

        // Create payment record
        const payment = await TenantAwareDB.createPayment({
          athleteId: validatedData.athleteId,
          itemPurchaseId: purchase.id,
          amount: totalPrice,
          type: 'equipment',
          description: `Purchase: ${validatedData.quantity}x ${item.name}`,
          status: validatedData.paymentStatus || 'pending',
          date: purchaseDate,
          dueDate: purchaseDate // Due immediately for equipment purchases
        }, effectiveTenantId || undefined, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);

        // Update item stock (decrease by quantity)
        await TenantAwareDB.updateItemStock(
          validatedData.itemId, 
          -validatedData.quantity, 
          effectiveTenantId || undefined, 
          effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined
        );

        // Update athlete balance if payment is not completed
        if (validatedData.paymentStatus && validatedData.paymentStatus !== 'completed' && effectiveTenantId) {
          await updateAthleteBalance(validatedData.athleteId, effectiveTenantId, effectiveUserId ? BigInt(effectiveUserId.toString()) : undefined);
        }

        return { purchase, payment };
      },
      {
        userId,
        tenantId,
        resource: 'itemSale',
        metadata: { operation: 'create' },
      }
    );
  }
}

// Factory function
export const itemPurchaseService = () => new ItemPurchaseService();
