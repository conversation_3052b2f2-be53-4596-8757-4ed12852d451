"use client";

import React, { useState, useEffect } from "react";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, FileText, CreditCard } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { createPaymentColumns } from "@/components/payments-table-columns";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { format } from "date-fns";
import { Payment, PaymentPlan } from "@/lib/types";

interface PaymentsListClientProps {
  payments: Payment[];
  plans: PaymentPlan[];
}

export default function PaymentsListClient({ payments, plans }: PaymentsListClientProps) {
  const { t } = useSafeTranslation();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("payments");
  
  const paymentColumns = createPaymentColumns(t);

  // Handle URL parameters for tab selection
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['payments', 'plans'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Update URL when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    router.replace(url.pathname + url.search, { scroll: false });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('payments.title')}</h1>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/payments/new">
              <CreditCard className="mr-2 h-4 w-4" />
              {t('payments.recordPayment')}
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/payments/plans/new">
              <FileText className="mr-2 h-4 w-4" />
              {t('payments.newPaymentPlan')}
            </Link>
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="payments" onValueChange={handleTabChange} value={activeTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="payments">{t('payments.tabPayments')}</TabsTrigger>
          <TabsTrigger value="plans">{t('payments.tabPlans')}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="payments">
          <DataTable
            columns={paymentColumns}
            data={payments}
            searchKey="description"
            searchPlaceholder={t('payments.placeholders.searchPayments')}
          />
        </TabsContent>
        
        <TabsContent value="plans">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              return (
                <Card key={plan.id}>
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>
                      {t('payments.plans.monthlyValue')}: {plan.monthlyValue} {t('common.currency')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Monthly Value */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.monthlyValue')}:</span>
                        <span className="font-medium">{plan.monthlyValue} {t('common.currency')}</span>
                      </div>
                      
                      {/* Payment Schedule */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.assignDay')}:</span>
                        <span className="font-medium">{plan.assignDay}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.dueDay')}:</span>
                        <span className="font-medium">{plan.dueDay}</span>
                      </div>
                      
                      {/* Status */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.statusLabel')}:</span>
                        <span className="capitalize">{t(`payments.planEdit.status.${plan.status}`, plan.status)}</span>
                      </div>
                      
                      {/* Description */}
                      {plan.description && (
                        <div className="mt-2">
                          <span className="text-sm text-muted-foreground">{plan.description}</span>
                        </div>
                      )}
                      
                      {/* Branches */}
                      {plan.branches && plan.branches.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4">
                          {plan.branches.map((branch) => (
                            <span
                              key={branch.id}
                              className="bg-secondary text-secondary-foreground text-xs rounded-full px-2 py-1"
                            >
                              {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}`}>{t('payments.actions.view')}</Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}/edit`}>{t('payments.actions.edit')}</Link>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
            
            <Link href="/payments/plans/new" className="block">
              <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
                    <PlusCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    {t('payments.createPlan')}
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
