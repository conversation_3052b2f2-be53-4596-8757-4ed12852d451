import { notFound } from "next/navigation";
import { getAthleteById } from "@/lib/actions";
import { getAthleteTeams } from "@/lib/actions/athlete-teams";
import { EditAthleteClient } from "@/components/athletes/edit/EditAthleteClient";

interface EditAthletePageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditAthletePage({ params }: EditAthletePageProps) {
  const { id } = await params;
  
  try {
    const [athlete, athleteTeams] = await Promise.all([
      getAthleteById(id),
      getAthleteTeams(id),
    ]);

    if (!athlete) {
      notFound();
    }

    return (
      <EditAthleteClient 
        athlete={athlete}
        athleteTeams={athleteTeams}
      />
    );
  } catch (error) {
    console.error("Error loading athlete data:", error);
    notFound();
  }
}