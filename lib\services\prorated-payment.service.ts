/**
 * Service for handling prorated payment calculations and operations
 * Supports athlete creation, activation, and deactivation scenarios
 */

import { calculateProratedAmount, getRemainingDaysInMonth } from '../proration-utils';
import { getPaymentTranslation } from '../server-i18n';

export interface PaymentPlanInfo {
  id: string;
  name: string;
  monthlyValue: string;
}

export interface PaymentPlanAssignment {
  id: string;
  planId: string;
  planName?: string;
  monthlyValue?: string;
}

export interface ProratedCalculationResult {
  totalAmount: number;
  payments: Array<{
    assignmentId: string;
    planId: string;
    planName: string;
    monthlyAmount: number;
    proratedAmount: number;
    description: string;
  }>;
  remainingDays: number;
  totalDaysInMonth: number;
}

export interface ProratedPaymentData {
  athleteId: string;
  athletePaymentPlanId: string | null;
  amount: string;
  date: string;
  dueDate: string;
  status: 'pending' | 'completed' | 'overdue' | 'cancelled';
  type: 'fee' | 'equipment' | 'other';
  method?: string | null;
  description: string;
}

export type ProratedScenario = 'creation' | 'activation' | 'deactivation';

export class ProratedPaymentService {
  /**
   * Calculate prorated amounts for payment plan assignments
   */
  static calculateProratedAmounts(
    assignments: PaymentPlanAssignment[],
    paymentPlans: PaymentPlanInfo[],
    scenario: ProratedScenario,
    locale: string = 'en',
    calculationDate: Date = new Date()
  ): ProratedCalculationResult {
    const remainingDays = getRemainingDaysInMonth(calculationDate);
    const totalDaysInMonth = new Date(
      calculationDate.getFullYear(),
      calculationDate.getMonth() + 1,
      0
    ).getDate();

    let totalAmount = 0;
    const payments: ProratedCalculationResult['payments'] = [];

    for (const assignment of assignments) {
      const plan = paymentPlans.find(p => p.id === assignment.planId);
      if (!plan) continue;

      const monthlyAmount = parseFloat(plan.monthlyValue);
      let proratedAmount: number;
      let description: string;

      switch (scenario) {
        case 'creation':
        case 'activation':
          // Calculate prorated charge for remaining days
          proratedAmount = calculateProratedAmount(monthlyAmount, calculationDate);
          description = getPaymentTranslation(
            'descriptions.proratedBalance',
            locale,
            { planName: plan.name }
          );
          break;

        case 'deactivation':
          // Calculate prorated refund for remaining days
          proratedAmount = -calculateProratedAmount(monthlyAmount, calculationDate);
          description = getPaymentTranslation(
            'descriptions.proratedRefund',
            locale,
            { planName: plan.name }
          );
          break;

        default:
          throw new Error(`Unsupported scenario: ${scenario}`);
      }

      if (Math.abs(proratedAmount) > 0.01) { // Only include if amount is significant
        payments.push({
          assignmentId: assignment.id,
          planId: assignment.planId,
          planName: plan.name,
          monthlyAmount,
          proratedAmount,
          description
        });

        totalAmount += proratedAmount;
      }
    }

    return {
      totalAmount: Math.round(totalAmount * 100) / 100,
      payments,
      remainingDays,
      totalDaysInMonth
    };
  }

  /**
   * Generate payment data objects for creating payments
   */
  static generatePaymentData(
    athleteId: string,
    calculation: ProratedCalculationResult,
    scenario: ProratedScenario,
    calculationDate: Date = new Date()
  ): ProratedPaymentData[] {
    const dateStr = calculationDate.toISOString().split('T')[0];
    const paymentData: ProratedPaymentData[] = [];

    for (const payment of calculation.payments) {
      // Skip payments with zero or negligible amounts
      if (Math.abs(payment.proratedAmount) < 0.01) continue;

      const status: ProratedPaymentData['status'] = 
        scenario === 'deactivation' ? 'completed' : 'pending';

      paymentData.push({
        athleteId,
        athletePaymentPlanId: payment.assignmentId,
        amount: Math.abs(payment.proratedAmount).toFixed(2),
        date: dateStr,
        dueDate: dateStr,
        status,
        type: 'fee',
        method: null,
        description: payment.description
      });
    }

    return paymentData;
  }

  /**
   * Create prorated payments for athlete activation
   */
  static async createActivationPayments(
    athleteId: string,
    assignments: PaymentPlanAssignment[],
    paymentPlans: PaymentPlanInfo[],
    locale: string = 'en',
    activationDate: Date = new Date()
  ): Promise<{ success: boolean; totalAmount: number; paymentsCreated: number }> {
    try {
      const { createPayment } = await import('../actions/payments');
      
      const calculation = this.calculateProratedAmounts(
        assignments,
        paymentPlans,
        'activation',
        locale,
        activationDate
      );

      if (calculation.totalAmount <= 0) {
        return { success: true, totalAmount: 0, paymentsCreated: 0 };
      }

      const paymentData = this.generatePaymentData(
        athleteId,
        calculation,
        'activation',
        activationDate
      );

      let paymentsCreated = 0;
      for (const payment of paymentData) {
        await createPayment(payment);
        paymentsCreated++;
      }

      return {
        success: true,
        totalAmount: calculation.totalAmount,
        paymentsCreated
      };
    } catch (error) {
      console.error('Error creating activation payments:', error);
      return { success: false, totalAmount: 0, paymentsCreated: 0 };
    }
  }

  /**
   * Create prorated refund payments for athlete deactivation
   */
  static async createDeactivationRefunds(
    athleteId: string,
    assignments: PaymentPlanAssignment[],
    paymentPlans: PaymentPlanInfo[],
    locale: string = 'en',
    deactivationDate: Date = new Date()
  ): Promise<{ success: boolean; totalRefund: number; refundsCreated: number }> {
    try {
      const { createPayment } = await import('../actions/payments');
      
      const calculation = this.calculateProratedAmounts(
        assignments,
        paymentPlans,
        'deactivation',
        locale,
        deactivationDate
      );

      if (calculation.totalAmount >= 0) {
        return { success: true, totalRefund: 0, refundsCreated: 0 };
      }

      const paymentData = this.generatePaymentData(
        athleteId,
        calculation,
        'deactivation',
        deactivationDate
      );

      let refundsCreated = 0;
      for (const payment of paymentData) {
        await createPayment(payment);
        refundsCreated++;
      }

      return {
        success: true,
        totalRefund: Math.abs(calculation.totalAmount),
        refundsCreated
      };
    } catch (error) {
      console.error('Error creating deactivation refunds:', error);
      return { success: false, totalRefund: 0, refundsCreated: 0 };
    }
  }

  /**
   * Get prorated calculation preview without creating payments
   */
  static getCalculationPreview(
    assignments: PaymentPlanAssignment[],
    paymentPlans: PaymentPlanInfo[],
    scenario: ProratedScenario,
    locale: string = 'en',
    calculationDate: Date = new Date()
  ): ProratedCalculationResult {
    return this.calculateProratedAmounts(
      assignments,
      paymentPlans,
      scenario,
      locale,
      calculationDate
    );
  }

  /**
   * Get a preview of prorated calculations without creating payments
   * Used for UI display before user confirmation
   */
  static getCalculationPreview(
    assignments: PaymentPlanAssignment[],
    paymentPlans: PaymentPlanInfo[],
    scenario: ProratedScenario,
    locale: string = 'en',
    calculationDate: Date = new Date()
  ): {
    totalAmount: number;
    payments: Array<{
      planName: string;
      monthlyAmount: number;
      proratedAmount: number;
      description: string;
    }>;
    remainingDays: number;
    totalDaysInMonth: number;
  } {
    const calculation = this.calculateProratedAmounts(
      assignments,
      paymentPlans,
      scenario,
      locale,
      calculationDate
    );

    return {
      totalAmount: calculation.totalAmount,
      payments: calculation.payments.map(payment => ({
        planName: payment.planName,
        monthlyAmount: payment.monthlyAmount,
        proratedAmount: payment.proratedAmount,
        description: payment.description
      })),
      remainingDays: calculation.remainingDays,
      totalDaysInMonth: calculation.totalDaysInMonth
    };
  }
}

export default ProratedPaymentService;
