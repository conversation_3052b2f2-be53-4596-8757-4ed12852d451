import { notFound } from "next/navigation";
import { Metadata } from "next";
import { getAthletes, getItemById } from "@/lib/db/actions";
import SellItemClient from "./sell-item-client";

interface SellItemPageProps {
  params: Promise<{
    id: string;
  }>;
}

// Static params for better performance (if applicable)
export const dynamicParams = true;

// Revalidate every 5 minutes to ensure fresh data
export const revalidate = 300;

export async function generateMetadata({ params }: SellItemPageProps): Promise<Metadata> {
  try {
    const { id } = await params;
    const item = await getItemById(id);
    if (!item) {
      return {
        title: "Item Not Found",
        description: "The requested item could not be found.",
      };
    }
    
    return {
      title: `Sell ${item.name}`,
      description: `Sell ${item.name} to an athlete. Price: $${item.price}, Stock: ${item.stock}`,
      keywords: ["sell", "item", "sports equipment", item.category, item.name],
    };
  } catch {
    return {
      title: "Sell Item",
      description: "Sell sports equipment to athletes.",
    };
  }
}

export default async function SellItemPage({ params }: SellItemPageProps) {
  const { id } = await params;
  
  // Validate the item ID parameter
  if (!id || typeof id !== 'string') {
    notFound();
  }

  try {
    // Fetch data in parallel for better performance
    const [item, athletes] = await Promise.all([
      getItemById(id),
      getAthletes()
    ]);

    // Check if item exists
    if (!item) {
      notFound();
    }

    // Validate item has required properties
    if (!item.name || !item.price || item.stock === undefined) {
      console.error("Invalid item data:", item);
      notFound();
    }

    // Check if item has sufficient stock
    if (item.stock <= 0) {
      notFound();
    }

    // Validate athletes data
    if (!athletes || !Array.isArray(athletes)) {
      console.error("Invalid athletes data:", athletes);
      throw new Error("Failed to load athletes data");
    }

    return (
      <div className="container mx-auto py-6">
        <SellItemClient 
          item={item} 
          athletes={athletes} 
        />
      </div>
    );
  } catch (error) {
    console.error("Error loading item sale data:", error);
    notFound();
  }
}