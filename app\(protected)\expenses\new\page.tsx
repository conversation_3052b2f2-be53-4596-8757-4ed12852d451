import { getInstructors } from "@/lib/db/actions/instructors";
import { getFacilities } from "@/lib/db/actions/facilities";
import NewExpenseClient from "./new-expense-client";

export default async function NewExpensePage() {
  const [instructors, facilities] = await Promise.all([
    getInstructors(),
    getFacilities(),
  ]);
  
  return <NewExpenseClient instructors={instructors} facilities={facilities} />;
}
