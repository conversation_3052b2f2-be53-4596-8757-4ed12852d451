'use client';

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserX } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { Athlete } from "@/lib/types";
import { activateAthlete, deactivateAthlete, getAthleteActivationData } from "@/lib/db/actions/athletes";
import { getTeams } from "@/lib/db/actions/teams";
import { getPaymentPlans } from "@/lib/db/actions/payment-plans";

interface AthleteActivationDialogProps {
  athlete: Athlete;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

interface ActivationData {
  athlete: {
    id: string;
    name: string;
    surname: string;
    status: 'active' | 'inactive' | 'suspended';
  };
  teams: Array<{
    teamId: string;
    teamName: string;
    branchName: string;
    schoolName: string;
    instructorName: string;
    instructorSurname: string;
  }>;
  paymentPlans: Array<{
    id: string;
    planId: string;
    teamId: string;
    assignedDate: string;
    isActive: boolean;
    lastPaymentDate: string | null;
    planName: string;
    monthlyValue: string;
    assignDay: number;
    dueDay: number;
    planStatus: string;
    planDescription: string | null;
    teamName: string | null;
    athleteName: string;
    athleteSurname: string;
  }>;
}

interface Team {
  id: string;
  name: string;
  branch?: {
    name: string;
  };
  school?: {
    name: string;
  };
}

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
}

export const AthleteActivationDialog = ({ athlete, isOpen, onOpenChange, onSuccess }: AthleteActivationDialogProps) => {
  const { t } = useSafeTranslation();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activationData, setActivationData] = useState<ActivationData | null>(null);
  const [availableTeams, setAvailableTeams] = useState<Team[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [teamPaymentPlans, setTeamPaymentPlans] = useState<Record<string, string>>({});

  const isDeactivating = athlete.status === 'active';

  // Debug logging
  useEffect(() => {
  }, [isOpen, athlete.id, athlete.status]);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get activation data (current teams and payment plans)
      const activationResult = await getAthleteActivationData(athlete.id);
      
      if (activationResult.success && activationResult.data) {
        setActivationData(activationResult.data);
      } else {
        // Set empty data instead of closing dialog
        setActivationData({
          athlete: {
            id: athlete.id,
            name: athlete.name,
            surname: athlete.surname,
            status: athlete.status,
          },
          teams: [],
          paymentPlans: [],
        });
      }

      // If activating, also load available teams and payment plans
      if (!isDeactivating) {
        try {
          const [teamsResult, paymentPlansResult] = await Promise.all([
            getTeams(),
            getPaymentPlans(),
          ]);
          
          setAvailableTeams(teamsResult || []);
          setAvailablePaymentPlans(paymentPlansResult || []);
        } catch (error) {
          setAvailableTeams([]);
          setAvailablePaymentPlans([]);
        }
      }
    } catch (error) {
      toast.error(t('athletes.messages.loadDataError', 'Failed to load data'));
      // Don't close dialog on error, show empty state
      setActivationData({
        athlete: {
          id: athlete.id,
          name: athlete.name,
          surname: athlete.surname,
          status: athlete.status,
        },
        teams: [],
        paymentPlans: [],
      });
    } finally {
      setIsLoading(false);
    }
  }, [athlete.id, athlete.name, athlete.surname, athlete.status, isDeactivating, t]);

  useEffect(() => {
    if (isOpen) {
      // Reset state when dialog opens
      setSelectedTeams([]);
      setTeamPaymentPlans({});
      setActivationData(null);
      setAvailableTeams([]);
      setAvailablePaymentPlans([]);
      loadData();
    }
  }, [isOpen, athlete.id, loadData]);

  const handleTeamSelection = (teamId: string, checked: boolean) => {
    if (checked) {
      setSelectedTeams(prev => [...prev, teamId]);
    } else {
      setSelectedTeams(prev => prev.filter(id => id !== teamId));
      // Remove payment plan selection for this team
      setTeamPaymentPlans(prev => {
        const newPlans = { ...prev };
        delete newPlans[teamId];
        return newPlans;
      });
    }
  };

  const handlePaymentPlanSelection = (teamId: string, planId: string) => {
    setTeamPaymentPlans(prev => ({
      ...prev,
      [teamId]: planId,
    }));
  };

  const handleConfirm = async () => {
    try {
      setIsLoading(true);

      if (isDeactivating) {
        // Deactivate athlete
        const result = await deactivateAthlete(athlete.id);
        
        if (result.success) {
          toast.success(t('athletes.messages.deactivateSuccess', 'Athlete deactivated successfully'));
          onSuccess?.();
          onOpenChange(false);
          router.refresh();
        } else {
          toast.error(t('athletes.messages.deactivateError', 'Failed to deactivate athlete'));
        }
      } else {
        // Activate athlete
        const teamAssignments = selectedTeams.map(teamId => ({
          teamId,
          paymentPlanId: teamPaymentPlans[teamId] || undefined,
        }));

        const result = await activateAthlete(athlete.id, teamAssignments);
        
        if (result.success) {
          toast.success(t('athletes.messages.activateSuccess', 'Athlete activated successfully'));
          onSuccess?.();
          onOpenChange(false);
          router.refresh();
        } else {
          toast.error(t('athletes.messages.activateError', 'Failed to activate athlete'));
        }
      }
    } catch (error) {
      toast.error(t('athletes.messages.operationError', 'Operation failed'));
    } finally {
      setIsLoading(false);
    }
  };

  const getTeamName = (team: Team) => {
    const branchName = team.branch?.name || '';
    const schoolName = team.school?.name || '';
    return `${team.name} ${branchName ? `(${branchName})` : ''} ${schoolName ? `- ${schoolName}` : ''}`.trim();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isDeactivating 
              ? t('athletes.actions.deactivateAthlete', 'Deactivate Athlete')
              : t('athletes.actions.activateAthlete', 'Activate Athlete')
            }
          </DialogTitle>
          <DialogDescription>
            {isDeactivating 
              ? t('athletes.deactivateDialog.description', 
                  'When you deactivate this athlete, they will be removed from all teams and payment plans will be deactivated.')
              : t('athletes.activateDialog.description', 
                  'Select teams and payment plans to assign to this athlete.')
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-sm font-medium">
            {t('athletes.details.name')}: {athlete.name} {athlete.surname}
          </div>

          {isLoading && (
            <div className="text-center py-4">
              <div className="text-sm text-muted-foreground">
                {t('common.loading', 'Loading...')}
              </div>
            </div>
          )}

          {!isLoading && activationData && (
            <div className="space-y-4">
              {isDeactivating ? (
                // Deactivation view - show current teams and payment plans
                <div className="space-y-4">
                  {activationData.teams.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-destructive">
                        {t('athletes.deactivateDialog.teamsToRemove', 'Teams to remove from:')}
                      </Label>
                      <div className="mt-2 space-y-2">
                        {activationData.teams.map((team) => (
                          <div key={team.teamId} className="flex items-center space-x-2 p-2 bg-destructive/10 rounded">
                            <UserX className="h-4 w-4 text-destructive" />
                            <span className="text-sm">
                              {team.teamName} ({team.branchName}) - {team.schoolName}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {activationData.paymentPlans.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-destructive">
                        {t('athletes.deactivateDialog.paymentPlansToRemove', 'Payment plans to deactivate:')}
                      </Label>
                      <div className="mt-2 space-y-2">
                        {activationData.paymentPlans.map((plan) => (
                          <div key={plan.id} className="flex items-center space-x-2 p-2 bg-destructive/10 rounded">
                            <UserX className="h-4 w-4 text-destructive" />
                            <span className="text-sm">
                              {plan.planName} - {plan.monthlyValue} {t('common.currency')} 
                              {plan.teamName && ` (${plan.teamName})`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {activationData.teams.length === 0 && activationData.paymentPlans.length === 0 && (
                    <div className="text-sm text-muted-foreground">
                      {t('athletes.deactivateDialog.noActiveAssignments', 'No active team or payment plan assignments.')}
                    </div>
                  )}
                </div>
              ) : (
                // Activation view - show available teams and payment plans
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">
                      {t('athletes.activateDialog.selectTeams', 'Select teams to assign:')}
                    </Label>
                    <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                      {availableTeams.map((team) => (
                        <div key={team.id} className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`team-${team.id}`}
                              checked={selectedTeams.includes(team.id)}
                              onCheckedChange={(checked) => 
                                handleTeamSelection(team.id, checked as boolean)
                              }
                            />
                            <Label htmlFor={`team-${team.id}`} className="text-sm">
                              {getTeamName(team)}
                            </Label>
                          </div>
                          
                          {selectedTeams.includes(team.id) && (
                            <div className="ml-6 space-y-1">
                              <Label className="text-xs text-muted-foreground">
                                {t('athletes.activateDialog.selectPaymentPlan', 'Select payment plan (optional):')}
                              </Label>
                              <Select
                                value={teamPaymentPlans[team.id] || ""}
                                onValueChange={(value) => handlePaymentPlanSelection(team.id, value)}
                              >
                                <SelectTrigger className="h-8 text-sm">
                                  <SelectValue placeholder={t('athletes.activateDialog.noPaymentPlan', 'No payment plan')} />
                                </SelectTrigger>
                                <SelectContent>
                                  {availablePaymentPlans.map((plan) => (
                                    <SelectItem key={plan.id} value={plan.id}>
                                      {plan.name} - {plan.monthlyValue} {t('common.currency')}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    {availableTeams.length === 0 && (
                      <div className="text-sm text-muted-foreground mt-2">
                        {t('athletes.activateDialog.noAvailableTeams', 'No available teams.')}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {t('common.actions.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading || (!isDeactivating && selectedTeams.length === 0)}
            variant={isDeactivating ? "destructive" : "default"}
          >
            {isLoading ? (
              t('common.actions.processing', 'Processing...')
            ) : isDeactivating ? (
              t('athletes.actions.deactivate', 'Deactivate')
            ) : (
              t('athletes.actions.activate', 'Activate')
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
