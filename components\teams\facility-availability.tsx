"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, AlertTriangle } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { getAllFacilitySchedules } from "@/lib/db/actions";
import { 
  FacilitySchedule, 
  DaySchedule, 
  TimeSlot,
  getFacilityWeeklyAvailability,
  checkScheduleConflict,
  dayNames 
} from "@/lib/schedule-utils";

interface FacilityAvailabilityProps {
  facilityId: string;
  facilityName: string;
  selectedDay?: number;
  selectedStartTime?: string;
  selectedEndTime?: string;
  excludeTeamId?: string;
  currentTeamSchedules?: Array<{
    dayOfWeek: number;
    startTime: string;
    endTime: string;
    facilityId: string;
  }>;
}

export function FacilityAvailability({
  facilityId,
  facilityName,
  selectedDay,
  selectedStartTime,
  selectedEndTime,
  excludeTeamId,
  currentTeamSchedules = []
}: FacilityAvailabilityProps) {
  const { t } = useSafeTranslation();
  const [schedules, setSchedules] = useState<FacilitySchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [weeklyAvailability, setWeeklyAvailability] = useState<DaySchedule[]>([]);

  useEffect(() => {
    async function loadSchedules() {
      try {
        const allSchedules = await getAllFacilitySchedules();
        setSchedules(allSchedules);
        
        if (facilityId) {
          const availability = getFacilityWeeklyAvailability(allSchedules, facilityId);
          setWeeklyAvailability(availability);
        }
      } catch (error) {
        console.error('Failed to load facility schedules:', error);
      } finally {
        setLoading(false);
      }
    }

    loadSchedules();
  }, [facilityId]);

  // Check for conflicts with current selection
  const conflict = selectedDay && selectedStartTime && selectedEndTime
    ? checkScheduleConflict(
        schedules,
        facilityId,
        selectedDay,
        selectedStartTime,
        selectedEndTime,
        excludeTeamId
      )
    : { hasConflict: false };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('teams.schedule.facilityAvailability')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!facilityId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t('teams.schedule.facilityAvailability')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">
            {t('teams.schedule.selectFacilityToViewAvailability')}
          </p>
        </CardContent>
      </Card>
    );
  }

  const facilitySchedulesToday = selectedDay 
    ? [
        // Existing schedules from other teams
        ...schedules.filter(s => 
          s.facilityId === facilityId && 
          s.dayOfWeek === selectedDay && 
          s.teamId !== excludeTeamId
        ),
        // Current team's schedules being edited
        ...currentTeamSchedules
          .filter(s => s.facilityId === facilityId && s.dayOfWeek === selectedDay)
          .map(s => ({
            ...s,
            facilityId: s.facilityId,
            facilityName: facilityName,
            scheduleId: `temp-${s.dayOfWeek}-${s.startTime}`,
            teamId: excludeTeamId || 'current-team',
            teamName: 'Current Team'
          }))
      ]
    : [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {t('teams.schedule.facilityAvailability')} - {facilityName}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Conflict Warning - Only show when user selection conflicts */}
        {conflict.hasConflict && selectedDay && selectedStartTime && selectedEndTime && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-destructive" />
            <div className="text-sm">
              <p className="font-medium text-destructive">
                {t('teams.schedule.conflictDetected')}
              </p>
              <p className="text-muted-foreground">
                {t('teams.schedule.conflictWith', { team: conflict.conflictingTeam })}
              </p>
            </div>
          </div>
        )}

        {/* Selected Day Schedule - Show existing occupied schedules */}
        {selectedDay && (
          <div>
            <h4 className="font-medium mb-2 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              {t(`teams.days.${dayNames[selectedDay as keyof typeof dayNames]}`)} {t('teams.schedule.schedule')}
            </h4>
            {facilitySchedulesToday.length === 0 ? (
              <p className="text-sm text-muted-foreground">
                {t('teams.schedule.noSchedulesThisDay')}
              </p>
            ) : (
              <div className="space-y-2">
                <p className="text-xs text-muted-foreground mb-2">
                  {t('teams.schedule.existingSchedules')}:
                </p>
                {facilitySchedulesToday.map((schedule) => (
                  <div
                    key={`${schedule.scheduleId}-${schedule.teamId}`}
                    className="flex items-center justify-between p-2 bg-blue-50 border border-blue-200 rounded-lg"
                  >
                    <div>
                      <p className="font-medium text-sm text-blue-900">{schedule.teamName}</p>
                      <p className="text-xs text-blue-700">
                        {schedule.startTime} - {schedule.endTime}
                      </p>
                    </div>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {t('teams.schedule.occupied')}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Weekly Overview */}
        <div>
          <h4 className="font-medium mb-3">{t('teams.schedule.weeklyOverview')}</h4>
          <div className="grid grid-cols-7 gap-2 text-xs">
            {weeklyAvailability.map((day) => {
              const daySchedules = schedules.filter(
                s => s.facilityId === facilityId && s.dayOfWeek === day.dayOfWeek
              );
              const occupiedSlots = daySchedules.length;
              const availableSlots = Math.max(0, 16 - occupiedSlots); // Assuming 6 AM to 10 PM (16 hours)
              
              return (
                <div key={day.dayOfWeek} className="text-center">
                  <div className="font-medium mb-1">
                    {t(`teams.days.${dayNames[day.dayOfWeek as keyof typeof dayNames]}`).slice(0, 3)}
                  </div>
                  <div className="space-y-1">
                    <div className="text-green-600 bg-green-50 rounded px-1 py-0.5">
                      {availableSlots} {t('teams.schedule.available')}
                    </div>
                    <div className="text-red-600 bg-red-50 rounded px-1 py-0.5">
                      {occupiedSlots} {t('teams.schedule.occupied')}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Available Time Slots for Selected Day */}
        {selectedDay && (
          <div>
            <h4 className="font-medium mb-2">
              {t('teams.schedule.availableTimeSlots')}
            </h4>
            <div className="grid grid-cols-4 gap-2 text-xs">
              {Array.from({ length: 16 }, (_, i) => {
                const hour = i + 6; // Start from 6 AM
                const timeSlot = `${hour.toString().padStart(2, '0')}:00`;
                const nextTimeSlot = `${(hour + 1).toString().padStart(2, '0')}:00`;
                
                // Check if this hourly slot is occupied using the formula: startTime <= x && endTime > x
                // where x is the hour being checked (timeSlot)
                const isOccupied = facilitySchedulesToday.some(schedule => {
                  // Normalize time format by taking only HH:MM part (remove seconds if present)
                  const scheduleStart = schedule.startTime.substring(0, 5); // Get HH:MM part
                  const scheduleEnd = schedule.endTime.substring(0, 5); // Get HH:MM part
                  
                  return scheduleStart <= timeSlot && scheduleEnd > timeSlot;
                });

                return (
                  <div
                    key={timeSlot}
                    className={`p-2 rounded text-center ${
                      isOccupied
                        ? 'bg-red-50 text-red-700 border border-red-200'
                        : 'bg-green-50 text-green-700 border border-green-200'
                    }`}
                  >
                    {timeSlot}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
