import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { validators } from './validation';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateSchoolData {
  name: string;
  foundedYear: number;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface UpdateSchoolData {
  name?: string;
  foundedYear?: number;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export class SchoolService extends BaseService {
  constructor() {
    super('SchoolService');
  }

  /**
   * Get all schools
   */
  async getSchools(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSchools',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSchools(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'schools',
      }
    );
  }

  /**
   * Get school by ID
   */
  async getSchoolById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getSchoolById',
      'School',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSchoolById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'school',
      }
    );
  }

  /**
   * Create a new school
   */
  async createSchool(
    data: CreateSchoolData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateSchoolData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'School name is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 2 || data.name.length > 255) {
          return { field: 'name', message: 'School name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateSchoolData): ValidationError | null => {
        if (!data.foundedYear) {
          return { field: 'foundedYear', message: 'Founded year is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const currentYear = new Date().getFullYear();
        if (data.foundedYear < 1800 || data.foundedYear > currentYear) {
          return { field: 'foundedYear', message: `Founded year must be between 1800 and ${currentYear}`, code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateSchoolData): ValidationError | null => {
        if (data.email && data.email.length > 0) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(data.email)) {
            return { field: 'email', message: 'Email must be a valid email address', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: CreateSchoolData): ValidationError | null => {
        if (data.phone && data.phone.length > 0) {
          const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
          if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
            return { field: 'phone', message: 'Phone must be a valid phone number', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createSchool',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check for duplicate school name
        const existingSchool = await TenantAwareDB.getSchoolByName(validatedData.name, effectiveTenantId || undefined);
        if (existingSchool) {
          throw new BusinessRuleError('duplicate_name', 'A school with this name already exists');
        }

        return TenantAwareDB.createSchool(validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'school',
      }
    );
  }

  /**
   * Update a school
   */
  async updateSchool(
    id: string,
    data: UpdateSchoolData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateSchoolData): ValidationError | null => {
        if (data.name !== undefined) {
          if (!data.name || data.name.trim().length === 0) {
            return { field: 'name', message: 'School name cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.name.length < 2 || data.name.length > 255) {
            return { field: 'name', message: 'School name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateSchoolData): ValidationError | null => {
        if (data.foundedYear !== undefined) {
          const currentYear = new Date().getFullYear();
          if (data.foundedYear < 1800 || data.foundedYear > currentYear) {
            return { field: 'foundedYear', message: `Founded year must be between 1800 and ${currentYear}`, code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateSchoolData): ValidationError | null => {
        if (data.email !== undefined && data.email && data.email.length > 0) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(data.email)) {
            return { field: 'email', message: 'Email must be a valid email address', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateSchoolData): ValidationError | null => {
        if (data.phone !== undefined && data.phone && data.phone.length > 0) {
          const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
          if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
            return { field: 'phone', message: 'Phone must be a valid phone number', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateSchool',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if school exists
        const existingSchool = await TenantAwareDB.getSchoolById(id, effectiveTenantId || undefined);
        if (!existingSchool) {
          throw new NotFoundError('School not found');
        }

        // Check for duplicate school name (excluding current school)
        if (validatedData.name) {
          const duplicateSchool = await TenantAwareDB.getSchoolByName(validatedData.name, effectiveTenantId || undefined);
          if (duplicateSchool && duplicateSchool.id !== id) {
            throw new BusinessRuleError('duplicate_name', 'A school with this name already exists');
          }
        }

        return TenantAwareDB.updateSchool(id, validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'school',
      }
    );
  }

  /**
   * Delete a school
   */
  async deleteSchool(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteSchool',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if school exists
        const existingSchool = await TenantAwareDB.getSchoolById(id, effectiveTenantId || undefined);
        if (!existingSchool) {
          throw new NotFoundError('School not found');
        }

        // Check for dependent teams
        const teams = await TenantAwareDB.getTeamsBySchoolId(id, effectiveTenantId || undefined);
        if (teams && teams.length > 0) {
          throw new BusinessRuleError('has_teams', 'Cannot delete school with existing teams');
        }

        await TenantAwareDB.deleteSchool(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'school',
      }
    );
  }
  
  /**
   * Update a school with branches
   */
  async updateSchoolWithBranches(
    id: string,
    data: UpdateSchoolData & { branches?: string[] },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'updateSchoolWithBranches',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Update the school
        const schoolData = {
          name: data.name,
          address: data.address,
          phone: data.phone,
          email: data.email,
          logo: data.logo,
          foundedYear: data.foundedYear,
        };
        
        const updateResult = await TenantAwareDB.updateSchool(id, schoolData, effectiveTenantId || undefined);
        
        // If branches are provided, update the school-branch associations
        if (data.branches !== undefined) {
          await TenantAwareDB.assignBranchesToSchool(id, data.branches);
        }
        
        return updateResult;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'updateWithBranches', schoolId: id }
      }
    );
  }

  /**
   * Create a school with branches
   */
  async createSchoolWithBranches(
    data: CreateSchoolData & { branches?: string[] },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createSchoolWithBranches',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Create the school
        const schoolData = {
          name: data.name,
          foundedYear: data.foundedYear,
          address: data.address,
          phone: data.phone,
          email: data.email,
          logo: data.logo,
        };
        
        const school = await TenantAwareDB.createSchool(schoolData, effectiveTenantId || undefined);
        
        // If branches are provided, assign them to the school
        if (data.branches && data.branches.length > 0 && school) {
          await TenantAwareDB.assignBranchesToSchool(school.id, data.branches);
        }
        
        return school;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'createWithBranches' }
      }
    );
  }

  /**
   * Assign branches to a school
   */
  async assignBranchesToSchool(
    schoolId: string,
    branchIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'assignBranchesToSchool',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if school exists
        const existingSchool = await TenantAwareDB.getSchoolById(schoolId, effectiveTenantId || undefined);
        if (!existingSchool) {
          throw new NotFoundError('School not found');
        }
        
        await TenantAwareDB.assignBranchesToSchool(schoolId, branchIds);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'assignBranches', schoolId }
      }
    );
  }
}

// Factory function
export const schoolService = () => new SchoolService();
