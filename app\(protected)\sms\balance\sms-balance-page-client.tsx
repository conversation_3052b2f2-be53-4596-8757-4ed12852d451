'use client';

import { Suspense, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';
import { getSmsBalance, getBalanceStatus } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import SmsBalanceForm from './sms-balance-form';
import PricingTable from '@/components/sms/pricing-table';

function SmsBalanceContent() {
  const { t } = useSafeTranslation();
  const [data, setData] = useState<{
    balance: any;
    balanceStatus: any;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadBalance = async () => {
    try {
      setLoading(true);
      const [balance, balanceStatus] = await Promise.all([
        getSmsBalance(),
        getBalanceStatus()
      ]);
      setData({ balance, balanceStatus });
      setError(null);
    } catch (err) {
      setError('Error loading SMS balance information. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBalance();
  }, []);

  if (loading) {
    return <SmsBalanceSkeleton />;
  }

  if (error || !data) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Error loading SMS balance information. Please try again later.'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const { balance, balanceStatus } = data;

  return (
    <div className="space-y-6">
      {/* Current Balance */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5" />
              <span>{t('sms:balance.current.title')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-3xl font-bold">{balance?.balance || 0}</p>
                <p className="text-sm text-muted-foreground">{t('sms:balance.current.credits')}</p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge 
                  variant={
                    balanceStatus?.status === 'sufficient' ? 'default' : 
                    balanceStatus?.status === 'low' ? 'secondary' : 'destructive'
                  }
                  className="flex items-center space-x-1"
                >
                  {balanceStatus?.status === 'sufficient' && <CheckCircle className="h-3 w-3" />}
                  {balanceStatus?.status !== 'sufficient' && <AlertTriangle className="h-3 w-3" />}
                  <span>{(() => {
                    const status = balanceStatus?.status || 'sufficient';
                    if (status === 'sufficient') return t('sms:balance.status.sufficient');
                    if (status === 'low') return t('sms:balance.status.low');
                    if (status === 'empty') return t('sms:balance.status.empty');
                    return t('sms:balance.status.sufficient'); // fallback
                  })()}</span>
                </Badge>
              </div>
              {balanceStatus?.status && balanceStatus.status !== 'sufficient' && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {balanceStatus.status === 'empty'
                      ? t('sms:balance.warnings.empty')
                      : t('sms:balance.warnings.low')
                    }
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>{t('sms:balance.history.title')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {t('sms:balance.history.lastUpdated')}: {balance?.lastUpdated ? new Date(balance.lastUpdated).toLocaleString() : t('sms:balance.history.never')}
              </p>
              <p className="text-sm text-muted-foreground">
                {t('sms:balance.history.accountCreated')}: {balance?.createdAt ? new Date(balance.createdAt).toLocaleDateString() : t('sms:balance.history.unknown')}
              </p>
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm">
                  <strong>Tip:</strong> {t('sms:balance.history.tip')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Credits Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('sms:balance.add.title')}</CardTitle>
          <CardDescription>
            {t('sms:balance.add.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SmsBalanceForm
            currentBalance={balance?.balance || 0}
            onBalanceUpdate={loadBalance}
          />
        </CardContent>
      </Card>

      {/* Pricing Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('sms:balance.info.title')}</CardTitle>
          <CardDescription>
            {t('sms:balance.info.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-3">{t('sms:balance.info.usage.title')}</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• {t('sms:balance.info.usage.items.0')}</li>
                <li>• {t('sms:balance.info.usage.items.1')}</li>
                <li>• {t('sms:balance.info.usage.items.2')}</li>
                <li>• {t('sms:balance.info.usage.items.3')}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">{t('sms:balance.info.types.title')}</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li>• {t('sms:balance.info.types.items.0')}</li>
                <li>• {t('sms:balance.info.types.items.1')}</li>
                <li>• {t('sms:balance.info.types.items.2')}</li>
                <li>• {t('sms:balance.info.types.items.3')}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function SmsBalanceSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="h-6 w-32 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-8 w-16 bg-muted animate-pulse rounded" />
              <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              <div className="h-6 w-20 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <div className="h-6 w-32 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-4 w-48 bg-muted animate-pulse rounded" />
              <div className="h-4 w-40 bg-muted animate-pulse rounded" />
              <div className="h-16 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>

        {/* Pricing Table */}
        <PricingTable />
      </div>
    </div>
  );
}

export default function SmsBalancePageClient() {
  const { t } = useSafeTranslation();
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('sms:balance.title')}</h1>
          <p className="text-muted-foreground">
            {t('sms:balance.description')}
          </p>
        </div>
      </div>

      <Suspense fallback={<SmsBalanceSkeleton />}>
        <SmsBalanceContent />
      </Suspense>
    </div>
  );
}
