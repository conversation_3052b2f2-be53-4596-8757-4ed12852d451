{"facilities": {"title": "Facilities", "edit": "Edit Facility", "details": {"name": "Facility Name", "address": "Address", "type": "Type", "capacity": "Capacity", "maxCapacity": "Max Capacity", "dimensions": "Dimensions", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "unit": "Unit", "information": "Facility Information", "people": "people"}, "types": {"court": "Court", "field": "Field", "pool": "Pool", "studio": "Studio", "other": "Other"}, "units": {"meters": "Meters", "feet": "Feet"}, "form": {"title": "Facility Details", "name": "Facility Name", "type": "Facility Type", "address": "Address", "capacity": "Capacity", "dimensionUnit": "Dimension Unit", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "namePlaceholder": "Enter facility name", "addressPlaceholder": "Enter facility address", "selectType": "Select facility type", "capacityPlaceholder": "Enter maximum capacity", "meters": "Meters", "feet": "Feet", "lengthPlaceholderMeters": "Length in meters", "lengthPlaceholderFeet": "Length in feet", "widthPlaceholderMeters": "Width in meters", "widthPlaceholderFeet": "Width in feet"}, "placeholders": {"enterName": "Enter facility name", "enterAddress": "Enter facility address", "selectType": "Select facility type", "enterCapacity": "Enter max capacity", "lengthIn": "Length in {{unit}}", "widthIn": "Width in {{unit}}", "selectUnit": "Select unit", "selectFacility": "Select a facility"}, "actions": {"view": "View Details", "edit": "Edit", "delete": "Delete", "create": "Create Facility", "add": "Add New Facility", "addNew": "Add a new facility", "openMenu": "Open menu", "deleteConfirm": "Are you sure you want to delete this facility?"}, "deleteDialog": {"title": "Delete Facility", "description": "This action cannot be undone. This will permanently delete the facility and all associated data."}, "schedule": {"title": "Training Schedule", "weeklySchedule": "Weekly Schedule", "noSchedules": "No training schedules for this facility", "noSchedulesDescription": "Training schedules will appear here when teams are assigned to this facility.", "noSchedulesDay": "No schedules", "timeSlot": "{{startTime}} - {{endTime}}", "team": "Team: {{teamName}}", "scheduleOverview": "Schedule Overview", "trainingTimes": "Training Times", "session": "session", "sessions": "sessions"}}}