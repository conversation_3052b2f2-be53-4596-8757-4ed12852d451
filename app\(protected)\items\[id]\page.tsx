import { notFound } from "next/navigation";
import { getItemById } from "@/lib/actions";
import ItemDetailClient from "./item-detail-client";

interface ItemDetailPageProps {
  params: Promise<{ id: string }>;
}

export default async function ItemDetailPage({ params }: ItemDetailPageProps) {
  const resolvedParams = await params;
  
  try {
    const item = await getItemById(resolvedParams.id);
    
    if (!item) {
      notFound();
    }

    return <ItemDetailClient item={item} />;
  } catch (error) {
    console.error("Error fetching item:", error);
    notFound();
  }
}
