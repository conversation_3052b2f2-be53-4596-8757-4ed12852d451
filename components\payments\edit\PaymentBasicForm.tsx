import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { formatDateToLocalString } from "@/lib/utils/date-formatter";
import type { PaymentFormData, PaymentStatus, PaymentType, PaymentMethod } from "@/hooks/payments/usePaymentForm";

interface Athlete {
  id: string;
  name: string;
  surname: string;
  parentEmail?: string | null;
  parentPhone?: string | null;
  nationalId?: string | null;
}

interface PaymentBasicFormProps {
  formData: PaymentFormData;
  errors: Record<string, string>;
  athletes: Athlete[];
  onUpdateField: (field: keyof PaymentFormData, value: string) => void;
}

const paymentStatusOptions: { value: PaymentStatus; label: string }[] = [
  { value: "pending", label: "Pending" },
  { value: "completed", label: "Completed" },
  { value: "overdue", label: "Overdue" },
  { value: "cancelled", label: "Cancelled" },
];

const paymentTypeOptions: { value: PaymentType; label: string }[] = [
  { value: "fee", label: "Fee" },
  { value: "equipment", label: "Equipment" },
  { value: "other", label: "Other" },
];

const paymentMethodOptions: { value: string; label: string }[] = [
  { value: "none", label: "Not specified" },
  { value: "cash", label: "Cash" },
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "credit_card", label: "Credit Card" },
];

export function PaymentBasicForm({ 
  formData, 
  errors, 
  athletes, 
  onUpdateField 
}: PaymentBasicFormProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      {/* Athlete Selection */}
      <div className="space-y-2">
        <Label htmlFor="athleteId">{t("payments.details.athlete")}</Label>
        <Select 
          value={formData.athleteId} 
          onValueChange={(value) => onUpdateField("athleteId", value)}
        >
          <SelectTrigger className={errors.athleteId ? "border-red-500" : ""}>
            <SelectValue placeholder={t("payments.placeholders.selectAthlete")} />
          </SelectTrigger>
          <SelectContent>
            {athletes.map((athlete) => (
              <SelectItem key={athlete.id} value={athlete.id}>
                {athlete.name} {athlete.surname}
                {athlete.nationalId && ` (${athlete.nationalId})`}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.athleteId && (
          <p className="text-sm text-red-500">{errors.athleteId}</p>
        )}
      </div>

      {/* Amount */}
      <div className="space-y-2">
        <Label htmlFor="amount">{t("payments.details.amount")}</Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          value={formData.amount}
          onChange={(e) => onUpdateField("amount", e.target.value)}
          className={errors.amount ? "border-red-500" : ""}
          placeholder={t("payments.placeholders.enterAmount")}
        />
        {errors.amount && (
          <p className="text-sm text-red-500">{errors.amount}</p>
        )}
      </div>

      {/* Payment Date */}
      <div className="space-y-2">
        <Label>{t("payments.details.date")}</Label>
        <DatePicker
          date={formData.date ? new Date(formData.date) : undefined}
          onSelect={(date) => {
            if (date) {
              onUpdateField("date", formatDateToLocalString(date));
            } else {
              onUpdateField("date", "");
            }
          }}
          className={errors.date ? "border-red-500" : ""}
          placeholder={t("payments.placeholders.selectDate")}
        />
        {errors.date && (
          <p className="text-sm text-red-500">{errors.date}</p>
        )}
      </div>

      {/* Due Date */}
      <div className="space-y-2">
        <Label>{t("payments.details.dueDate")}</Label>
        <DatePicker
          date={formData.dueDate ? new Date(formData.dueDate) : undefined}
          onSelect={(date) => {
            if (date) {
              onUpdateField("dueDate", formatDateToLocalString(date));
            } else {
              onUpdateField("dueDate", "");
            }
          }}
          className={errors.dueDate ? "border-red-500" : ""}
          placeholder={t("payments.placeholders.selectDueDate")}
        />
        {errors.dueDate && (
          <p className="text-sm text-red-500">{errors.dueDate}</p>
        )}
      </div>

      {/* Status */}
      <div className="space-y-2">
        <Label>{t("payments.details.status")}</Label>
        <Select
          value={formData.status}
          onValueChange={(value) => {
            onUpdateField("status", value);
            // Reset payment method if status is not completed
            if (value !== "completed") {
              onUpdateField("method", "");
            }
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {paymentStatusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {t(`payments.status.${option.value}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Type */}
      <div className="space-y-2">
        <Label>{t("payments.details.type")}</Label>
        <Select
          value={formData.type}
          onValueChange={(value) => onUpdateField("type", value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {paymentTypeOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {t(`payments.types.${option.value}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Payment Method */}
      <div className="space-y-2">
        <Label>{t("payments.details.method")}</Label>
        <Select
          value={formData.method || "none"}
          onValueChange={(value) => onUpdateField("method", value === "none" ? "" : value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {paymentMethodOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.value === "none" ? t("payments.methods.not_specified") : t(`payments.methods.${option.value}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {formData.status !== "completed" && (
          <p className="text-xs text-muted-foreground">
            {t("payments.methodHint")}
          </p>
        )}
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">
          {t("payments.details.description")} ({t("common.optional")})
        </Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => onUpdateField("description", e.target.value)}
          placeholder={t("payments.placeholders.enterDescription")}
          rows={3}
        />
      </div>
    </div>
  );
}
