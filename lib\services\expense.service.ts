import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateExpenseData {
  amount: string;
  date: string;
  category: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other';
  description: string;
  instructorId?: string;
  facilityId?: string;
}

export interface UpdateExpenseData {
  amount?: string;
  date?: string;
  category?: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other';
  description?: string;
  instructorId?: string;
  facilityId?: string;
}

export class ExpenseService extends BaseService {
  constructor() {
    super('ExpenseService');
  }

  /**
   * Get all expenses
   */
  async getExpenses(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getExpenses',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getExpenses(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expenses',
      }
    );
  }

  /**
   * Get expenses with pagination
   */
  async getExpensesPaginated(
    options: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      category?: string;
      fromDate?: string;
      toDate?: string;
      instructorId?: string;
      facilityId?: string;
    },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getExpensesPaginated',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getExpensesPaginated(options, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expenses',
        metadata: options,
      }
    );
  }

  /**
   * Get expense by ID
   */
  async getExpenseById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getExpenseById',
      'Expense',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getExpenseById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expense',
      }
    );
  }

  /**
   * Create a new expense
   */
  async createExpense(
    data: CreateExpenseData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateExpenseData): ValidationError | null => {
        if (!data.amount || data.amount.trim().length === 0) {
          return { field: 'amount', message: 'Amount is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const amountValue = parseFloat(data.amount);
        if (isNaN(amountValue) || amountValue <= 0) {
          return { field: 'amount', message: 'Amount must be a valid positive number', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateExpenseData): ValidationError | null => {
        if (!data.date || data.date.trim().length === 0) {
          return { field: 'date', message: 'Date is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const dateValue = new Date(data.date);
        if (isNaN(dateValue.getTime())) {
          return { field: 'date', message: 'Date must be a valid date', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (data: CreateExpenseData): ValidationError | null => {
        if (!data.category) {
          return { field: 'category', message: 'Category is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const validCategories = ['salary', 'insurance', 'rent', 'equipment', 'other'];
        if (!validCategories.includes(data.category)) {
          return { field: 'category', message: 'Invalid expense category', code: 'INVALID_VALUE' };
        }
        return null;
      },
      (data: CreateExpenseData): ValidationError | null => {
        if (!data.description || data.description.trim().length === 0) {
          return { field: 'description', message: 'Description is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.description.length > 1000) {
          return { field: 'description', message: 'Description must be no more than 1000 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createExpense',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Validate optional references
        if (validatedData.instructorId) {
          const instructor = await TenantAwareDB.getInstructorById(validatedData.instructorId, effectiveTenantId || undefined);
          if (!instructor) {
            throw new BusinessRuleError('invalid_instructor', 'Selected instructor does not exist');
          }
        }

        if (validatedData.facilityId) {
          const facility = await TenantAwareDB.getFacilityById(validatedData.facilityId, effectiveTenantId || undefined);
          if (!facility) {
            throw new BusinessRuleError('invalid_facility', 'Selected facility does not exist');
          }
        }

        return TenantAwareDB.createExpense(validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expense',
      }
    );
  }

  /**
   * Update an expense
   */
  async updateExpense(
    id: string,
    data: UpdateExpenseData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateExpenseData): ValidationError | null => {
        if (data.amount !== undefined) {
          if (!data.amount || data.amount.trim().length === 0) {
            return { field: 'amount', message: 'Amount cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const amountValue = parseFloat(data.amount);
          if (isNaN(amountValue) || amountValue <= 0) {
            return { field: 'amount', message: 'Amount must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateExpenseData): ValidationError | null => {
        if (data.date !== undefined) {
          if (!data.date || data.date.trim().length === 0) {
            return { field: 'date', message: 'Date cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const dateValue = new Date(data.date);
          if (isNaN(dateValue.getTime())) {
            return { field: 'date', message: 'Date must be a valid date', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateExpenseData): ValidationError | null => {
        if (data.category !== undefined) {
          const validCategories = ['salary', 'insurance', 'rent', 'equipment', 'other'];
          if (!validCategories.includes(data.category)) {
            return { field: 'category', message: 'Invalid expense category', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
      (data: UpdateExpenseData): ValidationError | null => {
        if (data.description !== undefined) {
          if (!data.description || data.description.trim().length === 0) {
            return { field: 'description', message: 'Description cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.description.length > 1000) {
            return { field: 'description', message: 'Description must be no more than 1000 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateExpense',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if expense exists
        const existingExpense = await TenantAwareDB.getExpenseById(id, effectiveTenantId || undefined);
        if (!existingExpense) {
          throw new NotFoundError('Expense not found');
        }

        // Validate optional references
        if (validatedData.instructorId !== undefined) {
          if (validatedData.instructorId) {
            const instructor = await TenantAwareDB.getInstructorById(validatedData.instructorId, effectiveTenantId || undefined);
            if (!instructor) {
              throw new BusinessRuleError('invalid_instructor', 'Selected instructor does not exist');
            }
          }
        }

        if (validatedData.facilityId !== undefined) {
          if (validatedData.facilityId) {
            const facility = await TenantAwareDB.getFacilityById(validatedData.facilityId, effectiveTenantId || undefined);
            if (!facility) {
              throw new BusinessRuleError('invalid_facility', 'Selected facility does not exist');
            }
          }
        }

        return TenantAwareDB.updateExpense(id, validatedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expense',
      }
    );
  }

  /**
   * Delete an expense
   */
  async deleteExpense(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteExpense',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if expense exists
        const existingExpense = await TenantAwareDB.getExpenseById(id, effectiveTenantId || undefined);
        if (!existingExpense) {
          throw new NotFoundError('Expense not found');
        }

        await TenantAwareDB.deleteExpense(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'expense',
      }
    );
  }

  /**
   * Get expenses by category
   */
  async getExpensesByCategory(
    category: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other',
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getExpensesByCategory',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getExpensesByCategory(category, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expenses',
        metadata: { category },
      }
    );
  }

  /**
   * Get expenses by date range
   */
  async getExpensesByDateRange(
    startDate: string,
    endDate: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    const validationFunctions = [
      (): ValidationError | null => {
        const start = new Date(startDate);
        if (isNaN(start.getTime())) {
          return { field: 'startDate', message: 'Start date must be a valid date', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (): ValidationError | null => {
        const end = new Date(endDate);
        if (isNaN(end.getTime())) {
          return { field: 'endDate', message: 'End date must be a valid date', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (): ValidationError | null => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (start > end) {
          return { field: 'dateRange', message: 'Start date must be before end date', code: 'INVALID_VALUE' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'getExpensesByDateRange',
      { startDate, endDate },
      validationFunctions,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getExpensesByDateRange(startDate, endDate, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'expenses',
        metadata: { startDate, endDate },
      }
    );
  }
}

// Factory function
export const expenseService = () => new ExpenseService();
