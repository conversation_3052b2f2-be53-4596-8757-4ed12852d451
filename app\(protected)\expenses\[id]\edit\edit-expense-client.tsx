"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import Link from "next/link";
import { ArrowLeft, Save } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { updateExpense } from "@/lib/actions/expenses";
import { Expense, Instructor, Facility } from "@/lib/types";
import {useToast} from "@/hooks/use-toast";

interface EditExpenseClientProps {
  expense: Expense;
  instructors: Instructor[];
  facilities: Facility[];
}

export default function EditExpenseClient({ expense, instructors, facilities }: EditExpenseClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    amount: expense.amount,
    category: expense.category,
    date: new Date(expense.date),
    description: expense.description,
    instructorId: expense.instructor?.id || "none",
    facilityId: expense.facility?.id || "none",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.amount || !formData.category || !formData.date || !formData.description) {
      toast({
        title: t('common.error'),
        description: t('expenses.messages.requiredFields', 'Please fill in all required fields'),
        variant: "destructive",
      });
      return;
    }

    // Validate amount is positive
    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: t('common.error'),
        description: t('expenses.messages.invalidAmount', 'Please enter a valid amount'),
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      const result = await updateExpense(expense.id, {
        amount: formData.amount,
        category: formData.category,
        date: formData.date.toISOString().split('T')[0],
        description: formData.description,
        instructorId: formData.instructorId === "none" ? undefined : formData.instructorId,
        facilityId: formData.facilityId === "none" ? undefined : formData.facilityId,
      });
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('expenses.messages.updateSuccess', 'Expense updated successfully'),
        });
        router.push("/expenses");
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'expenses.messages.updateError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error updating expense:', error);
      toast({
        title: t('common.error'),
        description: t('expenses.messages.updateError', 'Failed to update expense'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Button
        variant="ghost"
        className="mb-6"
        asChild
      >
        <Link href="/expenses">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Link>
      </Button>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('expenses.edit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="amount">{t('expenses.details.amount')} *</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.amount}
                    onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder={t('expenses.placeholders.enterAmount')}
                    className="max-w-[200px]"
                    disabled={loading}
                  />
                  <span className="text-sm text-muted-foreground">{t('common.currency')}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category">{t('expenses.details.category')} *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value as any }))}
                  disabled={loading}
                >
                  <SelectTrigger className="max-w-[200px]">
                    <SelectValue placeholder={t('expenses.placeholders.selectCategory')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="salary">{t('expenses.categories.salary')}</SelectItem>
                    <SelectItem value="insurance">{t('expenses.categories.insurance')}</SelectItem>
                    <SelectItem value="rent">{t('expenses.categories.rent')}</SelectItem>
                    <SelectItem value="equipment">{t('expenses.categories.equipment')}</SelectItem>
                    <SelectItem value="other">{t('expenses.categories.other')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>{t('expenses.details.date')} *</Label>
                <DatePicker
                  date={formData.date}
                  onSelect={(date) => setFormData(prev => ({ ...prev, date: date || new Date() }))}
                  placeholder={t('expenses.placeholders.selectDate')}
                />
              </div>

              {instructors.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="instructor">{t('expenses.details.instructor')}</Label>
                  <Select
                    value={formData.instructorId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, instructorId: value }))}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('expenses.placeholders.selectInstructor')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('common.actions.none')}</SelectItem>
                      {instructors.map((instructor) => (
                        <SelectItem key={instructor.id} value={instructor.id}>
                          {instructor.name} {instructor.surname}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {facilities.length > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="facility">{t('expenses.details.facility')}</Label>
                  <Select
                    value={formData.facilityId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, facilityId: value }))}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('expenses.placeholders.selectFacility')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('common.actions.none')}</SelectItem>
                      {facilities.map((facility) => (
                        <SelectItem key={facility.id} value={facility.id}>
                          {facility.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">{t('expenses.details.description')} *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t('expenses.placeholders.enterDescription')}
                rows={3}
                disabled={loading}
              />
            </div>
            
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                asChild
              >
                <Link href="/expenses">{t('common.actions.cancel')}</Link>
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-foreground" />
                    {t('common.actions.saving')}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t('expenses.actions.save')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
