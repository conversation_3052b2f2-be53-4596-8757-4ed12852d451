{"expenses": {"title": "Expenses", "new": "New Expense", "edit": "Edit Expense", "viewDetails": "Expense Details", "details": {"date": "Date", "amount": "Amount", "category": "Category", "description": "Description", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "facility": "Facility", "expenseDetails": "Expense Information", "expenseDate": "Expense Date", "expenseType": "Expense Type"}, "filters": {"fromDate": "From Date", "toDate": "To Date", "selectFromDate": "Select from date", "selectToDate": "Select to date"}, "categories": {"equipment": "Equipment", "insurance": "Insurance", "other": "Other", "salary": "Salary", "rent": "Rent"}, "types": {"general": "General Expense", "instructorOnly": "Instructor Related", "facilityOnly": "Facility Related", "instructorFacility": "Instructor & Facility"}, "actions": {"edit": "Edit", "delete": "Delete", "view": "View Details", "save": "Save Expense", "openMenu": "Open menu", "deleteConfirm": "Are you sure you want to delete this expense?"}, "messages": {"requiredFields": "Please fill in all required fields", "invalidAmount": "Please enter a valid amount", "createSuccess": "Expense created successfully", "createError": "Failed to create expense", "updateSuccess": "Expense updated successfully", "updateError": "Failed to update expense", "deleteSuccess": "Expense deleted successfully", "deleteError": "Failed to delete expense", "confirmDelete": "Are you sure you want to delete this expense?", "deleteWarning": "This action cannot be undone."}, "deleteDialog": {"title": "Delete Expense", "description": "Are you sure you want to delete this expense? This action cannot be undone."}, "placeholders": {"enterAmount": "0.00", "selectCategory": "Select category", "enterDescription": "Enter expense description", "searchExpenses": "Search expenses...", "selectInstructor": "Select instructor", "selectFacility": "Select facility", "filterByCategory": "Filter by category", "searchMinChars": "Type at least 3 characters", "selectDate": "Select date"}}}