{"nav": {"dashboard": "Dashboard", "schools": "Schools", "instructors": "Instructors", "teams": "Teams", "athletes": "Athletes", "payments": "Payments", "expenses": "Expenses", "facilities": "Facilities", "items": "Items"}, "breadcrumbs": {"home": "Home", "dashboard": "Dashboard", "schools": "Schools", "instructors": "Instructors", "teams": "Teams", "athletes": "Athletes", "payments": "Payments", "expenses": "Expenses", "facilities": "Facilities", "items": "Items", "plans": "Plans", "new": "New", "edit": "Edit", "details": "Details", "sell": "<PERSON>ll"}, "common": {"actionsHeader": "Actions", "details": {"description": "Description"}, "placeholders": {"enterDescription": "Enter description"}, "all": "All", "actions": {"title": "Actions", "create": "Create", "edit": "Edit", "delete": "Delete", "deleting": "Deleting...", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "back": "Back", "confirm": "Are you sure?", "view": "View", "assign": "Assign", "upload": "Upload", "download": "Download", "none": "None", "reset": "Reset", "toggleMenu": "Toggle menu", "signOut": "Sign out", "checking": "Checking...", "processing": "Processing...", "add": "Add", "remove": "Remove", "removing": "Removing...", "search": "Search", "filter": "Filter", "apply": "Apply", "clear": "Clear", "clear_filters": "Clear Filters", "filterBy": "Filter by {{field}}"}, "status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "loading": "Loading...", "dateCreated": "Date Created", "noData": "No data available", "error": "An error occurred", "success": "Success", "optional": "Optional", "paymentPlan": "Payment Plan", "currency": "TL", "month": "month", "unknown": "Unknown", "notAssigned": "Not assigned", "noDescription": "No description provided", "dayOfMonth": " of the month", "generatedOn": "Generated on", "locale": "en-US", "messages": {"updateSuccess": "Successfully updated", "updateError": "Failed to update", "deleteSuccess": "Successfully deleted", "deleteError": "Failed to delete", "createSuccess": "Successfully created", "createError": "Failed to create"}, "createdAt": "Created at", "sortBy": "Sort by", "order": "Order", "pageSize": "Page size", "page_size": "Page size", "ascending": "Ascending", "descending": "Descending", "search": "Search", "previous": "Previous", "next": "Next", "clear_filters": "Clear Filters", "pagination_info": "Showing {{start}}-{{end}} of {{total}} results", "page_of": "Page {{page}} of {{total}}", "pagination": {"previous": "Previous", "next": "Next", "showing": "Showing {{start}}-{{end}} of {{total}} results"}, "upload": {"dragDrop": "Drag and drop files here or click to upload", "maxSize": "Maximum file size: {{size}}", "formats": "Supported formats: {{formats}}"}, "table": {"previous": "Previous", "next": "Next", "noResults": "No results.", "searchPlaceholder": "Search..."}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "validation": {"required": "This field is required", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be at most {{max}} characters", "email": "Invalid email address", "phoneMin": "Phone number must be at least {{min}} characters", "yearMin": "Year must be after {{min}}", "yearMax": "Year cannot be in the future", "selectAtLeastOne": "Please select at least one {{field}}", "maxFileSize": "Max file size is {{size}}", "unsupportedFormat": "Only {{formats}} formats are supported"}, "fields": {"branch": "branch", "branches": "branches", "school": "school", "schools": "schools"}, "branches": {"Football": "Football", "Basketball": "Basketball", "Tennis": "Tennis", "Swimming": "Swimming", "Volleyball": "Volleyball", "Martial Arts": "Martial Arts"}}}