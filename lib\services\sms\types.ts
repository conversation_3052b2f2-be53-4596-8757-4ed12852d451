/**
 * SMS service types and interfaces
 */

export interface SmsMessage {
  /** Receiver phone number */
  receiver: string;
  /** Message content */
  message: string;
}

export interface SendSmsParams {
  /** Sender identifier */
  senderIdentifier: string;
  /** Encoding parameter for the SMS */
  encoding: string;
  /** Array of messages to send */
  messages: SmsMessage[];
}

export interface SmsProviderResponse {
  /** Whether the SMS sending was successful */
  success: boolean;
  /** Provider-specific response data */
  data?: any;
  /** Error message if failed */
  error?: string;
  /** Provider-specific error code */
  errorCode?: string;
  /** Array of message IDs for tracking */
  messageIds?: string[];
}

/**
 * Interface that all SMS providers must implement
 */
export interface SmsProvider {
  /**
   * Send SMS messages
   * @param params SMS parameters including sender, encoding, and messages
   * @returns Promise with provider response
   */
  sendSms(params: SendSmsParams): Promise<SmsProviderResponse>;
  
  /**
   * Get provider name for logging/debugging
   */
  getProviderName(): string;
}

export interface SmsServiceConfig {
  /** Provider instance to use */
  provider: SmsProvider;
  /** Default sender identifier if not provided in request */
  defaultSender?: string;
  /** Default encoding if not provided in request */
  defaultEncoding?: string;
  /** Enable/disable SMS sending (useful for testing) */
  enabled?: boolean;
}
