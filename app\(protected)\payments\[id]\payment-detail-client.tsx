"use client";

import { useRouter } from "next/navigation";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  ChevronLeft, 
  User, 
  CreditCard, 
  Calendar, 
  FileText, 
  Mail, 
  Phone, 
  IdCard, 
  Edit, 
  Trash2, 
  Printer, 
  Download, 
  MoreHorizontal,
  CheckCircle
} from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { BadgeStatus } from "@/components/ui/badge-status";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, Al<PERSON><PERSON>ialogFooter, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ead<PERSON>, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { deletePayment, processPayment } from "@/lib/db/actions";

interface PaymentDetailProps {
  payment: {
    id: string;
    athleteId: string;
    athletePaymentPlanId?: string | null;
    amount: string;
    date: string;
    dueDate: string;
    status: "pending" | "completed" | "overdue" | "cancelled";
    type: "fee" | "equipment" | "other";
    description?: string | null;
    athlete: {
      id: string;
      name: string;
      surname: string;
      parentEmail?: string | null;
      parentPhone?: string | null;
      nationalId?: string | null;
    } | null;
    paymentPlan: {
      id: string;
      name: string;
      monthlyValue: string;
      assignDay: number;
      dueDay: number;
      status: "active" | "inactive";
      description?: string | null;
    } | null;
  };
}

export default function PaymentDetailClient({ payment }: PaymentDetailProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleBack = () => {
    router.push("/payments");
  };

  const handleEdit = () => {
    router.push(`/payments/${payment.id}/edit`);
  };

  const handleDelete = async () => {
    try {
      await deletePayment(payment.id);
      toast({
        title: t('payments.messages.deleteSuccess'),
        variant: "default",
      });
      router.push("/payments");
    } catch (error) {
      toast({
        title: t('payments.messages.deleteError'),
        variant: "destructive",
      });
    }
  };

  const handleGenerateReceipt = async () => {
    setIsGeneratingReceipt(true);
    try {
      // Here you would add logic to generate a receipt
      // This would typically involve creating a PDF or another printable format
      // For now, we'll just simulate with a timeout
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Receipt generated successfully",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Failed to generate receipt",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  const handleProcessPayment = async () => {
    setIsProcessing(true);
    try {
      await processPayment(payment.id);
      toast({
        title: t('payments.messages.processSuccess'),
        description: t('payments.messages.processSuccessDetail'),
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      toast({
        title: t('payments.messages.processError'),
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('tr-TR', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    }).format(parseFloat(amount));
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'fee':
        return <CreditCard className="h-4 w-4" />;
      case 'equipment':
        return <FileText className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'fee':
        return t('payments.types.fee');
      case 'equipment':
        return t('payments.types.equipment');
      case 'other':
        return t('payments.types.other');
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleBack}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {t('payments.details.title')}
            </h1>
            <p className="text-muted-foreground">
              {payment.athlete ? `${payment.athlete.name} ${payment.athlete.surname}` : t('common.unknown')}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleGenerateReceipt} disabled={isGeneratingReceipt}>
            <Printer className="mr-2 h-4 w-4" />
            {t('payments.actions.generateReceipt')}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">{t('payments.actions.openMenu')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('payments.actions.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setDeleteDialogOpen(true)} className="text-destructive">
                <Trash2 className="mr-2 h-4 w-4" />
                {t('payments.actions.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Payment Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t('payments.details.paymentInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('payments.details.amount')}
                </p>
                <p className="text-2xl font-bold">
                  {formatAmount(payment.amount)} {t('common.currency')}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('payments.details.status')}
                </p>
                <div className="mt-1">
                  <BadgeStatus status={payment.status} type="payment" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('payments.details.date')}
                </p>
                <p className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {formatDate(payment.date)}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('payments.details.type')}
                </p>
                <p className="flex items-center gap-2">
                  {getTypeIcon(payment.type)}
                  {getTypeLabel(payment.type)}
                </p>
              </div>
            </div>

            {payment.description && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t('payments.details.description')}
                </p>
                <p className="text-base">{payment.description}</p>
              </div>
            )}
          </CardContent>
          {payment.status !== "completed" && (
            <CardFooter className="border-t pt-4">
              <Button 
                variant="default" 
                size="sm" 
                onClick={handleProcessPayment}
                disabled={isProcessing}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                {isProcessing ? t('common.actions.processing') : t('payments.actions.process')}
              </Button>
            </CardFooter>
          )}
        </Card>

        {/* Athlete Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('athletes.details.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {payment.athlete ? (
              <>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('athletes.details.fullName')}
                  </p>
                  <p className="text-lg font-semibold">
                    {payment.athlete.name} {payment.athlete.surname}
                  </p>
                </div>

                {payment.athlete.nationalId && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t('athletes.details.nationalId')}
                    </p>
                    <p className="flex items-center gap-2">
                      <IdCard className="h-4 w-4" />
                      {payment.athlete.nationalId}
                    </p>
                  </div>
                )}

                {payment.athlete.parentEmail && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t('athletes.details.parentEmail')}
                    </p>
                    <p className="flex items-center gap-2 break-all">
                      <Mail className="h-4 w-4 flex-shrink-0" />
                      {payment.athlete.parentEmail}
                    </p>
                  </div>
                )}

                {payment.athlete.parentPhone && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {t('athletes.details.parentPhone')}
                    </p>
                    <p className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {payment.athlete.parentPhone}
                    </p>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/athletes/${payment.athlete.id}`}>
                      {t('athletes.viewDetails')}
                    </a>
                  </Button>
                </div>
              </>
            ) : (
              <p className="text-muted-foreground">{t('common.noData')}</p>
            )}
          </CardContent>
        </Card>

        {/* Payment Plan Information (if exists) */}
        {payment.paymentPlan && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {t('payments.paymentPlan')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('payments.plans.name')}
                  </p>
                  <p className="text-lg font-semibold">{payment.paymentPlan.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('payments.plans.amount')}
                  </p>
                  <p className="text-lg font-semibold">
                    {formatAmount(payment.paymentPlan.monthlyValue)} {t('common.currency')}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('payments.plans.dueDay')}
                  </p>
                  <p className="text-base">
                    {t('payments.plans.dayOfMonth', { day: payment.paymentPlan.dueDay })}
                  </p>
                </div>
              </div>

              {payment.paymentPlan.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {t('payments.plans.description')}
                  </p>
                  <p className="text-base">{payment.paymentPlan.description}</p>
                </div>
              )}

              <div className="pt-4 border-t">
                <Button variant="outline" size="sm" asChild>
                  <a href={`/payments/plans/${payment.paymentPlan.id}`}>
                    {t('payments.viewPlan')}
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Receipt Preview Card */}
        {payment.status === "completed" && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Printer className="h-5 w-5" />
                {t('payments.receipt.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border rounded-md p-6 bg-muted/20">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-xl font-bold">{t('payments.receipt.title')}</h3>
                    <p className="text-sm text-muted-foreground">{t('payments.receipt.number')}: {payment.id.slice(0, 8).toUpperCase()}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{t('payments.receipt.issueDate')}</p>
                    <p>{formatDate(payment.date)}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="font-medium">{t('payments.receipt.to')}:</p>
                    {payment.athlete && (
                      <p>{payment.athlete.name} {payment.athlete.surname}</p>
                    )}
                  </div>
                  <div>
                    <p className="font-medium">{t('payments.receipt.for')}:</p>
                    <p>{getTypeLabel(payment.type)}</p>
                  </div>
                </div>
                
                <div className="border-t border-dashed pt-4 mt-4">
                  <div className="flex justify-between items-center font-bold">
                    <p>{t('payments.details.amount')}</p>
                    <p>{formatAmount(payment.amount)} {t('common.currency')}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" size="sm">
                  <Printer className="mr-2 h-4 w-4" />
                  {t('payments.actions.printReceipt')}
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  {t('common.actions.download')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('common.actions.confirm')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('payments.actions.deleteConfirm')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              {t('common.actions.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
