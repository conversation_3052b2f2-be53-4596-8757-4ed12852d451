"use server";

import { getServerUserId } from '../tenant-utils-server';
import { instructorService } from '../services/instructor.service';
import { revalidatePath } from 'next/cache';
import {BusinessRuleError} from "@/lib/errors/errors";

// Instructors
export async function getInstructors() {
  try {
    // Try using the service first
    const service = instructorService();
    const result = await service.getInstructors();

    if (result.success) {
      return result.data || [];
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get instructors");
  } catch (error) {
    console.error("Error getting instructors:", error);
    throw error;
  }
}

export async function getInstructorsPaginated(options: {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: {
    name?: string;
    surname?: string;
    email?: string;
    phone?: string;
    branchId?: string;
    schoolId?: string;
  };
}) {
  try {
    // Try using the service first
    const service = instructorService();
    const result = await service.getInstructorsPaginated(options);

    if (result.success) {
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || "Failed to get paginated instructors");
  } catch (error) {
    console.error("Error getting paginated instructors:", error);
    throw error;
  }
}

export async function getInstructorById(id: string) {
  console.log("Server: Getting instructor by ID:", id);
  try {
    // Try using the service first
    const service = instructorService();
    const result = await service.getInstructorById(id);

    if (result.success) {
      console.log("Server: Retrieved instructor via service:", result.data);
      return result.data;
    }

    // If service fails, log error and throw
    console.error("Service call failed:", result.error);
    throw new Error(result.error?.userMessage || `Failed to get instructor with ID ${id}`);
  } catch (error) {
    console.error("Server: Error getting instructor by ID:", error);
    throw error;
  }
}

export async function createInstructor(data: {
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: number;
  branchIds?: string[];
  schoolIds?: string[];
}) {
  
  try {
    const userId = await getServerUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Convert salary to string as required by the service
    const serviceData = {
      name: data.name,
      surname: data.surname,
      email: data.email,
      phone: data.phone,
      nationalId: data.nationalId || '',
      birthDate: data.birthDate || '',
      address: data.address || '',
      salary: data.salary !== undefined ? data.salary.toString() : ''
    };

    // Try using the service first
    const service = instructorService();
    const result = await service.createInstructor(serviceData, userId.toString());

    if (result.success) {
      // If branch IDs are provided, create instructor-branch relationships
      if (data.branchIds && data.branchIds.length > 0) {
        const branchResult = await service.updateInstructorBranches(result.data.id, data.branchIds, userId.toString());
        if (!branchResult.success) {
          console.error("Failed to update instructor branches:", branchResult.error);
          // Don't fail the entire operation, just log the error
        }
      }
      
      // If school IDs are provided, create instructor-school relationships
      if (data.schoolIds && data.schoolIds.length > 0) {
        const schoolResult = await service.updateInstructorSchools(result.data.id, data.schoolIds, userId.toString());
        if (!schoolResult.success) {
          console.error("Failed to update instructor schools:", schoolResult.error);
          // Don't fail the entire operation, just log the error
        }
      }

      // Revalidate paths
      revalidatePath('/instructors');
      revalidatePath(`/instructors/${result.data.id}`);
      
      return { 
        success: true, 
        data: result.data
      };
    }else{
      console.error("Service call failed:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create instructor' , errorType: 'general' };
    }
  } catch (error) {
    console.error('Error creating instructor:', error);
    return { success: false, error: 'Failed to create instructor' };
  }
}

export async function updateInstructor(id: string, data: {
  name?: string;
  surname?: string;
  email?: string;
  phone?: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: number;
  branchIds?: string[];
  schoolIds?: string[];
}) {
  
  try {
    const userId = await getServerUserId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    // Convert salary to string as required by the service
    const serviceData = {
      ...data,
      salary: data.salary !== undefined ? data.salary.toString() : undefined
    };

    // Try using the service first
    const service = instructorService();
    const result = await service.updateInstructor(id, serviceData, userId.toString());

    if (result.success) {
      // Handle branch/school updates if provided
      if (data.branchIds !== undefined) {
        const branchResult = await service.updateInstructorBranches(id, data.branchIds || [], userId.toString());
        if (!branchResult.success) {
          console.error("Failed to update instructor branches:", branchResult.error);
          // Don't fail the entire operation, just log the error
        }
      }
      
      if (data.schoolIds !== undefined) {
        const schoolResult = await service.updateInstructorSchools(id, data.schoolIds || [], userId.toString());
        if (!schoolResult.success) {
          console.error("Failed to update instructor schools:", schoolResult.error);
          // Don't fail the entire operation, just log the error
        }
      }

      // Revalidate paths
      revalidatePath('/instructors');
      revalidatePath(`/instructors/${id}`);
      
      return { 
        success: true, 
        data: result.data
      };
    }else{
      console.error("Service call failed:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to update instructor' , errorType: 'general' };
    }
  } catch (error) {
    console.error('Error updating instructor:', error);
    return { success: false, error: 'Failed to update instructor' };
  }
}

export async function updateInstructorBranches(instructorId: string, branchIds: string[]) {
  
  const service = instructorService();
  const result = await service.updateInstructorBranches(instructorId, branchIds);
  
  if (!result.success) {
    console.error('Error updating instructor branches:', result.error);
    return { success: false, error: 'Failed to update instructor branches' };
  }
  
  revalidatePath('/instructors');
  revalidatePath(`/instructors/${instructorId}`);
  return { success: true };
}

export async function updateInstructorSchools(instructorId: string, schoolIds: string[]) {
  
  const service = instructorService();
  const result = await service.updateInstructorSchools(instructorId, schoolIds);
  
  if (!result.success) {
    console.error('Error updating instructor schools:', result.error);
    return { success: false, error: 'Failed to update instructor schools' };
  }
  
  revalidatePath('/instructors');
  revalidatePath(`/instructors/${instructorId}`);
  return { success: true };
}

export async function deleteInstructor(id: string) {
  
  try {
    // Try using the service first
    const service = instructorService();
    const result = await service.deleteInstructor(id);

    if (result.success) {
      revalidatePath('/instructors');
      return { success: true };
    }

    // If service fails, log error and return error
    console.error("Service call failed:", result.error);
    if(result.error?.details && result.error.details.rule) {
      return { success: false, error: result.error?.details.rule , errorType: result.error?.cause?.name };
    }
    return { success: false, error: result.error?.userMessage || 'Failed to delete instructor' };
  } catch (error) {
    console.error('Error deleting instructor:', error);
    return { success: false, error: 'Failed to delete instructor', errorType: 'general' };
  }
}