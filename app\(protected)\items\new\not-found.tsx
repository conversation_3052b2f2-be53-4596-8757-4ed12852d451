"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, ArrowLeft } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export default function NotFound() {
  const { t } = useSafeTranslation();
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold tracking-tight">{t('items.errors.pageNotFound')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>{t('items.errors.pageNotFound')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            {t('items.errors.pageNotFoundDescription')}
          </p>
          <Link href="/items">
            <Button className="flex items-center space-x-2">
              <ArrowLeft className="h-4 w-4" />
              <span>{t('items.errors.backToItems')}</span>
            </Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
}
