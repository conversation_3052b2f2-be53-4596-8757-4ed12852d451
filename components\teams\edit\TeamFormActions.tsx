import { Button } from "@/components/ui/button";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface TeamFormActionsProps {
  onCancel: () => void;
  onSave: () => void;
  loading: boolean;
}

export function TeamFormActions({ onCancel, onSave, loading }: TeamFormActionsProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="flex gap-4 justify-end">
      <Button
        type="button"
        variant="outline"
        onClick={onCancel}
      >
        {t('common.actions.cancel')}
      </Button>
      <Button 
        type="button" 
        disabled={loading}
        onClick={onSave}
      >
        {loading ? t('common.actions.saving') : t('common.actions.save')}
      </Button>
    </div>
  );
}
