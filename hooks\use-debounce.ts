import { useState, useEffect } from 'react';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Enhanced debounce hook with immediate trigger option
 */
export function useAdvancedDebounce<T>(
  value: T,
  delay: number,
  options: {
    immediate?: boolean;
    minLength?: number;
    validator?: (value: T) => boolean;
  } = {}
): {
  debouncedValue: T;
  isDebouncing: boolean;
  trigger: () => void;
} {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const [isDebouncing, setIsDebouncing] = useState(false);

  const trigger = () => {
    setDebouncedValue(value);
    setIsDebouncing(false);
  };

  useEffect(() => {
    const { immediate = false, minLength = 0, validator } = options;

    // Check if value meets minimum requirements
    const valueString = String(value).trim();
    const meetsMinLength = valueString.length === 0 || valueString.length >= minLength;
    const passesValidation = !validator || validator(value);

    if (!meetsMinLength || !passesValidation) {
      setIsDebouncing(false);
      return;
    }

    if (immediate) {
      setDebouncedValue(value);
      setIsDebouncing(false);
      return;
    }

    setIsDebouncing(true);
    const handler = setTimeout(() => {
      setDebouncedValue(value);
      setIsDebouncing(false);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay, options]);

  return { debouncedValue, isDebouncing, trigger };
}
