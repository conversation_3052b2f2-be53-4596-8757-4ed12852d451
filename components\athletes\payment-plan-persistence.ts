"use server";

import { revalidatePath } from "next/cache";
import { assignPaymentPlan, deactivateAssignment, reactivateAssignment, deleteAssignment } from "@/lib/db/actions/payment-plan-assignments";
import { addAthleteToTeam } from "@/lib/db/actions/athlete-teams";

export interface PaymentPlanAssignmentData {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

export async function persistPaymentPlanChanges(
  athleteId: string,
  athleteTeams: any[],
  originalPlans: PaymentPlanAssignmentData[],
  currentPlans: PaymentPlanAssignmentData[]
) {
  try {
    // Find new assignments (temp IDs)
    const newAssignments = currentPlans.filter(plan => 
      plan.id.startsWith('temp-') && plan.isActive
    );

    // Find deleted assignments (in original but not in current)
    const deletedIds: string[] = [];
    for (const originalPlan of originalPlans) {
      const currentPlan = currentPlans.find(p => p.id === originalPlan.id);
      if (!currentPlan) {
        deletedIds.push(originalPlan.id);
      }
    }

    // Find status changes (active/inactive) for existing assignments
    const statusChanges: Array<{ id: string; isActive: boolean }> = [];
    for (const originalPlan of originalPlans) {
      const currentPlan = currentPlans.find(p => p.id === originalPlan.id);
      if (currentPlan && originalPlan.isActive !== currentPlan.isActive) {
        statusChanges.push({ id: originalPlan.id, isActive: currentPlan.isActive });
      }
    }

    // Process new assignments
    for (const assignment of newAssignments) {
      // Check if athlete is in the team
      const isAthleteInTeam = athleteTeams.some(team => team.teamId === assignment.teamId);
      
      // Add athlete to team if not already there
      if (!isAthleteInTeam && assignment.teamId) {
        await addAthleteToTeam(athleteId, assignment.teamId);
      }

      // Assign the payment plan
      await assignPaymentPlan({
        athleteId,
        planId: assignment.planId,
        teamId: assignment.teamId || undefined,
      });
    }

    // Process deletions
    for (const assignmentId of deletedIds) {
      await deleteAssignment(assignmentId); // Actually delete the assignment
    }

    // Process status changes (activate/deactivate existing assignments)
    for (const change of statusChanges) {
      if (!change.isActive) {
        await deactivateAssignment(change.id);
      } else {
        await reactivateAssignment(change.id);
      }
    }

    revalidatePath(`/athletes/${athleteId}`);
    revalidatePath(`/athletes/${athleteId}/edit`);
    
    return { success: true };
  } catch (error) {
    console.error("Error persisting payment plan changes:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    };
  }
}
