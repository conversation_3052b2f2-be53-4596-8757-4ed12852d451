import React, { Suspense } from "react";
import { getExpensesPaginated } from "@/lib/actions/expenses";
import { ExpensesListPaginated } from "@/app/(protected)/expenses/expenses-list-paginated";

interface ExpensesPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    category?: string;
    fromDate?: string;
    toDate?: string;
    instructorId?: string;
    facilityId?: string;
  }>;
}

function ExpensesListSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
      ))}
    </div>
  );
}

async function ExpensesListServer({ searchParams }: ExpensesPageProps) {
  const resolvedSearchParams = await searchParams;
  
  // Get and parse search params with defaults
  const page = resolvedSearchParams.page || '1';
  const limit = resolvedSearchParams.limit || '10';
  const search = resolvedSearchParams.search || '';
  const sortBy = resolvedSearchParams.sortBy || 'date';
  const sortOrder = (resolvedSearchParams.sortOrder as 'asc' | 'desc') || 'desc';
  const category = resolvedSearchParams.category || '';
  const fromDate = resolvedSearchParams.fromDate || '';
  const toDate = resolvedSearchParams.toDate || '';
  const instructorId = resolvedSearchParams.instructorId || '';
  const facilityId = resolvedSearchParams.facilityId || '';

  // Fetch expenses with pagination and filters
  const result = await getExpensesPaginated({
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    category,
    fromDate,
    toDate,
    instructorId,
    facilityId,
  });

  console.log('Server page received result:', {
    dataLength: result.data.length,
    pagination: result.pagination
  });

  // Create a serialized version of the search params to avoid _debugInfo issues
  const serializedSearchParams = {
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    category,
    fromDate,
    toDate,
    instructorId,
    facilityId,
  };

  return (
    <ExpensesListPaginated 
      initialData={result.data} 
      initialPagination={result.pagination}
      initialSearchParams={serializedSearchParams}
    />
  );
}

export default function ExpensesPage({ searchParams }: ExpensesPageProps) {
  return (
    <Suspense fallback={<ExpensesListSkeleton />}>
      <ExpensesListServer searchParams={searchParams} />
    </Suspense>
  );
}