"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, ArrowLeft, RefreshCw } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export default function SellItemError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const { t } = useSafeTranslation();
  
  useEffect(() => {
    console.error("Sell item page error:", error);
  }, [error]);

  return (
    <div className="container mx-auto py-6">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 text-destructive">
              <AlertTriangle className="h-full w-full" />
            </div>
            <CardTitle className="text-xl">{t('items.errors.pageError')}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground">
              {t('items.errors.pageErrorDescription')}
            </p>
            
            {process.env.NODE_ENV === "development" && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm font-medium">
                  {t('items.errors.errorDetails')}
                </summary>
                <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                  {error.message}
                  {error.stack && `\n\n${error.stack}`}
                </pre>
              </details>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                onClick={reset}
                variant="default"
                className="inline-flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                {t('items.errors.tryAgain')}
              </Button>
              
              <Button 
                asChild
                variant="outline"
                className="inline-flex items-center gap-2"
              >
                <Link href="/items">
                  <ArrowLeft className="h-4 w-4" />
                  {t('items.errors.backToItems')}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
