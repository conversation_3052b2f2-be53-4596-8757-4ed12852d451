'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, MessageSquare, AlertTriangle, CheckCircle, Eye } from 'lucide-react';
import { sendPaymentReminderSms } from '@/lib/actions/sms';
import { Payment } from '@/lib/types';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import {useToast} from "@/hooks/use-toast";

const paymentReminderSchema = z.object({
  templateType: z.enum(['pending', 'overdue']),
  useCustomTemplate: z.boolean(),
  customTemplate: z.string().max(1000, 'Template must be 1000 characters or less').optional(),
});

type PaymentReminderFormData = z.infer<typeof paymentReminderSchema>;

interface PaymentReminderDialogProps {
  payments: Payment[];
  trigger?: React.ReactNode;
  onSuccess?: () => void;
  defaultTemplateType?: 'pending' | 'overdue';
}

export default function PaymentReminderDialog({
  payments,
  trigger,
  onSuccess,
  defaultTemplateType
}: PaymentReminderDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [previewTemplate, setPreviewTemplate] = useState(false);
  const [combinePayments, setCombinePayments] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<PaymentReminderFormData>({
    resolver: zodResolver(paymentReminderSchema),
    defaultValues: {
      templateType: 'pending',
      useCustomTemplate: false,
      customTemplate: '',
    }
  });

  const templateType = watch('templateType');
  const useCustomTemplate = watch('useCustomTemplate');
  const customTemplate = watch('customTemplate');

  // Determine template type based on payment statuses
  const hasOverduePayments = payments.some(p => p.status === 'overdue');
  const hasPendingPayments = payments.some(p => p.status === 'pending');

  // Auto-select template type based on defaultTemplateType or payment statuses
  useEffect(() => {
    if (defaultTemplateType) {
      setValue('templateType', defaultTemplateType);
    } else if (hasOverduePayments && !hasPendingPayments) {
      setValue('templateType', 'overdue');
    } else if (hasPendingPayments && !hasOverduePayments) {
      setValue('templateType', 'pending');
    }
  }, [defaultTemplateType, hasOverduePayments, hasPendingPayments, setValue]);

  const defaultTemplates = {
    pending: t('sms:configuration.templates.pending.default'),
    overdue: t('sms:configuration.templates.overdue.default')
  };

  // Calculate SMS count based on message length
  const calculateSmsCount = (length: number) => {
    if (length === 0) return 0;
    if (length <= 160) return 1;
    if (length <= 306) return 2;
    if (length <= 459) return 3;
    if (length <= 612) return 4;
    return Math.ceil(length / 153); // For messages longer than 612 chars
  };

  const currentTemplate = useCustomTemplate ? customTemplate : defaultTemplates[templateType];
  const templateLength = currentTemplate?.length || 0;
  const smsCount = calculateSmsCount(templateLength);

  const processTemplatePreview = (template: string) => {
    // Use real data from the first payment if available
    const firstPayment = payments[0];
    const athlete = firstPayment?.athlete;

    // Calculate combined amount if combining payments
    let totalAmount;
    if (combinePayments) {
      // When combining, sum all payments for the same athlete
      const firstAthleteId = firstPayment?.athleteId;
      const athletePayments = payments.filter(p => p.athleteId === firstAthleteId);
      totalAmount = athletePayments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    } else {
      totalAmount = parseFloat(firstPayment?.amount || '0');
    }

    // Format amount with TL suffix for SMS compatibility
    const formattedAmount = `${totalAmount.toFixed(2)} TL`;

    let processedTemplate = template
      .replace(/\{\{athleteName\}\}/g, athlete?.name || t('sms:configuration.preview.athleteName'))
      .replace(/\{\{parentName\}\}/g, athlete?.parentName || t('sms:configuration.preview.parentName'))
      .replace(/\{\{amount\}\}/g, formattedAmount)
      .replace(/\{\{teamName\}\}/g, firstPayment?.team?.name || t('sms:configuration.preview.teamName'))
      .replace(/\{\{schoolName\}\}/g, firstPayment?.school?.name || t('sms:configuration.preview.schoolName'));

    // Only add due date if not combining payments (since combined payments may have different due dates)
    if (!combinePayments && firstPayment?.dueDate) {
      const formattedDate = new Date(firstPayment.dueDate).toLocaleDateString();
      processedTemplate = processedTemplate.replace(/\{\{paymentDueDate\}\}/g, formattedDate);
    } else {
      // Remove due date variable if combining payments
      processedTemplate = processedTemplate.replace(/\{\{paymentDueDate\}\}/g, t('sms:sending.paymentReminder.dueDateNotAvailableWhenCombining'));
    }

    return processedTemplate;
  };

  const onSubmit = (data: PaymentReminderFormData) => {
    startTransition(async () => {
      try {
        const paymentIds = payments.map(p => p.id);
        
        const result = await sendPaymentReminderSms({
          paymentIds,
          templateType: data.templateType,
          customTemplate: data.useCustomTemplate ? data.customTemplate : undefined,
          combinePayments,
        });

        if (result.success) {
          const sentCount = result.data?.sentCount || 0;
          toast({
            title: t('common.success'),
            description:sentCount === 1
                ? t('sms:sending.paymentReminder.successSingle')
                : t('sms:sending.paymentReminder.successPlural').replace(/{count}/g, sentCount.toString()),
          });
          setOpen(false);
          reset();
          onSuccess?.();
        }else{
          console.error('Failed to send payment reminder SMS:', result.error);
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'sms:sending.paymentReminder.error';
          }
          toast({
            title: t('common.error'),
            description:t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error sending payment reminder SMS:', error);
        toast({
          title: t('common.error'),
          description:t('sms:sending.paymentReminder.error'),
          variant: "destructive",
        });
      }
    });
  };

  const handleClose = () => {
    setOpen(false);
    reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            Send SMS Reminder
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] h-auto flex flex-col overflow-hidden">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{t('sms:sending.paymentReminder.title')}</DialogTitle>
          <DialogDescription>
            {payments.length === 1
              ? t('sms:sending.paymentReminder.descriptionSingle')
              : t('sms:sending.paymentReminder.descriptionPlural').replace(/{count}/g, payments.length.toString())
            }
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto overflow-x-hidden pr-2 -mr-2">
          <form id="payment-reminder-form" onSubmit={handleSubmit(onSubmit)} className="space-y-6 pr-2">
          {/* Payment Summary */}
          <div className="space-y-2">
            <Label>{t('sms:sending.paymentReminder.selectedPayments')}</Label>
            <div className="max-h-32 overflow-y-auto border rounded-md p-3 space-y-2">
              {payments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between text-sm">
                  <span>{payment.athlete?.name} {payment.athlete?.surname}</span>
                  <div className="flex items-center space-x-2">
                    <span>{payment.amount}</span>
                    <Badge variant={payment.status === 'overdue' ? 'destructive' : 'secondary'}>
                      {t(`sms:status.${payment.status}`)}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Template Type Selection */}
          <div className="space-y-3">
            <Label>{t('sms:sending.paymentReminder.templateType')}</Label>
            <div className="flex space-x-4">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="pending"
                  value="pending"
                  {...register('templateType')}
                  disabled={!hasPendingPayments}
                />
                <Label htmlFor="pending" className={!hasPendingPayments ? 'text-muted-foreground' : ''}>
                  {t('sms:sending.paymentReminder.pending')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="overdue"
                  value="overdue"
                  {...register('templateType')}
                  disabled={!hasOverduePayments}
                />
                <Label htmlFor="overdue" className={!hasOverduePayments ? 'text-muted-foreground' : ''}>
                  {t('sms:sending.paymentReminder.overdue')}
                </Label>
              </div>
            </div>
          </div>

          {/* Custom Template Option */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="useCustomTemplate"
              checked={useCustomTemplate}
              onCheckedChange={(checked) => setValue('useCustomTemplate', !!checked)}
            />
            <Label htmlFor="useCustomTemplate">{t('sms:sending.paymentReminder.customTemplate')}</Label>
          </div>

          {/* Combine Payments Option */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="combinePayments"
              checked={combinePayments}
              onCheckedChange={(checked) => setCombinePayments(!!checked)}
            />
            <Label htmlFor="combinePayments">{t('sms:sending.paymentReminder.combinePayments')}</Label>
          </div>
          {combinePayments && (
            <div className="text-sm text-muted-foreground pl-6">
              {t('sms:sending.paymentReminder.combinePaymentsDescription')}
            </div>
          )}

          {/* Template Preview/Edit */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>{t('sms:sending.paymentReminder.messageTemplate')}</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setPreviewTemplate(!previewTemplate)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {previewTemplate ? t('sms:sending.paymentReminder.hidePreview') : t('sms:sending.paymentReminder.preview')}
              </Button>
            </div>

            {useCustomTemplate ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="text-muted-foreground">
                      {templateLength} {t('sms:common.characters')}
                    </span>
                    <span className={`font-medium ${smsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                      ~{smsCount} SMS {t('sms:common.approximately')}
                    </span>
                  </div>
                </div>
                <Textarea
                  {...register('customTemplate')}
                  placeholder={t('sms:sending.paymentReminder.customPlaceholder')}
                  rows={4}
                  className={errors.customTemplate ? 'border-red-500' : ''}
                />
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="text-muted-foreground">
                      {templateLength} {t('sms:common.characters')}
                    </span>
                    <span className={`font-medium ${smsCount > 1 ? 'text-orange-600' : 'text-green-600'}`}>
                      ~{smsCount} SMS {t('sms:common.approximately')}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-muted rounded-md">
                  <p className="text-sm">{defaultTemplates[templateType]}</p>
                </div>
              </div>
            )}

            {errors.customTemplate && (
              <p className="text-sm text-red-500">{errors.customTemplate.message}</p>
            )}

            {previewTemplate && (
              <Alert>
                <AlertDescription>
                  <strong>Preview:</strong> {processTemplatePreview(
                    useCustomTemplate ? customTemplate || '' : defaultTemplates[templateType]
                  )}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Available Variables */}
          <div className="space-y-2">
            <Label>{t('sms:sending.paymentReminder.availableVariables')}</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
              <div className="flex items-center space-x-2">
                <code className="bg-muted px-1 rounded">{'{{athleteName}}'}</code>
                <span className="text-muted-foreground">- {t('sms:configuration.variables.athleteName')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-muted px-1 rounded">{'{{amount}}'}</code>
                <span className="text-muted-foreground">- {t('sms:configuration.variables.amount')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-muted px-1 rounded">{'{{teamName}}'}</code>
                <span className="text-muted-foreground">- {t('sms:configuration.variables.teamName')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-muted px-1 rounded">{'{{schoolName}}'}</code>
                <span className="text-muted-foreground">- {t('sms:configuration.variables.schoolName')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-muted px-1 rounded">{'{{paymentDueDate}}'}</code>
                <span className="text-muted-foreground">- {t('sms:configuration.variables.paymentDueDate')}</span>
              </div>
            </div>
          </div>

          {/* Warning */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {(() => {
                // Calculate recipients based on combine payments setting
                let recipientCount;
                let totalSmsCredits;

                if (combinePayments) {
                  // When combining payments, count unique athletes
                  const uniqueAthletes = new Set(payments.map(p => p.athleteId));
                  recipientCount = uniqueAthletes.size;
                  totalSmsCredits = recipientCount * smsCount;
                } else {
                  // When not combining, each payment gets its own SMS
                  recipientCount = Math.max(1, payments.length || 1);
                  totalSmsCredits = recipientCount * smsCount;
                }

                try {
                  return (
                    <div className="space-y-1">
                      <p>
                        {recipientCount === 1
                          ? t('sms:sending.paymentReminder.warningRecipientSingle')
                          : t('sms:sending.paymentReminder.warningRecipientPlural').replace(/{count}/g, recipientCount.toString())
                        }
                      </p>
                      <p className="text-sm">
                        {smsCount === 1
                          ? t('sms:sending.paymentReminder.warningSmsCountSingle').replace(/{total}/g, totalSmsCredits.toString())
                          : t('sms:sending.paymentReminder.warningSmsCountPlural')
                              .replace(/{smsPerRecipient}/g, smsCount.toString())
                              .replace(/{total}/g, totalSmsCredits.toString())
                        }
                      </p>
                      {combinePayments && (
                        <p className="text-xs text-blue-600">
                          {t('sms:sending.paymentReminder.combiningPaymentsNote')}
                        </p>
                      )}
                      {smsCount > 1 && (
                        <p className="text-xs text-orange-600">
                          {t('sms:sending.paymentReminder.warningLongMessage')}
                        </p>
                      )}
                    </div>
                  );
                } catch (error) {
                  // Fallback message if translation fails
                  console.warn('SMS translation error:', error);
                  return `This will send SMS messages to ${recipientCount} recipients and will consume ${totalSmsCredits} SMS credits from your balance.`;
                }
              })()}
            </AlertDescription>
          </Alert>

          </form>
        </div>

        <DialogFooter className="flex-shrink-0 mt-4 pt-4 border-t">
          <Button type="button" variant="outline" onClick={handleClose} disabled={isPending}>
            {t('sms:common.cancel')}
          </Button>
          <Button type="submit" form="payment-reminder-form" disabled={isPending}>
            {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <MessageSquare className="mr-2 h-4 w-4" />
            {t('sms:sending.paymentReminder.send')} ({(() => {
              if (combinePayments) {
                const uniqueAthletes = new Set(payments.map(p => p.athleteId));
                return uniqueAthletes.size;
              } else {
                return payments.length;
              }
            })()})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
