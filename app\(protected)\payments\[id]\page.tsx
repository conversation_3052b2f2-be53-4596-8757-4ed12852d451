import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPaymentById } from "@/lib/actions";
import PaymentDetailClient from "./payment-detail-client";

export async function generateMetadata({ params }: {
  params: Promise<{ id: string }>
}): Promise<Metadata> {
  const resolvedParams = await params;
  
  try {
    const payment = await getPaymentById(resolvedParams.id);
    
    if (!payment) {
      return {
        title: "Payment Not Found",
        description: "The requested payment could not be found.",
      };
    }

    const athleteName = payment.athlete ? `${payment.athlete.name} ${payment.athlete.surname}` : 'Unknown';
    
    return {
      title: `Payment Details - ${athleteName}`,
      description: `Payment details for ${athleteName}. Amount: ${payment.amount}, Status: ${payment.status}`,
    };
  } catch (error) {
    return {
      title: "Payment Details",
      description: "View payment details and transaction information.",
    };
  }
}

interface Props {
  params: Promise<{ id: string }>;
}

export default async function PaymentDetailPage({ params }: Props) {
  const resolvedParams = await params;
  
  try {
    const payment = await getPaymentById(resolvedParams.id);
    
    if (!payment) {
      notFound();
    }

    return <PaymentDetailClient payment={payment} />;
  } catch (error) {
    console.error("Failed to load payment details:", error);
    notFound();
  }
}