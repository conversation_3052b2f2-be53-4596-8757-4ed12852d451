"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Calendar } from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

// Import components and hooks
import { PaymentBasicForm } from "./PaymentBasicForm";
import { AthleteInfoCard } from "./AthleteInfoCard";
import { PaymentFormActions } from "./PaymentFormActions";
import { usePaymentForm } from "@/hooks/payments/usePaymentForm";
import { usePaymentOperations } from "@/hooks/payments/usePaymentOperations";

interface Athlete {
  id: string;
  name: string;
  surname: string;
  parentEmail?: string | null;
  parentPhone?: string | null;
  nationalId?: string | null;
}

interface Payment {
  id: string;
  athleteId: string;
  athletePaymentPlanId?: string | null;
  amount: string;
  date: string;
  dueDate: string;
  status: "pending" | "completed" | "overdue" | "cancelled";
  type: "fee" | "equipment" | "other";
  method: "cash" | "bank_transfer" | "credit_card" | null;
  description?: string | null;
  athlete: Athlete | null;
}

interface EditPaymentClientProps {
  payment: Payment;
  athletes: Athlete[];
}

export default function EditPaymentClient({ 
  payment, 
  athletes 
}: EditPaymentClientProps) {
  const { t } = useSafeTranslation();

  // Custom hooks
  const {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
  } = usePaymentForm({ initialPayment: payment });

  const {
    isSaving,
    savePayment,
    navigateBack,
    navigateToList,
  } = usePaymentOperations({ paymentId: payment.id });

  // Find selected athlete
  const selectedAthlete = athletes.find(a => a.id === formData.athleteId) || payment.athlete;

  // Handlers
  const handleSave = async () => {
    if (validateForm()) {
      const success = await savePayment(formData);
      if (success) {
        navigateBack();
      }
    }
  };

  const handleCancel = () => {
    resetForm();
    navigateBack();
  };

  const isFormValid = formData.athleteId && formData.amount && formData.date && formData.dueDate;

  return (
    <div className="space-y-6">
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TurkishLiraIcon className="h-5 w-5" />
                {t("payments.edit")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PaymentBasicForm
                formData={formData}
                errors={errors}
                athletes={athletes}
                onUpdateField={updateField}
              />
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <AthleteInfoCard athlete={selectedAthlete} />
        </div>
      </div>

      {/* Actions */}
      <PaymentFormActions
        onSave={handleSave}
        onCancel={handleCancel}
        onGoBack={navigateBack}
        isSaving={isSaving}
        isValid={!!isFormValid}
      />
    </div>
  );
}
