import { Suspense } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { InstructorListPaginated } from "./instructor-list-paginated";
import { getInstructorsPaginated } from "@/lib/actions/instructors";

function InstructorListSkeleton() {
  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-4 w-64 mt-2" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-64 mt-2" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface InstructorsPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    name?: string;
    surname?: string;
    email?: string;
    phone?: string;
  }>;
}

async function InstructorListServer({ searchParams }: InstructorsPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = parseInt(resolvedSearchParams.limit || '10');
  const search = resolvedSearchParams.search;
  const sortBy = resolvedSearchParams.sortBy || 'createdAt';
  const sortOrder = resolvedSearchParams.sortOrder || 'desc';
  
  const filters: Record<string, string> = {};
  if (resolvedSearchParams.name) filters.name = resolvedSearchParams.name;
  if (resolvedSearchParams.surname) filters.surname = resolvedSearchParams.surname;
  if (resolvedSearchParams.email) filters.email = resolvedSearchParams.email;
  if (resolvedSearchParams.phone) filters.phone = resolvedSearchParams.phone;

  const options = {
    page,
    limit,
    search: search && search.length >= 3 ? search : undefined,
    sortBy,
    sortOrder,
    filters: Object.keys(filters).length > 0 ? filters : undefined,
  };

  const result = await getInstructorsPaginated(options);

  return (
    <InstructorListPaginated 
      initialData={result.data || []}
      initialPagination={result.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      }}
      initialSearchParams={resolvedSearchParams}
    />
  );
}

export default function InstructorsPage({ searchParams }: InstructorsPageProps) {
  return (
    <Suspense fallback={<InstructorListSkeleton />}>
      <InstructorListServer searchParams={searchParams} />
    </Suspense>
  );
}