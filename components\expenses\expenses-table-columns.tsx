"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { MoreH<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash, Eye } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { deleteExpense } from "@/lib/actions/expenses";
import Link from "next/link";
import { format } from "date-fns";
import { Expense } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import {useToast} from "@/hooks/use-toast";

const ExpenseActionsCell = ({ expense }: { expense: Expense }) => {
    const { t } = useSafeTranslation();
    const { toast } = useToast();
    const router = useRouter();
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
        setIsDeleting(true);
        try {
            await deleteExpense(expense.id);
            toast({
                title: t('common.success'),
                description: t('expenses.messages.deleteSuccess')
             });
            router.refresh();
        } catch (error) {
            console.error('Error deleting expense:', error);
            toast({
                title: t('common.error'),
                description: t('expenses.messages.deleteError'),
                variant: "destructive",
              });
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">{t("expenses.actions.openMenu")}</span>
                    <MoreHorizontal className="h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
                <DropdownMenuLabel>{t("common.actionsHeader")}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                    <Link href={`/expenses/${expense.id}`} className="flex items-center">
                        <Eye className="mr-2 h-4 w-4" />
                        {t("expenses.actions.view")}
                    </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                    <Link href={`/expenses/${expense.id}/edit`} className="flex items-center">
                        <Pencil className="mr-2 h-4 w-4" />
                        {t("expenses.actions.edit")}
                    </Link>
                </DropdownMenuItem>
                <AlertDialog>
                    <AlertDialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                            <Trash className="mr-2 h-4 w-4" />
                            {t("expenses.actions.delete")}
                        </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>{t("expenses.messages.confirmDelete")}</AlertDialogTitle>
                            <AlertDialogDescription>
                                {t("expenses.messages.deleteWarning")}
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel>{t("common.actions.cancel")}</AlertDialogCancel>
                            <AlertDialogAction
                                onClick={handleDelete}
                                disabled={isDeleting}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                                {isDeleting ? t("common.actions.deleting") : t("common.actions.delete")}
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export const useExpenseColumns = () => {
    const { t } = useSafeTranslation();

    return [
        {
            accessorKey: "date",
            header: t("expenses.details.date"),
            cell: ({ row }: { row: { original: Expense } }) => format(new Date(row.original.date), "dd/MM/yyyy"),
        },
        {
            accessorKey: "amount",
            header: t("expenses.details.amount"),
            cell: ({ row }: { row: { original: Expense } }) => (
                <div className="font-medium">{parseFloat(row.original.amount).toFixed(2)} {t('common.currency')}</div>
            ),
        },
        {
            accessorKey: "category",
            header: t("expenses.details.category"),
            cell: ({ row }: { row: { original: Expense } }) => (
                <div className="capitalize">{t(`expenses.categories.${row.original.category}`)}</div>
            ),
        },
        {
            accessorKey: "description",
            header: t("expenses.details.description"),
        },
        {
            accessorKey: "instructor",
            header: t("expenses.details.instructor"),
            cell: ({ row }: { row: { original: Expense } }) => (
                <div className="text-sm">
                    {row.original.instructor ? `${row.original.instructor.name} ${row.original.instructor.surname}` : "-"}
                </div>
            ),
        },
        {
            accessorKey: "facility",
            header: t("expenses.details.facility"),
            cell: ({ row }: { row: { original: Expense } }) => (
                <div className="text-sm">
                    {row.original.facility ? row.original.facility.name : "-"}
                </div>
            ),
        },
        {
            id: "actions",
            cell: ({ row }: { row: { original: Expense } }) => (
                <ExpenseActionsCell expense={row.original} />
            ),
        },
    ];
};
