"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { getSchoolById } from "@/lib/db/actions"
import { SecureImage } from "@/components/ui/secure-image"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowLeft, Pencil, School as SchoolIcon } from "lucide-react"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { School } from "@/lib/types"
import {useSafeTranslation} from "@/hooks/use-safe-translation";

export default function SchoolPage() {
  const { t } = useSafeTranslation();
  const params = useParams();
  const [school, setSchool] = useState<School | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSchool = async () => {
      try {
        const schoolData = await getSchoolById(params.id as string);
        setSchool(schoolData || null);
      } catch (error) {
        console.error("Error fetching school:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSchool();
  }, [params.id]);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-10 bg-muted rounded-md w-48" />
          <div className="grid gap-6 md:grid-cols-3">
            <div className="h-64 bg-muted rounded-lg" />
            <div className="md:col-span-2 h-64 bg-muted rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  if (!school) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold">{t('noData', { ns: 'shared' })}</h1>
          <p className="text-muted-foreground mt-2">{t('error', { ns: 'shared' })}</p>
          <Button asChild className="mt-4">
            <Link href="/schools">{t('schools.actions.backToSchools')}</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <Button variant="ghost" asChild>
          <Link href="/schools" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            {t('schools.actions.backToSchools')}
          </Link>
        </Button>
        <Button asChild>
          <Link href={`/schools/${school.id}/edit`} className="flex items-center gap-2">
            <Pencil className="h-4 w-4" />
            {t('schools.edit')}
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>{t('schools.details.logo')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-square relative rounded-lg overflow-hidden">
              <SecureImage
                src={school.logo || ""}
                alt={school.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                placeholderIcon={SchoolIcon}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>{school.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="font-medium mb-2">{t('schools.details.information')}</h3>
                <Separator className="my-2" />
                <div className="grid gap-2">
                  <div>
                    <span className="font-medium">{t('schools.details.foundedYear')}:</span> {school.foundedYear}
                  </div>
                  <div>
                    <span className="font-medium">{t('schools.details.instructors')}:</span> {school.instructors.length}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">{t('schools.details.branches')}</h3>
                <Separator className="my-2" />
                <div className="flex flex-wrap gap-2">
                  {school.branches.map((branch) => (
                    <span
                      key={branch.id}
                      className="bg-secondary text-secondary-foreground text-sm rounded-full px-3 py-1"
                    >
                      {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}