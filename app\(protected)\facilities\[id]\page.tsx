import { notFound } from "next/navigation";
import { getFacilityById, getFacilitySchedules } from "@/lib/actions/facilities";
import FacilityDetailClient from "./facility-detail-client";

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function FacilityDetailPage({ params }: PageProps) {
  try {
    const { id } = await params;
    const [facility, schedules] = await Promise.all([
      getFacilityById(id),
      getFacilitySchedules(id)
    ]);

    if (!facility) {
      notFound();
    }

    return <FacilityDetailClient facility={facility} schedules={schedules || []} />;
  } catch (error) {
    console.error('Error fetching facility:', error);
    notFound();
  }
}
