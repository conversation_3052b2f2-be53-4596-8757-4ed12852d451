"use client";

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { MultiSelect, Option } from "@/components/ui/multi-select";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createInstructor } from "@/lib/actions";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import {useToast} from "@/hooks/use-toast";

interface Branch {
  id: string;
  name: string;
  description?: string | null;
}

interface School {
  id: string;
  name: string;
  foundedYear: number;
}

interface NewInstructorClientProps {
  branches: Branch[];
  schools: School[];
}

export default function NewInstructorClient({ branches, schools }: NewInstructorClientProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);
  const [selectedSchools, setSelectedSchools] = useState<string[]>([]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const formData = new FormData(e.currentTarget);
      const salaryValue = formData.get("salary") as string;
      const instructorData = {
        name: formData.get("name") as string,
        surname: formData.get("surname") as string,
        email: formData.get("email") as string,
        phone: formData.get("phone") as string,
        nationalId: formData.get("nationalId") as string,
        birthDate: formData.get("birthDate") as string,
        address: formData.get("address") as string,
        salary: salaryValue ? parseFloat(salaryValue) : undefined,
        branchIds: selectedBranches,
        schoolIds: selectedSchools,
      };

      const result = await createInstructor(instructorData);
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('instructors.messages.createSuccess'),
        });
        router.push("/instructors");
        router.refresh();
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'instructors.messages.createError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to create instructor:', error);
      toast({
        title: t('common.error'),
        description: t('instructors.messages.createError'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const branchOptions: Option[] = branches.map(branch => ({
    value: branch.id,
    label: t(`common.branches.${branch.name}`, { ns: 'shared' }),
  }));

  const schoolOptions: Option[] = schools.map(school => ({
    value: school.id,
    label: school.name,
  }));

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="mb-6">
        <Link href="/instructors">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.actions.back')}
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">{t('instructors.actions.add')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('instructors.form.title')}</CardTitle>
          <CardDescription>{t('instructors.form.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">{t('instructors.form.name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  required
                  placeholder={t('instructors.form.namePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="surname">{t('instructors.form.surname')} *</Label>
                <Input
                  id="surname"
                  name="surname"
                  type="text"
                  required
                  placeholder={t('instructors.form.surnamePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{t('instructors.form.email')} *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  required
                  placeholder={t('instructors.form.emailPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('instructors.form.phone')}</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder={t('instructors.form.phonePlaceholder')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="nationalId">{t('instructors.form.nationalId')}</Label>
                <Input
                  id="nationalId"
                  name="nationalId"
                  type="text"
                  placeholder={t('instructors.form.nationalIdPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="birthDate">{t('instructors.form.birthDate')}</Label>
                <Input
                  id="birthDate"
                  name="birthDate"
                  type="date"
                  placeholder={t('instructors.form.birthDatePlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">{t('instructors.form.address')}</Label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder={t('instructors.form.addressPlaceholder')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="salary">{t('instructors.form.salary')}</Label>
                <Input
                  id="salary"
                  name="salary"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder={t('instructors.form.salaryPlaceholder')}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>{t('instructors.form.branches')}</Label>
                <MultiSelect
                  options={branchOptions}
                  selected={selectedBranches}
                  onChange={setSelectedBranches}
                  placeholder={t('instructors.form.selectBranches')}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('instructors.form.schools')}</Label>
                <MultiSelect
                  options={schoolOptions}
                  selected={selectedSchools}
                  onChange={setSelectedSchools}
                  placeholder={t('instructors.form.selectSchools')}
                />
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Link href="/instructors">
                <Button variant="outline" type="button">
                  {t('common.actions.cancel')}
                </Button>
              </Link>
              <Button type="submit" disabled={loading}>
                {loading ? t('common.loading') : t('instructors.actions.create')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
