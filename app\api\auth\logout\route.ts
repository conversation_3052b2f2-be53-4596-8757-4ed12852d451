import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';

/**
 * Secure server-side logout endpoint
 * This endpoint will:
 * 1. Verify the user is authenticated
 * 2. Validate the request origin for security
 * 3. Construct the Zitadel logout URL with proper parameters
 * 4. Clear server-side session and tokens securely
 * 5. Return the logout URL for client-side redirect
 */
export async function POST(request: NextRequest) {
  try {
    // Security: Validate request origin
    const origin = request.headers.get('origin');
    const allowedOrigins = [
      process.env.NEXTAUTH_URL,
      process.env.NEXT_PUBLIC_APP_URL,
      'http://localhost:3000', // For development
      'https://localhost:3000'  // For development with HTTPS
    ].filter(Boolean);

    if (origin && !allowedOrigins.includes(origin)) {
      console.warn(`Logout request from unauthorized origin: ${origin}`);
      return NextResponse.json(
        { error: 'Unauthorized origin' },
        { status: 403 }
      );
    }

    // Get the current server session to verify authentication
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Log the logout attempt for security auditing
    console.info(`User logout initiated for session: ${session.user?.email || 'unknown'}`);

    // Construct Zitadel end session URL
    const zitadelIssuer = process.env.ZITADEL_ISSUER;
    if (!zitadelIssuer) {
      console.error('ZITADEL_ISSUER environment variable is not set');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    const zitadelEndSessionUrl = new URL(`${zitadelIssuer}/oidc/v1/end_session`);
    
    // Add id_token_hint if available (recommended for proper OIDC logout)
    if (session.idToken) {
      zitadelEndSessionUrl.searchParams.append('id_token_hint', session.idToken);
    }
    
    // Set post logout redirect URI to our sign-in page
    const baseUrl = origin || process.env.NEXTAUTH_URL;
    const postLogoutRedirectUri = `${baseUrl}/auth/signin`;
    zitadelEndSessionUrl.searchParams.append('post_logout_redirect_uri', postLogoutRedirectUri);

    // Create response with logout URL
    const response = NextResponse.json({
      logoutUrl: zitadelEndSessionUrl.toString(),
      success: true
    });

    // Clear authentication cookies
    const sessionCookieName = process.env.NODE_ENV === 'production'
      ? "__Secure-next-auth.session-token"
      : "next-auth.session-token";
      
    response.cookies.set(sessionCookieName, '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });

    response.cookies.set('next-auth.csrf-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });

    // Clear custom token cookies (matching the names from tokens API)
    response.cookies.set('access-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });

    response.cookies.set('id-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });

    response.cookies.set('refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 0,
      path: '/'
    });

    // Add security headers
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    console.info(`User logout completed successfully for: ${session.user?.email || 'unknown'}`);
    
    return response;

  } catch (error) {
    console.error('Error during server-side logout:', error);
    return NextResponse.json(
      { error: 'Internal server error during logout' },
      { status: 500 }
    );
  }
}

// Only allow POST requests for security
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for logout.' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for logout.' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed. Use POST for logout.' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}
