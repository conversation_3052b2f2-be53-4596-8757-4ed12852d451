{"items": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "addItem": "<PERSON><PERSON><PERSON><PERSON>", "details": {"title": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "stock": "Stok", "category": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON><PERSON>", "information": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"createItem": "<PERSON><PERSON><PERSON><PERSON>", "creating": "Oluşturuluyor...", "editItem": "<PERSON><PERSON><PERSON><PERSON>", "deleteItem": "<PERSON><PERSON><PERSON><PERSON>", "sellItem": "Sat", "viewDetails": "Detayları Görüntüle", "changeImage": "<PERSON><PERSON><PERSON>", "openMenu": "Menüyü a<PERSON>", "addNew": "<PERSON><PERSON> bir <PERSON><PERSON><PERSON><PERSON> ekle"}, "messages": {"createSuccess": "<PERSON><PERSON>ün başarıyla oluşturuldu", "createError": "<PERSON><PERSON><PERSON><PERSON> olu<PERSON>ulurken hata oluştu", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> başar<PERSON><PERSON> gü<PERSON>llendi", "updateError": "<PERSON><PERSON><PERSON><PERSON> gü<PERSON>en hata oluş<PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>en hata o<PERSON>", "deleteConfirm": "<PERSON>u ürünü silmek istediğinizden emin misiniz?", "hasBeenDeleted": "<PERSON><PERSON>i"}, "errors": {"requiredFields": "Lütfen tüm gerekli alanları doldurun", "pageError": "<PERSON>ir hata o<PERSON>", "pageErrorDescription": "Ürün satış sayfası yüklenirken bir hata oluştu. Bu bir ağ sorunu olabilir veya ürün mevcut olmayabilir.", "errorDetails": "Hata Detayları (Geliştirme)", "tryAgain": "<PERSON><PERSON><PERSON> dene", "backToItems": "Ürü<PERSON>lere Geri <PERSON>ö<PERSON>", "pageNotFound": "Sayfa Bulunamadı", "pageNotFoundDescription": "Aradığınız sayfa mevcut değil veya taşınmış."}, "placeholders": {"enterName": "<PERSON><PERSON><PERSON><PERSON> adını girin", "enterDescription": "<PERSON>rün açıklamasını girin", "enterPrice": "0,00", "name": "<PERSON><PERSON><PERSON><PERSON> adını girin", "description": "<PERSON>rün açıklamasını girin", "category": "<PERSON><PERSON><PERSON>", "enterStock": "Stok miktarını girin", "uploadImage": "Yüklemek için tıklayın veya sürükleyip bırakın", "imageFormats": "PNG, JPG veya WEBP (MAKS. 5MB)", "chooseAthlete": "Bir sporcu seçin"}, "categories": {"equipment": "<PERSON><PERSON><PERSON><PERSON>", "clothing": "G<PERSON><PERSON><PERSON>", "accessories": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "status": {"inStock": "Stok<PERSON>", "lowStock": "Az Stok", "outOfStock": "Stok Yok"}, "sell": {"title": "<PERSON><PERSON><PERSON><PERSON>", "itemDetails": "<PERSON><PERSON><PERSON><PERSON>", "saleInformation": "Satış Bilgileri", "selectAthlete": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>", "unitPrice": "<PERSON><PERSON><PERSON>", "total": "Toplam", "processing": "İşleniyor...", "completeSale": "Satışı Tamamla", "availableStock": "Mevcut Stok", "paymentStatus": "<PERSON><PERSON><PERSON>", "selectPaymentStatus": "<PERSON><PERSON><PERSON> du<PERSON>", "billingDate": "<PERSON><PERSON>", "dueDate": "<PERSON> <PERSON><PERSON>", "selectBillingDate": "<PERSON><PERSON> ta<PERSON>", "selectDueDate": "Son ödeme ta<PERSON>", "errors": {"invalidData": "Lütfen tüm gerekli alanları doldurun", "saleError": "Satış işlenirken hata oluştu"}, "messages": {"saleCompleted": "Satış başarıyla tamamlandı"}}, "paymentStatus": {"pending": "Beklemede", "completed": "Tamamlandı", "overdue": "Gecikmiş"}}}