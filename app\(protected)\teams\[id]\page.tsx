import { Suspense } from "react";
import { getTeamById, getAthletes } from "@/lib/actions";
import { getPaymentPlans } from "@/lib/actions/payment-plans";
import TeamDetailClient from "./team-detail-client";
import { TeamErrorClient } from "./team-error-client";

interface TeamPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function TeamDataWrapper({ teamId }: { teamId: string }) {
  try {
    const [team, allAthletes, paymentPlans] = await Promise.all([
      getTeamById(teamId),
      getAthletes(),
      getPaymentPlans()
    ]);

    // Filter athletes that belong to this team
    const teamAthletes = allAthletes?.filter(athlete => 
      athlete.teams?.includes(teamId)
    ) || [];

    return (
      <TeamDetailClient 
        team={team} 
        athletes={teamAthletes}
        availablePaymentPlans={paymentPlans}
      />
    );
  } catch (error) {
    console.error("Error fetching team data:", error);
    return (
      <div className="text-center py-8">
        <TeamErrorClient />
      </div>
    );
  }
}

function TeamDetailSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="h-9 w-32 bg-muted animate-pulse rounded-md" />
        <div className="h-9 w-24 bg-muted animate-pulse rounded-md" />
      </div>
      
      <div className="h-32 bg-muted animate-pulse rounded-lg" />
      
      <div className="grid gap-6 md:grid-cols-2">
        <div className="h-48 bg-muted animate-pulse rounded-lg" />
        <div className="h-48 bg-muted animate-pulse rounded-lg" />
      </div>
      
      <div className="h-64 bg-muted animate-pulse rounded-lg" />
    </div>
  );
}

export default async function TeamPage({ params }: TeamPageProps) {
  const { id } = await params;
  
  return (
    <Suspense fallback={<TeamDetailSkeleton />}>
      <TeamDataWrapper teamId={id} />
    </Suspense>
  );
}