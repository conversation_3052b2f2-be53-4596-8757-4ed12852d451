{"payments": {"title": "Ö<PERSON>mel<PERSON>", "add": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "recordPayment": "<PERSON><PERSON><PERSON>", "selectType": "<PERSON><PERSON><PERSON> tü<PERSON>", "selectStatus": "<PERSON><PERSON><PERSON> du<PERSON>", "newPaymentPlan": "<PERSON>ni Ödeme Planı", "tabPayments": "Ö<PERSON>mel<PERSON>", "tabPlans": "Ödeme <PERSON>", "createPlan": "Yeni bir ödeme planı oluştur", "search_placeholder": "Ödemeleri ara...", "athlete_name": "Sporcu Adı", "athlete_surname": "Sporcu Soyadı", "athlete_name_placeholder": "<PERSON><PERSON><PERSON> adına göre ara", "athlete_surname_placeholder": "Sporcu soyadına göre ara", "all_statuses": "<PERSON><PERSON><PERSON>", "all_types": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "amount_placeholder": "<PERSON><PERSON> göre ara", "date": "<PERSON><PERSON><PERSON>", "status_pending": "Beklemede", "status_completed": "Tamamlandı", "status_overdue": "Gecikmiş", "status_cancelled": "İptal Edildi", "type_fee": "Ücret", "type_equipment": "<PERSON><PERSON><PERSON><PERSON>", "type_other": "<PERSON><PERSON><PERSON>", "details": {"title": "Ö<PERSON>me <PERSON>", "paymentInfo": "Ödeme Bilgileri", "amount": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "dueDate": "<PERSON> <PERSON><PERSON>", "status": "Durum", "type": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "originalAmount": "Orijinal Tutar", "originalDate": "Orijinal Tarih", "originalStatus": "Orijinal Durum", "athlete": "<PERSON><PERSON><PERSON>", "frequency": "Sıklık"}, "dates": {"notSet": "Belirlenmedi", "invalidDate": "Geçersiz tarih", "due": "Son"}, "status": {"completed": "Tamamlandı", "pending": "Beklemede", "overdue": "Gecikmiş", "cancelled": "İptal Edildi"}, "types": {"fee": "Ücret", "equipment": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "installment": "Taks<PERSON>", "frequency": {"monthly": "Aylık", "quarterly": "Üç Aylık", "annually": "Yıllık"}, "actions": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "process": "Ödemeyi İşle", "viewDetails": "Detayları görüntüle", "openMenu": "Menüyü a<PERSON>", "deleteConfirm": "Bu ödemeyi silmek istediğinizden emin misiniz?", "deletePlanConfirm": "Bu ödeme planını silmek istediğinizden emin misiniz?", "generateReceipt": "Makbuz Oluştur", "printReceipt": "Makbuz Yazdır"}, "descriptions": {"initialBalance": "Başlangıç bakiyesi", "initialBalanceFromImport": "İçe aktarmadan ba<PERSON><PERSON><PERSON>ç bakiyesi", "proratedBalance": "{{planName}} i<PERSON><PERSON> (ayın kalan günleri)", "proratedBalanceGeneric": "Hesaplan<PERSON><PERSON>ş bakiye (ayın kalan günleri)", "remainingDaysOfMonth": "a<PERSON>ın kalan g<PERSON>i"}, "deleteDialog": {"title": "Ödemeyi <PERSON>", "description": "Bu ödemeyi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve ödeme kaydını kalıcı olarak kaldıracaktır."}, "messages": {"unknownAthlete": "Bilinmeyen Sporcu", "deleteSuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON>me si<PERSON>en hata o<PERSON>", "createSuccess": "Ödeme başarıyla oluşturuldu", "createSuccessDetail": "Ödeme başarıyla oluşturuldu", "createError": "Ödeme oluşturulurken hata oluştu", "createErrorDetail": "Lütfen tekrar deneyin veya destek ekibiyle iletişime geçin", "updateSuccess": "<PERSON><PERSON>me başar<PERSON><PERSON>", "updateSuccessDescription": "Ödeme bilgileri başarıyla güncellendi", "updateError": "<PERSON><PERSON>me g<PERSON> hata o<PERSON>", "updateErrorDetail": "Lütfen tekrar deneyin veya destek ekibiyle iletişime geçin", "updateSuccessDetail": "Ödeme bilgileri başarıyla kaydedildi", "processSuccess": "Ödeme başarıyla işlendi", "processSuccessDetail": "<PERSON><PERSON><PERSON>' <PERSON><PERSON><PERSON>", "processError": "Ödeme işlenirken hata oluştu"}, "placeholders": {"searchPayments": "Ödemeleri ara...", "enterAmount": "<PERSON><PERSON> girin", "enterDescription": "Açıklama girin", "selectAthlete": "<PERSON><PERSON><PERSON>", "searchMinChars": "Lütfen en az 3 karakter girin", "filterByStatus": "<PERSON><PERSON><PERSON> g<PERSON>re filtrele", "filterByType": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> filtrele"}, "paymentPlan": "Ödeme Planı", "statusLabel": "Durum", "plans": {"name": "Plan Adı", "amount": "<PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "monthlyValue": "Aylık <PERSON>", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "dayOfMonth": "Her ayın {{day}}. g<PERSON><PERSON><PERSON>", "plan": "Ödeme Planı", "select": "Ödeme planı seçin", "noAvailable": "Mevcut ödeme planı yok", "assignments": {"title": "Ödeme Planı Atamaları", "assign": "<PERSON>a", "assignNew": "<PERSON>ni <PERSON>", "assignFirst": "İlk Planı Ata", "assignToTeam": "Takıma Ödeme Planı Ata", "selectRequired": "Lütfen hem takım hem de ödeme planı seçin", "success": "Ödeme planı başarıyla atandı", "assignSuccess": "Ödeme planı başarıyla atandı", "removeSuccess": "Ödeme planı başarıyla kaldırıldı", "alreadyAssigned": "Bu ödeme planı zaten bu takıma atanmış", "error": "Ödeme planı atanamadı", "deactivated": "Ödeme planı başarıyla devre dışı bırakıldı", "activated": "Ödeme planı başarıyla etkinleştirildi", "deleted": "Ödeme planı ataması başarıyla silindi", "deactivate": "Ödeme planını devre dışı bırak", "activate": "Ödeme planını etkinleştir", "delete": "Atamayı sil", "oneActivePerTeam": "Takım başına yalnızca bir aktif ödeme planına izin verilir", "noAssignments": "Henüz ödeme planı ataması yok", "assignedDate": "<PERSON><PERSON>"}}, "viewPlan": "Plan Detaylarını Görüntüle", "receipt": {"title": "<PERSON><PERSON><PERSON>", "number": "Makbuz <PERSON>", "to": "<PERSON><PERSON>", "for": "Ödeme <PERSON>", "issueDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "editPage": {"title": "<PERSON><PERSON><PERSON>", "details": "Ö<PERSON>me <PERSON>", "description": "Ödeme bilgilerini ve ayarlarını güncelleyin", "currentInfo": "<PERSON><PERSON><PERSON>", "descriptionPlaceholder": "Ödeme açıklaması veya notlar ekleyin...", "completedWarning": "Bu ödeme tamamlandı olarak işaretlenmiş. Değişiklikler mali kayıtları etkileyebilir."}, "validation": {"required": "Lütfen tüm gerekli alanları doldurun", "requiredFields": "Devam etmek için lütfen tüm gerekli alanları doldurun", "dueDateBeforeDate": "Son ödeme tarihi, ödeme tarihinden önce olamaz"}, "addPage": {"title": "<PERSON><PERSON>", "description": "S<PERSON><PERSON> için yeni bir ödeme kaydı oluşturun", "details": "Ö<PERSON>me <PERSON>", "formDescription": "Aşağıdaki ödeme bilgilerini doldurun", "descriptionPlaceholder": "Ödeme açıklaması veya notlar ekleyin...", "info": "Ödeme Bilgileri", "manualPaymentNote": "Bu manuel bir ödeme kaydıdır. Otomatik işlem yapılmayacaktır.", "paymentPlanNote": "Yinelenen ödemeler için ödeme planı oluşturmayı değerlendirin."}, "planDetail": {"subtitle": "Ödeme Planı • {{date}} tarihinde oluşturuldu", "editPlan": "Planı Düzenle", "status": {"active": "Aktif"}, "actions": {"delete": "Planı Sil"}, "deleteDialog": {"title": "Ödeme Planını Sil", "description": "Bu ödeme planını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "descriptionWithAssignments": "Bu ödeme planına {{count}} sporcu atanmış durumda. Bu planı silmek tüm atamaları da kaldıracaktır. Devam etmek istediğinizden emin misiniz?"}, "messages": {"deleteSuccess": "Ödeme planı başar<PERSON><PERSON> silindi", "deleteError": "Ödeme planı silinirken hata oluştu"}, "overview": {"title": "Plan Özeti", "paymentAmount": "<PERSON><PERSON><PERSON><PERSON>", "billedFrequency": "Yinelenen {{frequency}} ödeme", "description": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "details": {"status": "Durum"}, "stats": {"title": "Hızlı İstatistikler", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "branches": "Branşlar"}, "branches": {"title": "<PERSON><PERSON><PERSON>", "description": "Bu plan {{count}} branşta mevcut", "description_other": "Bu plan {{count}} branşta mevcut", "noBranches": "Bu plana atanmış branş yok."}}, "planEdit": {"title": "Ödeme Planını Düzenle", "subtitle": "Ödeme planı detaylarını ve ayarlarını güncelleyin", "branchesCount_one": "{{count}} branş", "branchesCount_other": "{{count}} branş", "basicInfo": {"title": "<PERSON><PERSON>", "subtitle": "Plan adı, tutar ve faturalama sıklığını güncelleyin"}, "branches": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Hangi branşların bu ödeme planını sunabileceğini seçin", "loading": "Branşlar yükleniyor...", "selected": "Seçilen: {{selected}} / {{total}} branş", "selectAtLeastOne": "Bu ödeme planı için lütfen en az bir branş seçin."}, "summary": {"title": "Plan Ö<PERSON>zle<PERSON>i", "monthlyValue": "Aylık <PERSON>", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "branches": "Branşlar", "status": "Durum"}, "fields": {"name": "Plan Adı", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "monthlyValue": "Aylık <PERSON>", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "status": "Durum"}, "placeholders": {"description": "Ödeme planının isteğe bağlı açıklaması", "selectStatus": "<PERSON>rum seçin"}, "frequency": {"monthly": "Aylık"}, "status": {"active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>"}, "buttons": {"save": "Değişiklikleri Kaydet", "saving": "Kay<PERSON>ili<PERSON>r..."}, "messages": {"updateSuccess": "Ödeme planı başarıyla güncellendi"}, "errors": {"updateFailed": "Ödeme planı güncellenirken hata oluştu", "failedToLoadBranches": "Branşlar yüklenirken hata oluştu"}}, "planCreate": {"title": "Ödeme Planı Oluştur", "subtitle": "Kuruluşunuz için yeni bir ödeme planı oluşturun", "basicInfo": {"title": "<PERSON><PERSON>", "subtitle": "Bu ödeme planı için temel detayları girin"}, "fields": {"name": "Plan Adı", "amount": "Tutar (TL)", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "status": "Durum"}, "placeholders": {"name": "Plan adını girin", "amount": "0,00", "description": "Ödeme planının isteğe bağlı açıklaması", "selectStatus": "<PERSON>rum seçin"}, "branches": {"title": "Branş Seçimi", "subtitle": "Hangi branşların bu ödeme planını sunabileceğini seçin", "selectAtLeastOne": "Lütfen en az bir branş seçin", "loading": "Branşlar yükleniyor...", "selected": "Seçilen: {{selected}} / {{total}} branş"}, "summary": {"title": "<PERSON>zleme", "amount": "<PERSON><PERSON>", "assignDay": "Atama Günü", "dueDay": "<PERSON><PERSON>", "status": "Durum", "branches": "Branşlar"}, "buttons": {"save": "Plan Oluştur", "saving": "Oluşturuluyor...", "cancel": "İptal"}, "messages": {"createSuccess": "Ödeme planı başarıyla oluşturuldu", "createError": "Ödeme planı oluşturulurken hata oluştu", "validationError": "Lütfen formdaki hataları kontrol edin"}, "validation": {"branchesRequired": "En az bir branş seçilmelidir", "invalidDays": "Atama günü ve vade günü 1 ile 31 arasında olmalıdır"}}, "assignments": {"overview": {"error": "Ödeme planı atanamadı"}}}}