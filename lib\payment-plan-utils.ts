import { db } from "@/src/db";
import { athletePaymentPlans, payments, paymentPlans, teams, athletes } from "@/src/db/schema";
import { eq, and, count, desc, ne } from "drizzle-orm";
import { calculateAthleteBalance } from "./balance-calculator";

export interface AssignPaymentPlanParams {
  athleteId: string;
  planId: string;
  teamId?: string;
  tenantId: string;
  userId: bigint;
  isActive?: boolean;
}

/**
 * Assign a payment plan to an athlete for a specific team or general use
 */
export async function assignPaymentPlanToAthlete({
  athleteId,
  planId,
  teamId,
  tenantId,
  userId,
  isActive = true,
}: AssignPaymentPlanParams) {
  // Get payment plan details
  const plan = await db
    .select()
    .from(paymentPlans)
    .where(and(eq(paymentPlans.id, planId), eq(paymentPlans.tenantId, tenantId)))
    .limit(1);

  if (!plan.length) {
    throw new Error("Payment plan not found");
  }

  const paymentPlan = plan[0];

  if (paymentPlan.status !== 'active') {
    throw new Error("Cannot assign inactive payment plan");
  }

  // Validate due day is after assign day
  if (paymentPlan.dueDay < paymentPlan.assignDay) {
    throw new Error("Due day cannot be before assign day");
  }

  // If teamId is provided, check if athlete already has an active plan for this team
  if (teamId) {
    const existingAssignment = await db
      .select()
      .from(athletePaymentPlans)
      .where(
        and(
          eq(athletePaymentPlans.athleteId, athleteId),
          eq(athletePaymentPlans.teamId, teamId),
          eq(athletePaymentPlans.isActive, true),
          eq(athletePaymentPlans.tenantId, tenantId)
        )
      );

    if (existingAssignment.length > 0) {
      throw new Error("Athlete already has an active payment plan for this team");
    }
  }

  // Create athlete payment plan record
  const [athletePaymentPlan] = await db
    .insert(athletePaymentPlans)
    .values({
      tenantId,
      athleteId,
      planId,
      teamId,
      assignedDate: new Date().toISOString().split('T')[0],
      isActive,
      createdBy: userId,
      updatedBy: userId,
    })
    .returning();

  return athletePaymentPlan;
}

/**
 * Get all payment plans assigned to an athlete
 */
export async function getAthletePaymentPlans(athleteId: string, tenantId: string) {
  return await db
    .select({
      id: athletePaymentPlans.id,
      planId: athletePaymentPlans.planId,
      teamId: athletePaymentPlans.teamId,
      assignedDate: athletePaymentPlans.assignedDate,
      isActive: athletePaymentPlans.isActive,
      lastPaymentDate: athletePaymentPlans.lastPaymentDate,
      planName: paymentPlans.name,
      monthlyValue: paymentPlans.monthlyValue,
      assignDay: paymentPlans.assignDay,
      dueDay: paymentPlans.dueDay,
      planStatus: paymentPlans.status,
      planDescription: paymentPlans.description,
      teamName: teams.name,
      athleteName: athletes.name,
      athleteSurname: athletes.surname,
    })
    .from(athletePaymentPlans)
    .innerJoin(
      paymentPlans, 
      and(
        eq(athletePaymentPlans.planId, paymentPlans.id),
        eq(paymentPlans.tenantId, tenantId)
      )
    )
    .innerJoin(
      athletes, 
      and(
        eq(athletePaymentPlans.athleteId, athletes.id),
        eq(athletes.tenantId, tenantId)
      )
    )
    .leftJoin(
      teams, 
      and(
        eq(athletePaymentPlans.teamId, teams.id),
        eq(teams.tenantId, tenantId)
      )
    )
    .where(
      and(
        eq(athletePaymentPlans.athleteId, athleteId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    )
    .orderBy(desc(athletePaymentPlans.assignedDate));
}

/**
 * Get the count of athletes assigned to each payment plan
 */
export async function getPaymentPlanAssignmentCounts(tenantId: string) {
  return await db
    .select({
      planId: paymentPlans.id,
      planName: paymentPlans.name,
      monthlyValue: paymentPlans.monthlyValue,
      status: paymentPlans.status,
      activeAssignments: count(athletePaymentPlans.id),
    })
    .from(paymentPlans)
    .leftJoin(
      athletePaymentPlans,
      and(
        eq(paymentPlans.id, athletePaymentPlans.planId),
        eq(athletePaymentPlans.isActive, true)
      )
    )
    .where(eq(paymentPlans.tenantId, tenantId))
    .groupBy(paymentPlans.id, paymentPlans.name, paymentPlans.monthlyValue, paymentPlans.status)
    .orderBy(paymentPlans.name);
}

/**
 * Deactivate a payment plan assignment
 */
export async function deactivatePaymentPlanAssignment(
  athletePaymentPlanId: string,
  tenantId: string,
  userId: bigint
) {
  // Get the assignment details first to know which athlete's balance to update
  const assignment = await db
    .select({ athleteId: athletePaymentPlans.athleteId })
    .from(athletePaymentPlans)
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    )
    .limit(1);

  if (!assignment.length) {
    throw new Error("Payment plan assignment not found");
  }

  const athleteId = assignment[0].athleteId;

  // Deactivate the payment plan assignment
  await db
    .update(athletePaymentPlans)
    .set({
      isActive: false,
      updatedBy: userId,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    );

  // Cancel all pending payments for this assignment
  await db
    .update(payments)
    .set({
      status: "cancelled",
      updatedBy: userId,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(payments.athletePaymentPlanId, athletePaymentPlanId),
        eq(payments.status, "pending"),
        eq(payments.tenantId, tenantId)
      )
    );

  // Recalculate athlete balance after cancelling payments
  await calculateAthleteBalance(athleteId, tenantId);
}

/**
 * Reactivate a payment plan assignment
 */
export async function reactivatePaymentPlanAssignment(
  athletePaymentPlanId: string,
  tenantId: string,
  userId: bigint
) {
  // First, get the assignment details to check the team
  const assignment = await db
    .select({
      athleteId: athletePaymentPlans.athleteId,
      teamId: athletePaymentPlans.teamId,
    })
    .from(athletePaymentPlans)
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    )
    .limit(1);

  if (assignment.length === 0) {
    throw new Error('Payment plan assignment not found');
  }

  const { athleteId, teamId } = assignment[0];

  // If there's a team, deactivate any other active payment plans for the same athlete and team
  if (teamId) {
    await db
      .update(athletePaymentPlans)
      .set({
        isActive: false,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(athletePaymentPlans.athleteId, athleteId),
          eq(athletePaymentPlans.teamId, teamId),
          eq(athletePaymentPlans.isActive, true),
          ne(athletePaymentPlans.id, athletePaymentPlanId),
          eq(athletePaymentPlans.tenantId, tenantId)
        )
      );
  }

  // Reactivate the payment plan assignment
  await db
    .update(athletePaymentPlans)
    .set({
      isActive: true,
      updatedBy: userId,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    );

  // Recalculate athlete balance after reactivating
  await calculateAthleteBalance(athleteId, tenantId);
}

/**
 * Delete a payment plan assignment completely
 */
export async function deletePaymentPlanAssignment(
  athletePaymentPlanId: string,
  tenantId: string,
  userId: bigint,
  preservePaymentStatus: boolean = false
) {
  // Get the assignment details first to know which athlete's balance to update
  const assignment = await db
    .select({ athleteId: athletePaymentPlans.athleteId })
    .from(athletePaymentPlans)
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    )
    .limit(1);

  if (!assignment.length) {
    throw new Error("Payment plan assignment not found");
  }

  const athleteId = assignment[0].athleteId;

  // Only cancel payments if preservePaymentStatus is false (default behavior)
  if (!preservePaymentStatus) {
    // Cancel all related payments
    await db
      .update(payments)
      .set({
        status: "cancelled",
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(payments.athletePaymentPlanId, athletePaymentPlanId),
          eq(payments.status, "pending"),
          eq(payments.tenantId, tenantId)
        )
      );
  }

  // Then delete the payment plan assignment
  await db
    .delete(athletePaymentPlans)
    .where(
      and(
        eq(athletePaymentPlans.id, athletePaymentPlanId),
        eq(athletePaymentPlans.tenantId, tenantId)
      )
    );

  // Recalculate athlete balance after cancelling payments (or keeping them if preserved)
  await calculateAthleteBalance(athleteId, tenantId);
}

/**
 * Create scheduled payments for active payment plan assignments
 * This function should be called by a scheduled job (e.g., cron job)
 */
export async function createScheduledPayments(tenantId: string, userId: bigint) {
  const today = new Date();
  const currentDay = today.getDate();
  
  // Get all active payment plan assignments where today matches the assign day
  const assignmentsToProcess = await db
    .select({
      assignmentId: athletePaymentPlans.id,
      athleteId: athletePaymentPlans.athleteId,
      planId: athletePaymentPlans.planId,
      lastPaymentDate: athletePaymentPlans.lastPaymentDate,
      planName: paymentPlans.name,
      monthlyValue: paymentPlans.monthlyValue,
      assignDay: paymentPlans.assignDay,
      dueDay: paymentPlans.dueDay,
    })
    .from(athletePaymentPlans)
    .innerJoin(paymentPlans, eq(athletePaymentPlans.planId, paymentPlans.id))
    .where(
      and(
        eq(athletePaymentPlans.tenantId, tenantId),
        eq(athletePaymentPlans.isActive, true),
        eq(paymentPlans.status, 'active'),
        eq(paymentPlans.assignDay, currentDay)
      )
    );

  const createdPayments = [];

  for (const assignment of assignmentsToProcess) {
    // Check if we already created a payment this month
    const lastPayment = assignment.lastPaymentDate ? new Date(assignment.lastPaymentDate) : null;
    const thisMonth = today.getFullYear() * 12 + today.getMonth();
    const lastPaymentMonth = lastPayment ? lastPayment.getFullYear() * 12 + lastPayment.getMonth() : null;

    // Skip if we already created a payment this month
    if (lastPaymentMonth === thisMonth) {
      continue;
    }

    // Calculate due date (same month, but different day)
    const dueDate = new Date(today.getFullYear(), today.getMonth(), assignment.dueDay);
    
    // If due day has already passed this month, set due date to next month
    if (assignment.dueDay < currentDay) {
      dueDate.setMonth(dueDate.getMonth() + 1);
    }

    const dateString = today.toISOString().split('T')[0];
    const dueDateString = dueDate.toISOString().split('T')[0];

    // Create the payment
    const [payment] = await db
      .insert(payments)
      .values({
        tenantId,
        athleteId: assignment.athleteId,
        athletePaymentPlanId: assignment.assignmentId,
        amount: assignment.monthlyValue,
        date: dateString,
        dueDate: dueDateString,
        status: "pending",
        type: "fee",
        description: `Monthly payment for ${assignment.planName}`,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();

    // Recalculate athlete balance after creating payment
    await calculateAthleteBalance(assignment.athleteId, tenantId);

    // Update the last payment date in the assignment
    await db
      .update(athletePaymentPlans)
      .set({
        lastPaymentDate: dateString,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(athletePaymentPlans.id, assignment.assignmentId));

    createdPayments.push(payment);
  }

  return createdPayments;
}

/**
 * Mark a payment as completed and update athlete balance
 */
export async function markPaymentAsCompleted(
  paymentId: string,
  tenantId: string,
  userId: bigint
) {
  // Mark payment as completed
  const [payment] = await db
    .update(payments)
    .set({
      status: "completed",
      updatedBy: userId,
      updatedAt: new Date(),
    })
    .where(
      and(eq(payments.id, paymentId), eq(payments.tenantId, tenantId))
    )
    .returning();

  return payment;
}

/**
 * Check and update overdue payments
 * This function should be called by a scheduled job daily
 */
export async function updateOverduePayments(tenantId: string, userId: bigint) {
  const today = new Date();
  const todayString = today.toISOString().split('T')[0];

  // Update pending payments that are past due date to overdue status
  const updatedPayments = await db
    .update(payments)
    .set({
      status: "overdue",
      updatedBy: userId,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(payments.tenantId, tenantId),
        eq(payments.status, "pending"),
        // dueDate < today
      )
    )
    .returning();

  return updatedPayments;
}