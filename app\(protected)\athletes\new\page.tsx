import NewAthleteClient from "./new-athlete-client";
import { getTeams } from "@/lib/db/actions/teams";
import { getPaymentPlans } from "@/lib/db/actions/payment-plans";

export default async function NewAthletePage() {
  // Fetch teams and payment plans for the form
  const [teams, paymentPlans] = await Promise.all([
    getTeams(),
    getPaymentPlans()
  ]);

  return (
    <NewAthleteClient 
      teams={teams} 
      paymentPlans={paymentPlans} 
    />
  );
}
