"use server";

import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { athleteService } from '../services';
import { getTeams } from './teams';
import { parseAndFormatDate, parseAndFormatDateWithTodayFallback, getTodayAsLocalString } from '../utils/date-formatter';
import { getPaymentPlans } from './payment-plans';
import { addAthleteToTeam, removeAthleteFromTeam } from './athlete-teams';
import { assignPaymentPlan, getAthleteAssignedPlans, deleteAssignment } from './payment-plan-assignments';

// Athletes
export async function getAthletes() {
  try {
    const tenantId = await getServerTenantId();
    const result = await athleteService().getAthletes(
      undefined, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("getAthletes error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get athletes");
    }
    
    return result.data || [];
  } catch (error) {
    console.error("getAthletes error:", error);
    throw error;
  }
}

export async function getAthletesPaginated(
  page: number = 1,
  limit: number = 10,
  search?: string,
  sortBy?: string,
  sortOrder?: 'asc' | 'desc',
  filters?: {
    name?: string;
    surname?: string;
    parentEmail?: string;
    parentPhone?: string;
    nationalId?: string;
    status?: 'active' | 'inactive' | 'suspended';
  }
) {
  const tenantId = await getServerTenantId();
  
  const result = await athleteService().getAthletesPaginated(
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    filters,
    undefined,
    tenantId || undefined
  );
  
  if (!result.success) {
    throw new Error(result.error?.message || 'Failed to get athletes');
  }
  
  return result.data;
}

export async function getAthleteById(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await athleteService().getAthleteById(
      id, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("getAthleteById error:", result.error);
      throw new Error(result.error?.userMessage || `Failed to get athlete with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    console.error("getAthleteById error:", error);
    throw error;
  }
}

export async function getOverdueAthletes() {
  try {
    const tenantId = await getServerTenantId();
    const result = await athleteService().getOverdueAthletes(
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("getOverdueAthletes error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get overdue athletes");
    }
    
    return result.data || [];
  } catch (error) {
    console.error("getOverdueAthletes error:", error);
    throw error;
  }
}

export async function updateAthlete(id: string, data: {
  name?: string;
  surname?: string;
  nationalId?: string;
  birthDate?: string;
  registrationDate?: string;
  parentName?: string;
  parentSurname?: string;
  parentPhone?: string;
  parentEmail?: string;
  parentAddress?: string;
}) {
  try {
    const tenantId = await getServerTenantId();
    const result = await athleteService().updateAthlete(
      id, 
      data, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error(`Failed to update athlete with ID ${id} updateAthlete error:`, result.error);
      if(result.validationErrors!.length || 0 > 0){
        return { success: false, error: result.validationErrors![0].message , errorType: 'BusinessRuleError' };
      }
      return { success: false, error: result.error?.userMessage || "Failed to update athlete" , errorType: 'general' };
    }
    return { success: true, data: result.data };
  } catch (error) {
    console.error("updateAthlete error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to update athlete" };
  }
}

export async function deleteAthlete(id: string) {
  try {
    const tenantId = await getServerTenantId();
    const result = await athleteService().deleteAthlete(
      id, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      if(result.error?.details && result.error.details.rule) {
        return { success: false, error: result.error?.details.rule , errorType: result.error?.cause?.name };
      }
      console.error(`Failed to delete athlete with ID ${id}. deleteAthlete error:`, result.error);
      return { success: false, error: "Failed to delete athlete", errorType: 'general' };
    }
    
    return { success: true };
  } catch (error) {
    console.error("deleteAthlete error:", error);
    return { success: false, error: "Failed to delete athlete" };
  }
}

export async function createAthlete(values: {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate?: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  paymentPlanId?: string;
}) {
  try {
    const tenantId = await getServerTenantId();
    const athleteData = {
      name: values.name,
      surname: values.surname,
      nationalId: values.nationalId,
      birthDate: values.birthDate,
      registrationDate: values.registrationDate || getTodayAsLocalString(),
      parentName: values.parentName,
      parentSurname: values.parentSurname,
      parentPhone: values.parentPhone,
      parentEmail: values.parentEmail || "",
      parentAddress: values.parentAddress || "",
    };
    
    const result = await athleteService().createAthlete(
      athleteData, 
      undefined, 
      tenantId || undefined
    );
    
    if (!result.success) {
      console.error("createAthlete error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || "Failed to create athlete" , errorType: 'general' };
    }
    
    // TODO: If paymentPlanId is provided, create initial payment record
    if (values.paymentPlanId) {
      console.log('Payment plan assignment will be implemented later:', values.paymentPlanId);
    }

    return { success: true, data: result.data };
  } catch (error) {
    console.error('Error creating athlete:', error);
    return { success: false, error: "Failed to create athlete" };
  }
}

export async function createAthleteWithTeamAssignments(values: {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate?: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  teamAssignments?: {
    teamId: string;
    paymentPlanId?: string;
  }[];
}) {
  try {
    // First create the athlete
    const athleteResult = await createAthlete({
      name: values.name,
      surname: values.surname,
      nationalId: values.nationalId,
      birthDate: values.birthDate,
      registrationDate: values.registrationDate,
      parentName: values.parentName,
      parentSurname: values.parentSurname,
      parentPhone: values.parentPhone,
      parentEmail: values.parentEmail,
      parentAddress: values.parentAddress,
    });

    if (!athleteResult.success || !athleteResult.data?.id) {
      return athleteResult;
    }

    const athleteId = athleteResult.data.id;
    
    // Process team assignments if provided
    if (values.teamAssignments && values.teamAssignments.length > 0) {
      for (const assignment of values.teamAssignments) {
        try {
          // Add athlete to team
          await addAthleteToTeam(athleteId, assignment.teamId);
          
          // If payment plan is specified, assign it
          if (assignment.paymentPlanId) {
            await assignPaymentPlan({
              athleteId,
              planId: assignment.paymentPlanId,
              teamId: assignment.teamId,
              isActive: true
            });
          }
        } catch (assignmentError) {
          console.error(`Error processing team assignment for team ${assignment.teamId}:`, assignmentError);
          // Continue with other assignments even if one fails
        }
      }
    }

    return { success: true, data: athleteResult.data };
  } catch (error) {
    console.error('Error creating athlete with team assignments:', error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to create athlete with team assignments" };
  }
}

export async function createAthleteWithTeamAssignmentsAndBalance(values: {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate?: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  initialBalance?: string;
  useProrated?: boolean;
  teamAssignments?: {
    teamId: string;
    paymentPlanId?: string;
  }[];
}, locale: string = 'en') {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    
    // Delegate to service layer for business logic
    const result = await athleteService().createAthleteWithTeamAssignmentsAndBalance(
      {
        name: values.name,
        surname: values.surname,
        nationalId: values.nationalId,
        birthDate: values.birthDate,
        registrationDate: values.registrationDate,
        parentName: values.parentName,
        parentSurname: values.parentSurname,
        parentPhone: values.parentPhone,
        parentEmail: values.parentEmail || "",
        parentAddress: values.parentAddress || "",
        teamAssignments: values.teamAssignments,
        initialBalance: values.initialBalance,
        useProrated: values.useProrated,
      },
      userId?.toString(),
      tenantId || undefined,
      locale
    );
    
    if (!result.success) {
      console.error("createAthleteWithTeamAssignmentsAndBalance error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }else{
        return { success: false, error: result.error?.userMessage || "Failed to create athlete with team assignments and balance" , errorType: 'general' };
      }
    }
    return { success: true, data: result.data };
  } catch (error) {
    console.error('Error creating athlete with team assignments and balance:', error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to create athlete with team assignments and balance", errorType: 'general' };
  }
}

export async function activateAthlete(
  athleteId: string,
  teamAssignments?: {
    teamId: string;
    paymentPlanId?: string;
  }[]
) {
  try {
    const tenantId = await getServerTenantId();
    
    // First, update the athlete status to active
    const updateResult = await athleteService().updateAthlete(
      athleteId,
      { status: 'active' },
      undefined,
      tenantId || undefined
    );

    if (!updateResult.success) {
      console.error(`Failed to activate athlete with ID ${athleteId} activateAthlete error:`, updateResult.error);
      if((updateResult.validationErrors?.length || 0) > 0){
        return { success: false, error: updateResult.validationErrors![0].message , errorType: 'BusinessRuleError' };
      }
      return { success: false, error: updateResult.error?.userMessage || "Failed to activate athlete" , errorType: 'general' };
    }

    // If team assignments are provided, process them
    if (teamAssignments && teamAssignments.length > 0) {
      for (const assignment of teamAssignments) {
        try {
          // Add athlete to team
          await addAthleteToTeam(athleteId, assignment.teamId);
          
          // If payment plan is specified, assign it
          if (assignment.paymentPlanId) {
            await assignPaymentPlan({
              athleteId,
              planId: assignment.paymentPlanId,
              teamId: assignment.teamId,
              isActive: true
            });
          }
        } catch (assignmentError) {
          console.error(`Error processing team assignment for team ${assignment.teamId}:`, assignmentError);
          // Continue with other assignments even if one fails
        }
      }
    }

    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error("activateAthlete error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to activate athlete" };
  }
}

export async function deactivateAthlete(athleteId: string) {
  try {
    const tenantId = await getServerTenantId();
    
    // First, get current athlete data to check teams and payment plans
    const athlete = await getAthleteById(athleteId);
    
    if (!athlete) {
      throw new Error(`Athlete with ID ${athleteId} not found`);
    }

    // Remove athlete from all teams
    if (athlete.teamDetails && athlete.teamDetails.length > 0) {
      for (const teamDetail of athlete.teamDetails) {
        if (!teamDetail.leftAt) { // Only remove if still active in team
          try {
            await removeAthleteFromTeam(athleteId, teamDetail.teamId);
          } catch (removeError) {
            console.error(`Error removing athlete from team ${teamDetail.teamId}:`, removeError);
            // Continue with other teams even if one fails
          }
        }
      }
    }

    // Remove all payment plan assignments (preserve payment status)
    try {
      const paymentPlans = await getAthleteAssignedPlans(athleteId);
      
      for (const plan of paymentPlans) {
        if (plan.isActive) {
          try {
            await deleteAssignment(plan.id, true); // true = preserve payment status
          } catch (deleteError) {
            console.error(`Error deleting payment plan assignment ${plan.id}:`, deleteError);
            // Continue with other plans even if one fails
          }
        }
      }
    } catch (plansError) {
      console.error("Error getting athlete payment plans:", plansError);
    }

    // Finally, update the athlete status to inactive
    const updateResult = await athleteService().updateAthlete(
      athleteId,
      { status: 'inactive' },
      undefined,
      tenantId || undefined
    );

    if (!updateResult.success) {
      console.error(`Failed to deactivate athlete with ID ${athleteId} deactivateAthlete error:`, updateResult.error);
      if(updateResult.validationErrors?.length || 0 > 0){
        return { success: false, error: updateResult.validationErrors![0].message , errorType: 'BusinessRuleError' };
      }
      return { success: false, error: updateResult.error?.userMessage || "Failed to deactivate athlete" , errorType: 'general' };
    }
    return { success: true, data: updateResult.data };
  } catch (error) {
    console.error("deactivateAthlete error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to deactivate athlete" };
  }
}

export async function getAthleteActivationData(athleteId: string) {
  try {
    const athlete = await getAthleteById(athleteId);
    
    if (!athlete) {
      throw new Error(`Athlete with ID ${athleteId} not found`);
    }

    // Get current teams
    const teams = athlete.teamDetails || [];
    
    // Get current payment plans
    let paymentPlans = [];
    try {
      paymentPlans = await getAthleteAssignedPlans(athleteId);
    } catch (error) {
      console.error("Error getting athlete payment plans:", error);
      paymentPlans = [];
    }

    return {
      success: true,
      data: {
        athlete: {
          id: athlete.id,
          name: athlete.name,
          surname: athlete.surname,
          status: athlete.status,
        },
        teams: teams.filter((t: any) => !t.leftAt), // Only active teams
        paymentPlans: paymentPlans.filter(p => p.isActive), // Only active payment plans
      }
    };
  } catch (error) {
    console.error("getAthleteActivationData error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to get athlete activation data" };
  }
}

export async function downloadAthleteTemplate(locale: string = 'en'): Promise<Blob> {
  try {
    const tenantId = await getServerTenantId();

    // Get teams and payment plans for dropdowns
    const [teams, paymentPlans] = await Promise.all([
      getTeams(),
      getPaymentPlans()
    ]);

    // Dynamic import to avoid memory leaks
    const ExcelJS = (await import('exceljs')).default;
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Athletes');

    // Define column headers with translations
    const getColumnHeaders = (locale: string) => {
      if (locale === 'tr') {
        return {
          name: "Ad*",
          surname: "Soyad*",
          nationalId: "TC Kimlik No*",
          birthdate: "Doğum Tarihi*",
          registrationDate: "Kayıt Tarihi*",
          parentName: "Veli Adı*",
          parentSurname: "Veli Soyadı*",
          parentPhone: "Veli Telefonu*",
          parentEmail: "Veli E-postası",
          currentBalance: "Mevcut Bakiye",
          currentBalanceLastPaymentDate: "Mevcut Bakiye Son Ödeme Tarihi",
          status: "Durum",
          team1: "Takım1",
          team2: "Takım2",
          team3: "Takım3",
          paymentPlan1: "Ödeme Planı1",
          paymentPlan2: "Ödeme Planı2",
          paymentPlan3: "Ödeme Planı3"
        };
      } else {
        // Default to English
        return {
          name: "Name*",
          surname: "Surname*",
          nationalId: "National Id*",
          birthdate: "Birthdate*",
          registrationDate: "Registration Date*",
          parentName: "Parent Name*",
          parentSurname: "Parent Surname*",
          parentPhone: "Parent Phone*",
          parentEmail: "Parent Email",
          currentBalance: "Current Balance",
          currentBalanceLastPaymentDate: "Current Balance Last Payment Date",
          status: "Status",
          team1: "Team1",
          team2: "Team2",
          team3: "Team3",
          paymentPlan1: "Payment Plan1",
          paymentPlan2: "Payment Plan2",
          paymentPlan3: "Payment Plan3"
        };
      }
    };

    const t = getColumnHeaders(locale);

    // Define column headers with translations
    const headers = [
      t.name,
      t.surname,
      t.nationalId,
      t.birthdate,
      t.registrationDate,
      t.parentName,
      t.parentSurname,
      t.parentPhone,
      t.parentEmail,
      t.currentBalance,
      t.currentBalanceLastPaymentDate,
      t.status,
      t.team1,
      t.team2,
      t.team3,
      t.paymentPlan1,
      t.paymentPlan2,
      t.paymentPlan3
    ];

    // Add headers
    worksheet.addRow(headers);

    // Style headers
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Auto-fit columns
    worksheet.columns.forEach((column, index) => {
      column.width = Math.max(headers[index].length + 2, 15);
    });

    // Add data validation for dropdowns
    const teamNames = teams.map(team => team.name);
    const paymentPlanNames = paymentPlans.map(plan => plan.name);

    // Add 500 rows for dropdown data
    for (let row = 2; row <= 501; row++) {
      // Status dropdown (column K = 11)
      worksheet.getCell(`K${row}`).dataValidation = {
        type: 'list',
        allowBlank: true,
        formulae: ['"active,inactive"']
      };

      // Team dropdowns (columns L, M, N = 12, 13, 14)
      if (teamNames.length > 0) {
        ['L', 'M', 'N'].forEach(col => {
          worksheet.getCell(`${col}${row}`).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`"${teamNames.join(',')}"`]
          };
        });
      }

      // Payment plan dropdowns (columns O, P, Q = 15, 16, 17)
      if (paymentPlanNames.length > 0) {
        ['O', 'P', 'Q'].forEach(col => {
          worksheet.getCell(`${col}${row}`).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [`"${paymentPlanNames.join(',')}"`]
          };
        });
      }
    }

    // Generate buffer and return as blob
    const buffer = await workbook.xlsx.writeBuffer();
    return new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
  } catch (error) {
    console.error('Error generating template:', error);
    throw new Error('Failed to generate template');
  }
}

export async function bulkImportAthletes(formData: FormData, locale: string = 'en') {
  try {
    const tenantId = await getServerTenantId();

    if (!tenantId) {
      throw new Error('Tenant context not found');
    }

    const file = formData.get('file') as File;
    if (!file) {
      throw new Error('No file provided');
    }

    // Parse Excel file
    const buffer = await file.arrayBuffer();
    // Dynamic import to avoid memory leaks
    const ExcelJS = (await import('exceljs')).default;
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      throw new Error('No worksheet found');
    }

    // Get reference data for mapping team/payment plan names to IDs
    const [teams, paymentPlans] = await Promise.all([
      getTeams(),
      getPaymentPlans()
    ]);

    const teamMap = new Map(teams.map(team => [team.name, team.id]));
    const paymentPlanMap = new Map(paymentPlans.map(plan => [plan.name, plan.id]));

    // Parse Excel data into structured format
    const athletesData = [];
    const rows = worksheet.getSheetValues();

    for (let rowIndex = 2; rowIndex < rows.length; rowIndex++) {
      const row = rows[rowIndex] as any[];
      if (!row || row.length === 0) continue;

      // Extract data from row
      const name = row[1]?.toString().trim();
      const surname = row[2]?.toString().trim();
      const nationalId = row[3]?.toString().trim();
      const birthdate = row[4];
      const registrationDate = row[5];
      const parentName = row[6]?.toString().trim();
      const parentSurname = row[7]?.toString().trim();
      const parentPhone = row[8]?.toString().trim();

      // Handle parent email - Excel might convert it to hyperlink object
      let parentEmail = '';
      if (row[9]) {
        if (typeof row[9] === 'object' && row[9].text) {
          // Excel hyperlink object
          parentEmail = row[9].text.toString().trim();
        } else if (typeof row[9] === 'object' && row[9].hyperlink) {
          // Another hyperlink format
          parentEmail = row[9].hyperlink.toString().trim();
        } else {
          // Regular text
          parentEmail = row[9].toString().trim();
        }
      }

      const currentBalance = row[10]?.toString().trim() || '0';
      const balanceLastPaymentDate = row[11];
      const status = row[12]?.toString().trim() || 'active';
      const team1 = row[13]?.toString().trim();
      const team2 = row[14]?.toString().trim();
      const team3 = row[15]?.toString().trim();
      const paymentPlan1 = row[16]?.toString().trim();
      const paymentPlan2 = row[17]?.toString().trim();
      const paymentPlan3 = row[18]?.toString().trim();

      // Format birthdate using utility function
      const formattedBirthdate = parseAndFormatDate(birthdate);

      // Format registration date using utility function with today fallback
      const formattedRegistrationDate = parseAndFormatDateWithTodayFallback(registrationDate);

      // Prepare team assignments
      const teamAssignments: Array<{ teamId: string; paymentPlanId?: string }> = [];

      const teams = [team1, team2, team3].filter(Boolean);
      const paymentPlans = [paymentPlan1, paymentPlan2, paymentPlan3].filter(Boolean);

      for (let i = 0; i < teams.length; i++) {
        const teamName = teams[i];
        const paymentPlanName = paymentPlans[i];

        const teamId = teamMap.get(teamName);
        if (teamId) {
          const assignment: { teamId: string; paymentPlanId?: string } = { teamId };

          if (paymentPlanName) {
            const paymentPlanId = paymentPlanMap.get(paymentPlanName);
            if (paymentPlanId) {
              assignment.paymentPlanId = paymentPlanId;
            }
          }

          teamAssignments.push(assignment);
        }
      }

      // Add to athletes data array
      athletesData.push({
        name,
        surname,
        nationalId,
        birthDate: formattedBirthdate,
        registrationDate: formattedRegistrationDate,
        parentName,
        parentSurname,
        parentPhone,
        parentEmail,
        currentBalance,
        currentBalanceLastPaymentDate: balanceLastPaymentDate,
        status,
        teamAssignments
      });
    }

    // Use service layer for all business logic
    const result = await athleteService().bulkImportAthletes(athletesData, locale);

    if (!result.success) {
      throw new Error(result.error?.message || 'Import failed');
    }

    return result.data;

  } catch (error) {
    console.error('Bulk import error:', error);
    return {
      success: false,
      processed: 0,
      created: 0,
      errors: [{ row: 0, message: error instanceof Error ? error.message : 'Import failed' }]
    };
  }
}