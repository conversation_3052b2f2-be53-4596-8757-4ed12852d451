import { notFound } from "next/navigation";
import { Metadata } from "next";
import { getItemById } from "@/lib/db/actions";
import EditItemClient from "./edit-item-client";

interface EditItemPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({ params }: EditItemPageProps): Promise<Metadata> {
  try {
    const resolvedParams = await params;
    const item = await getItemById(resolvedParams.id);
    if (!item) {
      return {
        title: "Item Not Found",
        description: "The requested item could not be found.",
      };
    }
    
    return {
      title: `Edit ${item.name}`,
      description: `Edit ${item.name} details and settings`,
      keywords: ["edit", "item", "sports equipment", item.category, item.name],
    };
  } catch {
    return {
      title: "Edit Item",
      description: "Edit sports equipment details.",
    };
  }
}

export default async function EditItemPage({ params }: EditItemPageProps) {
  // Resolve the params Promise and validate the item ID parameter
  const resolvedParams = await params;
  if (!resolvedParams.id || typeof resolvedParams.id !== 'string') {
    notFound();
  }

  try {
    const item = await getItemById(resolvedParams.id);

    if (!item) {
      notFound();
    }

    // Validate item has required properties
    if (!item.name || !item.price) {
      console.error("Invalid item data:", item);
      notFound();
    }

    return (
      <div className="container mx-auto py-6">
        <EditItemClient item={item} />
      </div>
    );
  } catch (error) {
    console.error("Error loading item data:", error);
    notFound();
  }
}