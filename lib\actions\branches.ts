"use server";

import { getServerTenantId } from '../tenant-utils-server';
import { branchService } from '../services';

export async function getBranches() {
  try {
    const tenantId = await getServerTenantId();
    const result = await branchService().getBranches(undefined, tenantId || undefined);
    
    if (!result.success) {
      console.error("getBranches error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get branches");
    }
    
    return result.data || [];
  } catch (error) {
    console.error("getBranches error:", error);
    throw error;
  }
}
