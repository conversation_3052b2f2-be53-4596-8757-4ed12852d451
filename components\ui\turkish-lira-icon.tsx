import React from 'react';
import type { LucideProps } from 'lucide-react';

export const TurkishLiraIcon = React.forwardRef<SVGSVGElement, LucideProps>(({
  className = "h-4 w-4",
  size = 24,
  strokeWidth = 2,
  color = "currentColor",
  ...props
}, ref) => {
  return (
    <svg
      ref={ref}
      className={className}
      width={size}
      height={size}
      viewBox="0 0 440 440"
      fill={color}
      xmlns="http://www.w3.org/2000/svg"
      style={{
        filter: 'drop-shadow(0.5px 0 0 currentColor) drop-shadow(-0.5px 0 0 currentColor) drop-shadow(0 0.5px 0 currentColor) drop-shadow(0 -0.5px 0 currentColor)',
        fontWeight: 'bold'
      }}
      {...props}
    >
      {/* Turkish Lira symbol (₺) - Bold version using CSS filter */}
      <path d="M344.33,212.5c0,103.857-80.577,189.248-182.5,196.936V197.361l151.76-55.236l-10.26-28.191l-141.5,51.502V121.38 l151.76-55.236l-10.26-28.191l-141.5,51.502V0h-30v100.374l-66.16,24.08l10.261,28.191L131.83,132.3v44.055l-66.16,24.08 l10.261,28.191l55.899-20.346V440h15c60.813,0,117.957-23.651,160.902-66.597c42.946-42.946,66.598-100.089,66.598-160.903H344.33z" />
    </svg>
  );
});

TurkishLiraIcon.displayName = "TurkishLiraIcon";

export default TurkishLiraIcon;
