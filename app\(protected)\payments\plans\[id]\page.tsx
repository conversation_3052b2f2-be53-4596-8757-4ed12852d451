import { notFound } from 'next/navigation';
import { getPaymentPlanById, getPaymentPlanAssignedAthletesCount } from '@/lib/db/actions';
import PaymentPlanDetailsClient from './client';

interface PaymentPlanDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PaymentPlanDetailsPage({ params }: PaymentPlanDetailsPageProps) {
  const { id } = await params;
  
  const [paymentPlan, assignedAthletesCount] = await Promise.all([
    getPaymentPlanById(id),
    getPaymentPlanAssignedAthletesCount(id)
  ]);

  if (!paymentPlan) {
    notFound();
  }

  return (
    <PaymentPlanDetailsClient 
      paymentPlan={paymentPlan} 
      assignedAthletesCount={assignedAthletesCount}
    />
  );
}
