'use client';

import {
  Users,
  School,
  BarChart3,
  User,
  CreditCard,
  MapPin,
  AlertCircle,
} from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { BadgeStatus } from "@/components/ui/badge-status";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

const COLORS = ["hsl(var(--chart-1))", "hsl(var(--chart-2))", "hsl(var(--chart-3))", "hsl(var(--chart-4))", "hsl(var(--chart-5))"];

export function FinancialOverviewChart({ data } : any) {
  const { t } = useSafeTranslation();

  return (
    <Card className="col-span-1 md:col-span-2">
      <CardHeader>
        <CardTitle className="text-lg font-medium">{t("dashboard.financialOverview.title")}</CardTitle>
        <CardDescription>{t("dashboard.financialOverview.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-muted/40 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">{t("dashboard.financialOverview.totalIncome")}</p>
            <p className="text-2xl font-bold">{(data?.totalIncome || 0).toFixed(2)} TL</p>
          </div>
          <div className="bg-muted/40 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">{t("dashboard.financialOverview.totalExpenses")}</p>
            <p className="text-2xl font-bold">{(data?.totalExpenses || 0).toFixed(2)} TL</p>
          </div>
          <div className="bg-muted/40 p-4 rounded-lg">
            <p className="text-sm text-muted-foreground mb-1">{t("dashboard.financialOverview.netBalance")}</p>
            <p className={`text-2xl font-bold ${(data?.balance || 0) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {(data?.balance || 0).toFixed(2)} TL
            </p>
          </div>
        </div>
        
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={(data?.incomeByMonth || []).map((inc : any, i : any) => ({
                name: t(`common.months.${inc.month.toLowerCase()}`),
                income: inc.amount,
                expenses: (data?.expensesByMonth || [])[i]?.amount || 0,
              }))}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip 
                labelFormatter={(label) => label}
                formatter={(value, name) => [
                  `${value} TL`,
                  name === t('dashboard.financialOverview.income') ? t('dashboard.financialOverview.income') : t('dashboard.financialOverview.expenses')
                ]}
              />
              <Area 
                type="monotone" 
                dataKey="income" 
                name={t('dashboard.financialOverview.income')}
                stackId="1"
                stroke="hsl(var(--chart-1))" 
                fill="hsl(var(--chart-1))" 
              />
              <Area 
                type="monotone" 
                dataKey="expenses" 
                name={t('dashboard.financialOverview.expenses')}
                stackId="2"
                stroke="hsl(var(--chart-2))" 
                fill="hsl(var(--chart-2))" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

export function ExpenseBreakdownChart({ data } : any) {
  const { t } = useSafeTranslation();

  // Transform data to include translated category names
  const translatedData = (data?.expensesByCategory || []).map((item: any) => ({
    ...item,
    category: t(`expenses.categories.${item.category}`)
  }));

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{t("dashboard.expenseBreakdown.title")}</CardTitle>
        <CardDescription>{t("dashboard.expenseBreakdown.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={translatedData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="amount"
                nameKey="category"
              >
                {translatedData.map((entry: any, index: number) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [`${value} TL`, name]}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

export function IncomeSourcesChart({ data }: { data: any }) {
  const { t } = useSafeTranslation();

  // Transform data to include translated category names
  const categoryMap: Record<string, string> = {
    'Monthly Fees': 'payments.frequency.monthly',
    'Quarterly Fees': 'payments.frequency.quarterly', 
    'Equipment Sales': 'dashboard.incomeSources.categories.equipmentSales',
    'Registration Fees': 'dashboard.incomeSources.categories.registrationFees',
    'Private Lessons': 'dashboard.incomeSources.categories.privateLessons',
    'Tournaments': 'dashboard.incomeSources.categories.tournaments',
    'Other Fees': 'dashboard.incomeSources.categories.otherFees',
    'Other': 'expenses.categories.other'
  };

  const translatedData = (data?.incomeByCategory || []).map((item: any) => ({
    ...item,
    category: t(categoryMap[item.category] || item.category),
    originalCategory: item.category // Keep original for debugging
  }));

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{t("dashboard.incomeSources.title")}</CardTitle>
        <CardDescription>{t("dashboard.incomeSources.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-48">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={translatedData}
              margin={{
                top: 5,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="category" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  `${value} TL`,
                  t("dashboard.incomeSources.amount")
                ]}
                labelFormatter={(label) => `${t("dashboard.incomeSources.category")}: ${label}`}
              />
              <Bar dataKey="amount" fill="hsl(var(--chart-1))" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

export function OverduePaymentsList({ athletes }: { athletes: any[] }) {
  const { t } = useSafeTranslation();

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
          {t("dashboard.overduePayments.title")}
        </CardTitle>
        <CardDescription>{t("dashboard.overduePayments.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {athletes.length === 0 ? (
            <p className="text-muted-foreground text-sm">{t("dashboard.overduePayments.noOverdue")}</p>
          ) : (
            athletes.slice(0, 4).map((athlete: any) => (
              <div key={athlete.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="ml-4">
                    <p className="text-sm font-medium">{athlete.name} {athlete.surname}</p>
                    <p className="text-xs text-muted-foreground">
                      {athlete.parentName} {athlete.parentSurname}
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium text-red-500">
                    {Math.abs(parseFloat(athlete.balance)).toFixed(2)} TL
                  </span>
                  <BadgeStatus status={athlete.status} className="ml-2" />
                </div>
              </div>
            ))
          )}
          
          {athletes.length > 4 && (
            <p className="text-sm text-muted-foreground text-center">
              +{athletes.length - 4} {t("dashboard.overduePayments.overdueMore")}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}