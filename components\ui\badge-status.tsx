"use client";

import { cn } from "@/lib/utils";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

export interface BadgeStatusProps {
  status: "active" | "suspended" | "inactive" | "completed" | "pending" | "overdue" | "cancelled";
  type?: "athlete" | "payment" | "item";
  className?: string;
}

export function BadgeStatus({ status, type = "athlete", className }: BadgeStatusProps) {
  const { t } = useSafeTranslation();
  
  const statusClasses = {
    active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    suspended: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100",
    inactive: "bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-100",
    completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    pending: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
    overdue: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    cancelled: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-100",
  };

  // Map status to translation key based on type
  const getStatusTranslation = (status: string) => {
    if (type === "athlete") {
      return t(`common.status.${status}`);
    } else if (type === "payment") {
      return t(`payments.status.${status}`);
    } else if (type === "item") {
      return t(`items.status.${status}`);
    } else {
      // Default to common status translations
      return t(`common.status.${status}`);
    }
  };

  return (
    <span
      className={cn(
        "inline-block px-2 py-1 text-xs font-medium rounded-full capitalize",
        statusClasses[status],
        className
      )}
    >
      {getStatusTranslation(status)}
    </span>
  );
}