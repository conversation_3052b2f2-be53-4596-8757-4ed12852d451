import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateInstructorData {
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: string;
}

export interface UpdateInstructorData {
  name?: string;
  surname?: string;
  email?: string;
  phone?: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: string;
}

export class InstructorService extends BaseService {
  constructor() {
    super('InstructorService');
  }

  /**
   * Get all instructors
   */
  async getInstructors(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getInstructors',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getInstructors(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructors',
      }
    );
  }

  /**
   * Get instructors with pagination and filtering
   */
  async getInstructorsPaginated(
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: {
        name?: string;
        surname?: string;
        email?: string;
        phone?: string;
        branchId?: string;
        schoolId?: string;
      };
    },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getInstructorsPaginated',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getInstructorsPaginated(effectiveTenantId || undefined, options);
      },
      {
        userId,
        tenantId,
        resource: 'instructors',
        metadata: { operation: 'paginated', options },
      }
    );
  }

  /**
   * Get instructor by ID
   */
  async getInstructorById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getInstructorById',
      'Instructor',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getInstructorById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructor',
      }
    );
  }

  /**
   * Create a new instructor
   */
  async createInstructor(
    data: CreateInstructorData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: CreateInstructorData): ValidationError | null => {
        if (!data.name || data.name.trim().length === 0) {
          return { field: 'name', message: 'Name is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.name.length < 2 || data.name.length > 255) {
          return { field: 'name', message: 'Name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateInstructorData): ValidationError | null => {
        if (!data.surname || data.surname.trim().length === 0) {
          return { field: 'surname', message: 'Surname is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        if (data.surname.length < 2 || data.surname.length > 255) {
          return { field: 'surname', message: 'Surname must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
        }
        return null;
      },
      (data: CreateInstructorData): ValidationError | null => {
        if (!data.email || data.email.trim().length === 0) {
          return { field: 'email', message: 'Email is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
          return { field: 'email', message: 'Email must be a valid email address', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (data: CreateInstructorData): ValidationError | null => {
        if (!data.phone || data.phone.trim().length === 0) {
          return { field: 'phone', message: 'Phone is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
        if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
          return { field: 'phone', message: 'Phone must be a valid phone number', code: 'INVALID_FORMAT' };
        }
        return null;
      },
      (data: CreateInstructorData): ValidationError | null => {
        if (data.nationalId && data.nationalId.length > 0) {
          if (data.nationalId.length !== 11 || !/^\d{11}$/.test(data.nationalId)) {
            return { field: 'nationalId', message: 'National ID must be 11 digits', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: CreateInstructorData): ValidationError | null => {
        if (data.salary && data.salary.length > 0) {
          const salaryValue = parseFloat(data.salary);
          if (isNaN(salaryValue) || salaryValue < 0) {
            return { field: 'salary', message: 'Salary must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'createInstructor',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check for duplicate email
        const existingInstructor = await TenantAwareDB.getInstructorByEmail(validatedData.email, effectiveTenantId || undefined);
        if (existingInstructor) {
          throw new BusinessRuleError('duplicate_instructor_email', 'An instructor with this email already exists');
        }

        // Check for duplicate national ID if provided
        if (validatedData.nationalId) {
          const existingByNationalId = await TenantAwareDB.getInstructorByNationalId(validatedData.nationalId, effectiveTenantId || undefined);
          if (existingByNationalId) {
            throw new BusinessRuleError('duplicate_instructor_national_id', 'An instructor with this national ID already exists');
          }
        }

        // Process the data to handle empty strings for optional fields
        const processedData = {
          ...validatedData,
          nationalId: validatedData.nationalId && validatedData.nationalId.trim() !== '' ? validatedData.nationalId : undefined,
          birthDate: validatedData.birthDate && validatedData.birthDate.trim() !== '' ? validatedData.birthDate : undefined,
          address: validatedData.address && validatedData.address.trim() !== '' ? validatedData.address : undefined,
          salary: validatedData.salary && validatedData.salary.trim() !== '' ? validatedData.salary : undefined,
        };

        return TenantAwareDB.createInstructor(processedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructor',
      }
    );
  }

  /**
   * Update an instructor
   */
  async updateInstructor(
    id: string,
    data: UpdateInstructorData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const validationFunctions = [
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.name !== undefined) {
          if (!data.name || data.name.trim().length === 0) {
            return { field: 'name', message: 'Name cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.name.length < 2 || data.name.length > 255) {
            return { field: 'name', message: 'Name must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.surname !== undefined) {
          if (!data.surname || data.surname.trim().length === 0) {
            return { field: 'surname', message: 'Surname cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          if (data.surname.length < 2 || data.surname.length > 255) {
            return { field: 'surname', message: 'Surname must be between 2 and 255 characters', code: 'INVALID_LENGTH' };
          }
        }
        return null;
      },
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.email !== undefined) {
          if (!data.email || data.email.trim().length === 0) {
            return { field: 'email', message: 'Email cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(data.email)) {
            return { field: 'email', message: 'Email must be a valid email address', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.phone !== undefined) {
          if (!data.phone || data.phone.trim().length === 0) {
            return { field: 'phone', message: 'Phone cannot be empty', code: 'REQUIRED_FIELD_MISSING' };
          }
          const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
          if (!phoneRegex.test(data.phone.replace(/\s/g, ''))) {
            return { field: 'phone', message: 'Phone must be a valid phone number', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.nationalId !== undefined && data.nationalId && data.nationalId.length > 0) {
          if (data.nationalId.length !== 11 || !/^\d{11}$/.test(data.nationalId)) {
            return { field: 'nationalId', message: 'National ID must be 11 digits', code: 'INVALID_FORMAT' };
          }
        }
        return null;
      },
      (data: UpdateInstructorData): ValidationError | null => {
        if (data.salary !== undefined && data.salary && data.salary.length > 0) {
          const salaryValue = parseFloat(data.salary);
          if (isNaN(salaryValue) || salaryValue < 0) {
            return { field: 'salary', message: 'Salary must be a valid positive number', code: 'INVALID_VALUE' };
          }
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'updateInstructor',
      data,
      validationFunctions,
      async (validatedData) => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if instructor exists
        const existingInstructor = await TenantAwareDB.getInstructorById(id, effectiveTenantId || undefined);
        if (!existingInstructor) {
          throw new NotFoundError('Instructor not found');
        }

        // Check for duplicate email (excluding current instructor)
        if (validatedData.email) {
          const duplicateEmail = await TenantAwareDB.getInstructorByEmail(validatedData.email, effectiveTenantId || undefined);
          if (duplicateEmail && duplicateEmail.id !== id) {
            throw new BusinessRuleError('duplicate_instructor_email', 'An instructor with this email already exists');
          }
        }

        // Check for duplicate national ID (excluding current instructor)
        if (validatedData.nationalId) {
          const duplicateNationalId = await TenantAwareDB.getInstructorByNationalId(validatedData.nationalId, effectiveTenantId || undefined);
          if (duplicateNationalId && duplicateNationalId.id !== id) {
            throw new BusinessRuleError('duplicate_instructor_national_id', 'An instructor with this national ID already exists');
          }
        }

        // Process the data to handle empty strings for optional fields
        const processedData = {
          ...validatedData,
          nationalId: validatedData.nationalId !== undefined ? 
            (validatedData.nationalId && validatedData.nationalId.trim() !== '' ? validatedData.nationalId : null) : 
            undefined,
          birthDate: validatedData.birthDate !== undefined ? 
            (validatedData.birthDate && validatedData.birthDate.trim() !== '' ? validatedData.birthDate : null) : 
            undefined,
          address: validatedData.address !== undefined ? 
            (validatedData.address && validatedData.address.trim() !== '' ? validatedData.address : null) : 
            undefined,
          salary: validatedData.salary !== undefined ? 
            (validatedData.salary && validatedData.salary.trim() !== '' ? validatedData.salary : null) : 
            undefined,
        };

        return TenantAwareDB.updateInstructor(id, processedData, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructor',
      }
    );
  }

  /**
   * Update instructor branches
   */
  async updateInstructorBranches(
    instructorId: string,
    branchIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<void>> {
    return this.executeOperation(
      'updateInstructorBranches',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if instructor exists
        const existingInstructor = await TenantAwareDB.getInstructorById(instructorId, effectiveTenantId || undefined);
        if (!existingInstructor) {
          throw new NotFoundError('Instructor not found');
        }

        await TenantAwareDB.updateInstructorBranches(instructorId, branchIds, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructorBranches',
        metadata: { instructorId, branchIds },
      }
    );
  }

  /**
   * Update instructor schools
   */
  async updateInstructorSchools(
    instructorId: string,
    schoolIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<void>> {
    return this.executeOperation(
      'updateInstructorSchools',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if instructor exists
        const existingInstructor = await TenantAwareDB.getInstructorById(instructorId, effectiveTenantId || undefined);
        if (!existingInstructor) {
          throw new NotFoundError('Instructor not found');
        }

        await TenantAwareDB.updateInstructorSchools(instructorId, schoolIds, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'instructorSchools',
        metadata: { instructorId, schoolIds },
      }
    );
  }

  /**
   * Delete an instructor
   */
  async deleteInstructor(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteInstructor',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if instructor exists
        const existingInstructor = await TenantAwareDB.getInstructorById(id, effectiveTenantId || undefined);
        if (!existingInstructor) {
          throw new NotFoundError('Instructor not found');
        }

        // Check for dependent teams
        const teams = await TenantAwareDB.getTeamsByInstructorId(id, effectiveTenantId || undefined);
        if (teams && teams.length > 0) {
          throw new BusinessRuleError('has_dependent_teams', 'Cannot delete instructor with assigned teams');
        }

        await TenantAwareDB.deleteInstructor(id, effectiveTenantId || undefined);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'instructor',
      }
    );
  }
}

// Factory function
export const instructorService = () => new InstructorService();
