"use server";

import { revalidatePath } from 'next/cache';
import { itemPurchaseService } from '../services/item-purchase.service';

// Create an instance of the service
const itemPurchaseServiceInstance = () => itemPurchaseService();

// Item Purchases
export async function getItemPurchases() {
  try {
    // Use the service
    const service = itemPurchaseServiceInstance();
    const result = await service.getItemPurchases();

    if (!result.success) {
      console.error("getItemPurchases error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get item purchases");
    }

    return result.data;
  } catch (error) {
    console.error("Error getting item purchases:", error);
    throw error;
  }
}

export async function createItemSale(data: {
  itemId: string;
  athleteId: string;
  quantity: number;
  paymentStatus?: 'pending' | 'completed' | 'overdue';
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card' | null;
  billingDate?: string;
  dueDate?: string;
}) {
  try {
    // Use the service
    const service = itemPurchaseServiceInstance();
    const result = await service.createItemSale(data);

    if (!result.success) {
      console.error("createItemSale error:", result.error);
      if(result.error?.details && result.error.details.rule){
        return { success: false, error: result.error?.details?.rule , errorType: result.error?.cause?.name };
      }
      return { success: false, error: result.error?.userMessage || 'Failed to create item sale' , errorType: 'general' };
    }else{
      revalidatePath('/item-purchases');
      revalidatePath('/items');
      revalidatePath('/athletes');
      return { success: true, purchase: result.data.purchase, payment: result.data.payment };
    }
  } catch (error) {
    console.error("createItemSale error:", error);
    return { success: false, error: error instanceof Error ? error.message : "Failed to create item sale", errorType: 'general' };
  }
}