"use client";

import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Edit, Package2, ShoppingCart, Calendar, User, Trash2 } from "lucide-react";
import Link from "next/link";
import { SecureImage } from "@/components/ui/secure-image";
import { Item } from "@/lib/types";
import { format } from "date-fns";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { deleteItem } from "@/lib/db/actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

interface ItemDetailClientProps {
  item: Item;
}

export default function ItemDetailClient({ item }: ItemDetailClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteItem(item.id);
      toast({
        title: t('items.messages.deleteSuccess'),
        description: item.name + ' ' + t('items.messages.hasBeenDeleted'),
        variant: "default"
      });
      router.push('/items');
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('items.messages.deleteError'),
        variant: "destructive"
      });
      console.error("Error deleting item:", error);
    }
  };

  const getStockStatus = (stock: number) => {
    if (stock === 0) return { 
      label: t('items.status.outOfStock'), 
      variant: "destructive" as const,
      className: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
    };
    if (stock < 5) return { 
      label: t('items.status.lowStock'), 
      variant: "secondary" as const,
      className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
    };
    return { 
      label: t('items.status.inStock'), 
      variant: "default" as const,
      className: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
    };
  };

  const stockStatus = getStockStatus(item.stock);
  const formattedPrice = parseFloat(item.price);

  return (
    <div className="container mx-auto py-6">
      {/* Navigation */}
      <Button variant="ghost" className="mb-6" asChild>
        <Link href="/items">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Link>
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Image Section */}
        <Card>
          <CardContent className="p-6">
            <div className="relative aspect-square">
              <SecureImage
                src={item.image || ""}
                alt={item.name}
                fill
                className="object-cover rounded-lg"
                priority
                placeholderIcon={Package2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Details Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl">{item.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="capitalize">
                      {t(`items.categories.${item.category}`)}
                    </Badge>
                    <Badge className={stockStatus.className}>
                      {stockStatus.label}
                    </Badge>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold">
                    {formattedPrice.toFixed(2)} {t('common.currency')}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {item.description && (
                <div>
                  <h3 className="font-semibold mb-2">{t('items.details.description')}</h3>
                  <p className="text-muted-foreground">{item.description}</p>
                </div>
              )}

              <Separator />

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Package2 className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{t('items.details.stock')}:</span>
                  <span className="font-medium">{item.stock}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{t('common.createdAt')}:</span>
                  <span className="font-medium">{format(new Date(item.createdAt), 'MMM dd, yyyy')}</span>
                </div>
              </div>

              <Separator />

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild className="flex-1">
                  <Link href={`/items/${item.id}/sell`}>
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    {t('items.actions.sellItem')}
                  </Link>
                </Button>
                
                <Button variant="outline" asChild className="flex-1">
                  <Link href={`/items/${item.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t('items.actions.editItem')}
                  </Link>
                </Button>

                <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="flex-1">
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t('items.actions.deleteItem')}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{t('common.actions.confirm')}</AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('items.messages.deleteConfirm')} &quot;{item.name}&quot;
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('common.actions.cancel')}</AlertDialogCancel>
                      <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        onClick={handleDelete}
                      >
                        {t('items.actions.deleteItem')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
