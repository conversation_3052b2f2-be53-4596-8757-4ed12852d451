"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditCard } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface AthletePaymentPlan {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

interface PaymentPlanViewProps {
  athletePaymentPlans: AthletePaymentPlan[];
}

export function PaymentPlanView({ athletePaymentPlans }: PaymentPlanViewProps) {
  const { t } = useSafeTranslation();

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  return (
    <div className="space-y-3">
      {athletePaymentPlans.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground">
          {t('payments.plans.assignments.noAssignments')}
        </div>
      ) : (
        athletePaymentPlans.map((plan) => (
          <Card key={plan.id}>
            <CardContent className="py-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{plan.planName}</h4>
                  <Badge variant={plan.isActive ? "default" : "secondary"}>
                    {plan.isActive ? t('common.status.active') : t('common.status.inactive')}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatCurrency(plan.monthlyValue)} • {t('payments.plans.assignDay')}: {plan.assignDay} • {t('payments.plans.dueDay')}: {plan.dueDay}
                </div>
                {plan.teamName && (
                  <div className="text-sm text-muted-foreground">
                    {t('teams.details.team')}: {plan.teamName}
                  </div>
                )}
                <div className="text-xs text-muted-foreground">
                  {t('payments.plans.assignments.assignedDate')}: {new Date(plan.assignedDate).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );
}
