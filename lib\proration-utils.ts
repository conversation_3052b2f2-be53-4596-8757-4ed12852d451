/**
 * Utility functions for calculating prorated payment amounts
 */

/**
 * Calculate prorated amount for remaining days in the current month
 * @param monthlyAmount - The full monthly payment amount
 * @param startDate - The date to start proration from (defaults to today)
 * @returns The prorated amount for remaining days
 */
export function calculateProratedAmount(
  monthlyAmount: number,
  startDate: Date = new Date()
): number {
  const currentDate = new Date(startDate);
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  
  // Get total days in the current month
  const totalDaysInMonth = new Date(year, month + 1, 0).getDate();
  
  // Get remaining days (including today)
  const currentDay = currentDate.getDate();
  const remainingDays = totalDaysInMonth - currentDay + 1;
  
  // Calculate daily rate and multiply by remaining days
  const dailyRate = monthlyAmount / totalDaysInMonth;
  const proratedAmount = dailyRate * remainingDays;
  
  return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
}

/**
 * Get the number of remaining days in the current month (including today)
 * @param startDate - The date to calculate from (defaults to today)
 * @returns Number of remaining days
 */
export function getRemainingDaysInMonth(startDate: Date = new Date()): number {
  const currentDate = new Date(startDate);
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  
  // Get total days in the current month
  const totalDaysInMonth = new Date(year, month + 1, 0).getDate();
  
  // Get remaining days (including today)
  const currentDay = currentDate.getDate();
  const remainingDays = totalDaysInMonth - currentDay + 1;
  
  return remainingDays;
}

/**
 * Get total days in the current month
 * @param date - The date to get the month from (defaults to today)
 * @returns Total days in the month
 */
export function getTotalDaysInMonth(date: Date = new Date()): number {
  const year = date.getFullYear();
  const month = date.getMonth();
  return new Date(year, month + 1, 0).getDate();
}
