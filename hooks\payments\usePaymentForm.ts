import { useState } from "react";

export type PaymentStatus = "pending" | "completed" | "overdue" | "cancelled";
export type PaymentType = "fee" | "equipment" | "other";
export type PaymentMethod = "cash" | "bank_transfer" | "credit_card" | null;

export interface PaymentFormData {
  athleteId: string;
  amount: string;
  date: string;
  dueDate: string;
  status: PaymentStatus;
  type: PaymentType;
  method: string; // Use string in form, convert to PaymentMethod when submitting
  description: string;
}

interface PaymentData {
  id: string;
  athleteId: string;
  amount: string;
  date: string;
  dueDate: string;
  status: PaymentStatus;
  type: PaymentType;
  method: PaymentMethod;
  description?: string | null;
}

interface UsePaymentFormProps {
  initialPayment: PaymentData;
}

export function usePaymentForm({ initialPayment }: UsePaymentFormProps) {
  const [formData, setFormData] = useState<PaymentFormData>({
    athleteId: initialPayment.athleteId,
    amount: initialPayment.amount,
    date: initialPayment.date,
    dueDate: initialPayment.dueDate,
    status: initialPayment.status,
    type: initialPayment.type,
    method: initialPayment.method || "",
    description: initialPayment.description || "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const updateField = (field: keyof PaymentFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.athleteId) {
      newErrors.athleteId = "Please select an athlete";
    }
    if (!formData.amount || isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    }
    if (!formData.date) {
      newErrors.date = "Please select a payment date";
    }
    if (!formData.dueDate) {
      newErrors.dueDate = "Please select a due date";
    }
    if (formData.date && formData.dueDate && new Date(formData.date) > new Date(formData.dueDate)) {
      newErrors.dueDate = "Due date cannot be before payment date";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const resetForm = () => {
    setFormData({
      athleteId: initialPayment.athleteId,
      amount: initialPayment.amount,
      date: initialPayment.date,
      dueDate: initialPayment.dueDate,
      status: initialPayment.status,
      type: initialPayment.type,
      method: initialPayment.method || "",
      description: initialPayment.description || "",
    });
    setErrors({});
  };

  return {
    formData,
    errors,
    updateField,
    validateForm,
    resetForm,
    setFormData,
  };
}
