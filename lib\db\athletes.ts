import { eq, and, like, ilike, count, desc, asc, or, inArray, sql } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class AthletesDB extends TenantAwareDBBase {
  
  static async getAthletesPaginated(
    tenantId?: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: {
        name?: string;
        surname?: string;
        parentEmail?: string;
        parentPhone?: string;
        nationalId?: string;
        status?: 'active' | 'inactive' | 'suspended';
      };
    } = { page: 1, limit: 10 }
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const { page, limit, search, sortBy = 'createdAt', sortOrder = 'desc', filters = {} } = options;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [eq(schema.athletes.tenantId, filter.tenantId)];

    // Add search condition
    if (search) {
      whereConditions.push(
        or(
          ilike(schema.athletes.name, `%${search}%`),
          ilike(schema.athletes.surname, `%${search}%`),
          ilike(schema.athletes.parentEmail, `%${search}%`),
          ilike(schema.athletes.parentPhone, `%${search}%`),
          ilike(schema.athletes.nationalId, `%${search}%`)
        )!
      );
    }

    // Add column-based filters
    if (filters.name) {
      whereConditions.push(ilike(schema.athletes.name, `%${filters.name}%`));
    }
    if (filters.surname) {
      whereConditions.push(ilike(schema.athletes.surname, `%${filters.surname}%`));
    }
    if (filters.parentEmail) {
      whereConditions.push(ilike(schema.athletes.parentEmail, `%${filters.parentEmail}%`));
    }
    if (filters.parentPhone) {
      whereConditions.push(ilike(schema.athletes.parentPhone, `%${filters.parentPhone}%`));
    }
    if (filters.nationalId) {
      whereConditions.push(ilike(schema.athletes.nationalId, `%${filters.nationalId}%`));
    }
    if (filters.status) {
      whereConditions.push(eq(schema.athletes.status, filters.status));
    }

    const whereClause = whereConditions.length > 1 ? and(...whereConditions) : whereConditions[0];

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(schema.athletes)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;

    // Determine sort column and order
    let orderByClause;
    switch (sortBy) {
      case 'name':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.name) : desc(schema.athletes.name);
        break;
      case 'surname':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.surname) : desc(schema.athletes.surname);
        break;
      case 'parentEmail':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.parentEmail) : desc(schema.athletes.parentEmail);
        break;
      case 'parentPhone':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.parentPhone) : desc(schema.athletes.parentPhone);
        break;
      case 'status':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.status) : desc(schema.athletes.status);
        break;
      case 'balance':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.balance) : desc(schema.athletes.balance);
        break;
      case 'birthDate':
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.birthDate) : desc(schema.athletes.birthDate);
        break;
      case 'createdAt':
      default:
        orderByClause = sortOrder === 'asc' ? asc(schema.athletes.createdAt) : desc(schema.athletes.createdAt);
        break;
    }

    // Get paginated athletes
    const athletes = await db
      .select()
      .from(schema.athletes)
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Get team relationships for the paginated athletes
    let teamRelationships: any[] = [];
    if (athletes.length > 0) {
      const athleteIds = athletes.map((athlete: any) => athlete.id);
      teamRelationships = await db.select({
        athleteId: schema.athleteTeams.athleteId,
        teamId: schema.athleteTeams.teamId,
      })
      .from(schema.athleteTeams)
      .where(inArray(schema.athleteTeams.athleteId, athleteIds));
    }

    // Combine athletes with their team relationships
    const athletesWithTeams = athletes.map((athlete: any) => ({
      ...athlete,
      teams: teamRelationships
        .filter(rel => rel.athleteId === athlete.id)
        .map(rel => rel.teamId),
    }));

    return {
      data: athletesWithTeams,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  static async getAthletes(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const athletes = await db.select().from(schema.athletes)
      .where(eq(schema.athletes.tenantId, filter.tenantId));

    // Get team relationships for all athletes
    const athleteIds = athletes.map((athlete: any) => athlete.id);
    let teamRelationships: any[] = [];
    if (athleteIds.length > 0) {
      teamRelationships = await db.select({
        athleteId: schema.athleteTeams.athleteId,
        teamId: schema.athleteTeams.teamId,
      })
      .from(schema.athleteTeams)
      .where(inArray(schema.athleteTeams.athleteId, athleteIds));
    }

    // Combine athletes with their team relationships
    return athletes.map((athlete: any) => ({
      ...athlete,
      teams: teamRelationships
        .filter(rel => rel.athleteId === athlete.id)
        .map(rel => rel.teamId),
    }));
  }

  static async getAthleteById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.athletes)
      .where(and(
        eq(schema.athletes.id, id),
        eq(schema.athletes.tenantId, filter.tenantId)
      ));
    
    if (!result.length) {
      return null;
    }

    const athlete = result[0];

    // Get teams for this athlete with team details
    const athleteTeams = await db.select({
      teamId: schema.athleteTeams.teamId,
      teamName: schema.teams.name,
      branchName: schema.branches.name,
      branchId: schema.teams.branchId,
      schoolName: schema.schools.name,
      schoolId: schema.teams.schoolId,
      instructorName: schema.instructors.name,
      instructorSurname: schema.instructors.surname,
      joinedAt: schema.athleteTeams.joinedAt,
      leftAt: schema.athleteTeams.leftAt,
    })
    .from(schema.athleteTeams)
    .innerJoin(schema.teams, eq(schema.athleteTeams.teamId, schema.teams.id))
    .innerJoin(schema.branches, eq(schema.teams.branchId, schema.branches.id))
    .innerJoin(schema.schools, eq(schema.teams.schoolId, schema.schools.id))
    .innerJoin(schema.instructors, eq(schema.teams.instructorId, schema.instructors.id))
    .where(
      and(
        eq(schema.athleteTeams.athleteId, athlete.id),
        eq(schema.teams.tenantId, filter.tenantId)
      )
    );

    return {
      ...athlete,
      teams: athleteTeams.map((at: any) => at.teamId),
      teamDetails: athleteTeams,
    };
  }

  static async getAthleteByNationalId(nationalId:string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.athletes)
        .where(and(
            eq(schema.athletes.nationalId, nationalId),
            eq(schema.athletes.tenantId, filter.tenantId)
        ));
    if(result.length > 0){
      return result[0];
    }
    return null;
  }

  static async getAthletesByTeamId(teamId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    // Get athlete IDs for the team
    const athleteTeamRelations = await db.select({
      athleteId: schema.athleteTeams.athleteId,
    })
    .from(schema.athleteTeams)
    .where(eq(schema.athleteTeams.teamId, teamId));

    if (athleteTeamRelations.length === 0) {
      return [];
    }

    const athleteIds = athleteTeamRelations.map(rel => rel.athleteId);

    // Get athlete details
    const athletes = await db.select().from(schema.athletes)
      .where(and(
        inArray(schema.athletes.id, athleteIds),
        eq(schema.athletes.tenantId, filter.tenantId)
      ));

    return athletes;
  }

  static async getAthletesByTeamIdWithContactDetails(teamId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    return db.select({
      id: schema.athletes.id,
      name: schema.athletes.name,
      surname: schema.athletes.surname,
      parentPhone: schema.athletes.parentPhone,
      parentEmail: schema.athletes.parentEmail,
      parentName: schema.athletes.parentName,
      parentSurname: schema.athletes.parentSurname,
      status: schema.athletes.status,
    })
    .from(schema.athletes)
    .innerJoin(schema.athleteTeams, eq(schema.athletes.id, schema.athleteTeams.athleteId))
    .where(and(
      eq(schema.athleteTeams.teamId, teamId),
      eq(schema.athletes.tenantId, filter.tenantId),
      eq(schema.athletes.status, 'active')
    ))
    .orderBy(schema.athletes.name, schema.athletes.surname);
  }

  static async getAthletesByIdsWithContactDetails(athleteIds: string[], tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    return db.select({
      id: schema.athletes.id,
      name: schema.athletes.name,
      surname: schema.athletes.surname,
      parentPhone: schema.athletes.parentPhone,
      parentEmail: schema.athletes.parentEmail,
      parentName: schema.athletes.parentName,
      parentSurname: schema.athletes.parentSurname,
      status: schema.athletes.status,
    })
    .from(schema.athletes)
    .where(and(
      inArray(schema.athletes.id, athleteIds),
      eq(schema.athletes.tenantId, filter.tenantId),
      eq(schema.athletes.status, 'active')
    ))
    .orderBy(schema.athletes.name, schema.athletes.surname);
  }

  static async getAllAthletesWithContactDetails(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    return db.select({
      id: schema.athletes.id,
      name: schema.athletes.name,
      surname: schema.athletes.surname,
      parentPhone: schema.athletes.parentPhone,
      parentEmail: schema.athletes.parentEmail,
      parentName: schema.athletes.parentName,
      parentSurname: schema.athletes.parentSurname,
      status: schema.athletes.status,
    })
    .from(schema.athletes)
    .where(and(
      eq(schema.athletes.tenantId, filter.tenantId),
      eq(schema.athletes.status, 'active')
    ))
    .orderBy(schema.athletes.name, schema.athletes.surname);
  }

  static async getOverdueAthletes(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.athletes)
      .where(and(
        eq(schema.athletes.tenantId, filter.tenantId),
        sql`CAST(${schema.athletes.balance} AS DECIMAL) < 0`
      ))
      .orderBy(schema.athletes.balance);
  }

  static async createAthlete(
    data: Omit<typeof schema.athletes.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.athletes, data, tenantId, userId);
  }

  static async updateAthlete(
    id: string, 
    data: Partial<typeof schema.athletes.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.athletes, id, data, tenantId, userId);
  }

  static async deleteAthlete(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // First remove the athlete from all teams
    await db.delete(schema.athleteTeams)
      .where(eq(schema.athleteTeams.athleteId, id));

    // Then delete the athlete
    const result = await db.delete(schema.athletes)
      .where(and(
        eq(schema.athletes.id, id),
        eq(schema.athletes.tenantId, filter.tenantId)
      ))
      .returning();

    return result[0];
  }

  static async addAthleteToTeam(
    athleteId: string, 
    teamId: string, 
    joinedAt?: Date
  ) {
    const result = await db.insert(schema.athleteTeams).values({
      athleteId,
      teamId,
      joinedAt: joinedAt ? joinedAt.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    }).returning();

    return result[0];
  }

  static async removeAthleteFromTeam(
    athleteId: string, 
    teamId: string
  ) {
    await db.delete(schema.athleteTeams)
      .where(and(
        eq(schema.athleteTeams.athleteId, athleteId),
        eq(schema.athleteTeams.teamId, teamId)
      ));
  }

  static async hasAthleteNotPaidPayment(athleteId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);

    const result = await db
      .select()
      .from(schema.athletes)
      .innerJoin(schema.payments, eq(schema.athletes.id, schema.payments.athleteId))
      .where(
        and(
          eq(schema.athletes.id, athleteId),
          eq(schema.athletes.tenantId, filter.tenantId),
          eq(schema.payments.athleteId, athleteId),
          eq(schema.payments.status, 'pending')
        )
      );

    return result.length > 0;
  }
}
