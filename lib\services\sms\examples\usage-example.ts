/**
 * SMS Service Usage Examples
 * 
 * This file demonstrates how to use the SMS service with different providers
 */

import { smsService } from '@/lib/services';
import { SmsService } from '@/lib/services/sms.service';
import { MockSmsProvider, NetGsmProvider } from '@/lib/services/sms';

/**
 * Example 1: Basic SMS sending using the default service
 */
export async function basicSmsExample() {
  try {
    const result = await smsService().sendSms({
      senderIdentifier: 'MYCOMPANY',
      encoding: 'UTF-8',
      messages: [
        {
          receiver: '+**********',
          message: 'Hello! This is a test message from our sports club management system.'
        },
        {
          receiver: '+**********',
          message: 'Welcome to our club! Your membership has been activated.'
        }
      ],
      metadata: {
        campaign: 'welcome_messages',
        source: 'registration_system'
      }
    });

    if (result.success) {
      console.log('SMS sent successfully!');
      console.log('Sent count:', result.data?.sentCount);
      console.log('Message IDs:', result.data?.messageIds);
    } else {
      console.error('SMS failed:', result.error?.userMessage);
      if (result.validationErrors) {
        console.error('Validation errors:', result.validationErrors);
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

/**
 * Example 2: Using SMS service with Mock provider for testing
 */
export async function mockProviderExample() {
  // Create a custom SMS service with Mock provider
  const testSmsService = new SmsService({
    provider: new MockSmsProvider({ 
      shouldFail: false, 
      delay: 100 
    }),
    defaultSender: 'TESTCLUB',
    defaultEncoding: 'UTF-8',
    enabled: true
  });

  try {
    const result = await testSmsService.sendSms({
      messages: [
        {
          receiver: '+**********',
          message: 'This is a test message using Mock provider'
        }
      ]
    });

    console.log('Mock SMS result:', result);
  } catch (error) {
    console.error('Mock SMS error:', error);
  }
}

/**
 * Example 3: Using SMS service with NetGSM provider (production)
 */
export async function netgsmProviderExample() {
  // Create a custom SMS service with NetGSM provider
  const productionSmsService = new SmsService({
    provider: new NetGsmProvider({
      username: process.env.NETGSM_USERNAME,
      password: process.env.NETGSM_PASSWORD,
      appname: process.env.NETGSM_APPNAME // Optional
    }),
    defaultSender: 'SPORTSCLUB',
    defaultEncoding: 'UTF-8',
    enabled: true
  });

  try {
    const result = await productionSmsService.sendSms({
      messages: [
        {
          receiver: '**********', // Turkish phone number format (without +90)
          message: 'Spor kulübümüze hoş geldiniz! Üyeliğiniz aktif edilmiştir.'
        }
      ]
    });

    if (result.success) {
      console.log('NetGSM SMS sent successfully!');
      console.log('Job ID:', result.data?.messageIds?.[0]);
      console.log('Provider data:', result.data?.providerData);
    } else {
      console.error('NetGSM SMS failed:', result.error);
    }
  } catch (error) {
    console.error('NetGSM SMS error:', error);
  }
}

/**
 * Example 4: Check SMS service status
 */
export async function checkServiceStatus() {
  try {
    const status = await smsService().getServiceStatus();
    
    if (status.success) {
      console.log('SMS Service Status:');
      console.log('- Enabled:', status.data?.enabled);
      console.log('- Provider:', status.data?.provider);
      console.log('- Configured:', status.data?.configured);
      console.log('- Default Sender:', status.data?.defaultSender);
      console.log('- Default Encoding:', status.data?.defaultEncoding);
    } else {
      console.error('Failed to get service status:', status.error);
    }
  } catch (error) {
    console.error('Error checking service status:', error);
  }
}

/**
 * Example 5: Error handling and validation
 */
export async function errorHandlingExample() {
  try {
    // This will fail validation - empty messages array
    const result = await smsService().sendSms({
      senderIdentifier: 'TEST',
      messages: []
    });

    if (!result.success) {
      console.log('Expected validation error occurred:');
      console.log('Validation errors:', result.validationErrors);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }

  try {
    // This will fail validation - invalid phone number
    const result = await smsService().sendSms({
      senderIdentifier: 'TEST',
      messages: [
        {
          receiver: 'invalid-phone',
          message: 'Test message'
        }
      ]
    });

    if (!result.success) {
      console.log('Expected validation error for invalid phone:');
      console.log('Validation errors:', result.validationErrors);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

/**
 * Example 6: Bulk SMS sending
 */
export async function bulkSmsExample() {
  // Generate multiple messages for bulk sending
  const messages = [
    { receiver: '+**********', message: 'Training session tomorrow at 10 AM' },
    { receiver: '+1234567891', message: 'Training session tomorrow at 10 AM' },
    { receiver: '+1234567892', message: 'Training session tomorrow at 10 AM' },
    { receiver: '+1234567893', message: 'Training session tomorrow at 10 AM' },
    { receiver: '+1234567894', message: 'Training session tomorrow at 10 AM' }
  ];

  try {
    const result = await smsService().sendSms({
      senderIdentifier: 'SPORTSCLUB',
      encoding: 'UTF-8',
      messages: messages,
      metadata: {
        campaign: 'training_reminder',
        batch_size: messages.length
      }
    });

    if (result.success) {
      console.log(`Bulk SMS sent successfully!`);
      console.log(`Total messages: ${result.data?.totalCount}`);
      console.log(`Sent: ${result.data?.sentCount}`);
      console.log(`Failed: ${result.data?.failedCount}`);
    }
  } catch (error) {
    console.error('Bulk SMS error:', error);
  }
}

/**
 * Example 7: Advanced NetGSM usage with error handling
 */
export async function advancedNetgsmExample() {
  const netgsmService = new SmsService({
    provider: new NetGsmProvider({
      username: process.env.NETGSM_USERNAME,
      password: process.env.NETGSM_PASSWORD,
      appname: 'SportsClubApp'
    }),
    defaultSender: 'SPORTSCLUB',
    defaultEncoding: 'UTF-8',
    enabled: true
  });

  try {
    // Check service status first
    const status = await netgsmService.getServiceStatus();
    console.log('NetGSM Service Status:', status.data);

    if (!status.data?.configured) {
      console.error('NetGSM is not properly configured');
      return;
    }

    // Send SMS with Turkish characters
    const result = await netgsmService.sendSms({
      senderIdentifier: 'SPOR',
      encoding: 'UTF-8', // Will be mapped to 'TR' for NetGSM
      messages: [
        {
          receiver: '**********',
          message: 'Merhaba! Antrenman yarın saat 10:00\'da başlayacak. Lütfen zamanında gelin. 🏃‍♂️'
        }
      ],
      metadata: {
        campaign: 'training_notification',
        type: 'reminder'
      }
    });

    if (result.success) {
      console.log('✅ SMS sent successfully!');
      console.log('📱 Messages sent:', result.data?.sentCount);
      console.log('🆔 NetGSM Job ID:', result.data?.messageIds?.[0]);
      console.log('📊 Provider response:', result.data?.providerData);
    } else {
      console.error('❌ SMS failed:', result.error?.userMessage);
      console.error('🔍 Error details:', result.error?.details);

      if (result.validationErrors) {
        console.error('📝 Validation errors:', result.validationErrors);
      }
    }
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Export all examples for easy testing
export const smsExamples = {
  basicSmsExample,
  mockProviderExample,
  netgsmProviderExample,
  checkServiceStatus,
  errorHandlingExample,
  bulkSmsExample,
  advancedNetgsmExample
};
