"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { UserMinus, CreditCard, AlertTriangle } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { removeAthleteFromTeamWithCleanup, getAthleteTeamPaymentPlans } from "@/lib/actions/athlete-team-management";
import {useToast} from "@/hooks/use-toast";

interface AthletePaymentPlan {
  id: string;
  planName: string;
  monthlyValue: string;
  assignedDate: string;
  isActive: boolean;
}

interface RemoveAthleteFromTeamDialogProps {
  athleteId: string;
  athleteName: string;
  athleteSurname: string;
  teamId: string;
  teamName: string;
  onAthleteRemoved?: () => void;
  triggerComponent?: React.ReactNode;
}

export function RemoveAthleteFromTeamDialog({
  athleteId,
  athleteName,
  athleteSurname,
  teamId,
  teamName,
  onAthleteRemoved,
  triggerComponent
}: RemoveAthleteFromTeamDialogProps) {
  const { t } = useSafeTranslation();
  const { toast} = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [removePaymentPlans, setRemovePaymentPlans] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [teamPaymentPlans, setTeamPaymentPlans] = useState<AthletePaymentPlan[]>([]);
  const [loadingPaymentPlans, setLoadingPaymentPlans] = useState(false);

  // Load payment plans when dialog opens
  const handleOpenChange = async (open: boolean) => {
    setIsOpen(open);
    
    if (open) {
      setLoadingPaymentPlans(true);
      try {
        const plans = await getAthleteTeamPaymentPlans(athleteId, teamId);
        setTeamPaymentPlans(plans);
      } catch (error) {
        console.error("Error loading payment plans:", error);
        setTeamPaymentPlans([]);
      } finally {
        setLoadingPaymentPlans(false);
      }
    }
  };

  const handleRemoveAthlete = async () => {
    setIsLoading(true);
    try {
      const result = await removeAthleteFromTeamWithCleanup({
        athleteId,
        teamId,
        removePaymentPlans
      });

      if (result.success) {
        let successMessage = t('teams.management.athleteRemovedSuccess', {
          athleteName: `${athleteName} ${athleteSurname}`,
          teamName
        });

        if (removePaymentPlans && teamPaymentPlans.length > 0) {
          successMessage += ` ${t('teams.management.paymentPlansDeactivated', {
            count: teamPaymentPlans.length
          })}`;
        }
        toast({
          title: t('common.success'),
          description: successMessage
        });
        setIsOpen(false);
        
        // Notify parent component
        onAthleteRemoved?.();
      } else {
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'teams.management.removeAthleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error removing athlete from team:", error);
      toast({
        title: t('common.error'),
        description: t('teams.management.removeAthleteError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <UserMinus className="h-4 w-4 mr-2" />
      {t('teams.management.removeAthlete')}
    </Button>
  );

  return (
    <AlertDialog open={isOpen} onOpenChange={handleOpenChange}>
      <AlertDialogTrigger asChild>
        {triggerComponent || defaultTrigger}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            {t('teams.management.removeAthleteFromTeam')}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {t('teams.management.removeAthleteConfirmation', {
              athleteName: `${athleteName} ${athleteSurname}`,
              teamName
            })}
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Payment Plans Section */}
          {loadingPaymentPlans ? (
            <div className="space-y-2">
              <div className="h-4 bg-muted animate-pulse rounded" />
              <div className="h-16 bg-muted animate-pulse rounded" />
            </div>
          ) : teamPaymentPlans.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="removePaymentPlans"
                  checked={removePaymentPlans}
                  onCheckedChange={(checked) => setRemovePaymentPlans(checked === true)}
                />
                <Label htmlFor="removePaymentPlans" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  {t('teams.management.removePaymentPlans')} ({teamPaymentPlans.length})
                </Label>
              </div>

              <div className="ml-6 space-y-2">
                <p className="text-sm text-muted-foreground">
                  {t('teams.management.activePaymentPlansForTeam')}:
                </p>
                <div className="bg-muted/50 p-3 rounded-md space-y-1">
                  {teamPaymentPlans.map((plan) => (
                    <div key={plan.id} className="flex justify-between items-center text-sm">
                      <span>{plan.planName}</span>
                      <span className="font-medium">{formatCurrency(plan.monthlyValue)}</span>
                    </div>
                  ))}
                </div>
                {removePaymentPlans && (
                  <p className="text-sm text-orange-600">
                    {t('teams.management.paymentPlansWillBeDeactivated')}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <CreditCard className="h-4 w-4" />
              {t('teams.management.noActivePaymentPlans')}
            </div>
          )}
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>
            {t('common.actions.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleRemoveAthlete}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700"
          >
            {isLoading ? t('common.removing') : t('common.actions.remove')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
