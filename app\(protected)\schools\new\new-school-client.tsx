"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Upload } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { SecureImage } from "@/components/ui/secure-image";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { createSchool, getBranches } from "@/lib/actions";
import { uploadImage } from "@/lib/file-uploads";
import { useToast } from "@/hooks/use-toast";
import { Branch } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

const MAX_FILE_SIZE = 5000000; // 5MB
const ACCEPTED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

export default function NewSchoolClient() {
  const router = useRouter();
  const { t } = useTranslation(['shared', 'schools']);
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);

  // Cleanup object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Load branches on component mount
  useEffect(() => {
    async function loadBranches() {
      try {
        setIsLoadingBranches(true);
        const branchData = await getBranches();
        setBranches(branchData);
      } catch (error) {
        console.error("Failed to load branches:", error);
        toast({
          title: t('schools.messages.createError'),
          description: t('schools.messages.branchLoadError'),
          variant: "destructive",
        });
      } finally {
        setIsLoadingBranches(false);
      }
    }
    loadBranches();
  }, [toast, t]);

  // Define schema inside component to avoid FileList issues during SSR
  const formSchema = z.object({
    name: z.string().min(2, t('common.validation.minLength', { min: 2, ns: 'shared' })),
    foundedYear: z.number()
      .min(1800, t('common.validation.yearMin', { min: 1800, ns: 'shared' }))
      .max(new Date().getFullYear(), t('common.validation.yearMax', { ns: 'shared' })),
    address: z.string().min(5, t('common.validation.minLength', { min: 5, ns: 'shared' })),
    phone: z.string().min(10, t('common.validation.phoneMin', { min: 10, ns: 'shared' })).optional(),
    email: z.string().email(t('common.validation.email', { ns: 'shared' })).optional(),
    branches: z.array(z.string()).min(1, t('common.validation.selectAtLeastOne', { field: t('common.fields.branch', { ns: 'shared' }), ns: 'shared' })),
    logo: z
      .any()
      .optional()
      .refine(
        (files) => {
          if (typeof window === 'undefined') return true; // Skip validation during SSR
          return !files || files.length === 0 || files[0]?.size <= MAX_FILE_SIZE;
        },
        t('common.validation.maxFileSize', { size: '5MB', ns: 'shared' })
      )
      .refine(
        (files) => {
          if (typeof window === 'undefined') return true; // Skip validation during SSR
          return !files || files.length === 0 || ACCEPTED_IMAGE_TYPES.includes(files[0]?.type);
        },
        t('common.validation.unsupportedFormat', { formats: '.jpg, .jpeg, .png, .webp', ns: 'shared' })
      ),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      foundedYear: new Date().getFullYear(),
      address: "",
      phone: "",
      email: "",
      branches: [],
    },
  });

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Clean up previous object URL if it exists
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      let logoPath: string | undefined;

      // Upload image only when form is submitted to avoid storing unnecessary files
      if (values.logo && values.logo.length > 0) {
        const formData = new FormData();
        formData.append('file', values.logo[0]);
        logoPath = await uploadImage(formData);
      }

      // Create school with server action  
      await createSchool({
        name: values.name,
        foundedYear: values.foundedYear,
        address: values.address,
        phone: values.phone || undefined,
        email: values.email || undefined,
        logo: logoPath,
        branches: values.branches,
      });

      toast({
        title: t('schools.actions.createSchool', { ns: 'schools' }),
        description: t('schools.messages.createSuccess', { ns: 'schools' }),
      });

      router.push("/schools");
    } catch (error) {
      console.error("Failed to create school:", error);
      toast({
        title: t('schools.messages.createError'),
        description: error instanceof Error ? error.message : 'Unknown error occurred',
        variant: "destructive",
      });
    }
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">        <Button variant="ghost" asChild>
          <Link href="/schools" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            {t('schools.actions.backToSchools', { ns: 'schools' })}
          </Link>
        </Button>
      </div>
      <Card>
        <CardHeader>
          <CardTitle>{t('schools.new', { ns: 'schools' })}</CardTitle>
          <CardDescription>{t('schools.placeholders.newSchoolDescription', { ns: 'schools' })}</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="logo"
                    render={({ field: { onChange, value, ...field } }) => (
                      <FormItem>
                        <FormLabel>{t('schools.details.logo', { ns: 'schools' })}</FormLabel>
                        <FormControl>
                          <div className="flex flex-col items-center justify-center w-full">
                            <label
                              htmlFor="logo"
                              className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/40 hover:bg-muted/60 transition-colors"
                            >
                              {previewUrl ? (
                                <div className="relative w-full h-full">
                                  <SecureImage
                                    src={previewUrl}
                                    alt="Preview"
                                    fill
                                    className="object-contain p-4"
                                  />
                                </div>
                              ) : (
                                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                  <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                                  <p className="mb-2 text-sm text-muted-foreground">
                                    {t('schools.placeholders.uploadImage', { ns: 'schools' })}
                                  </p>
                                  <p className="text-xs text-muted-foreground">
                                    {t('schools.placeholders.imageFormats', { ns: 'schools' })}
                                  </p>
                                </div>
                              )}
                              <Input
                                id="logo"
                                type="file"
                                accept="image/png,image/jpeg,image/webp"
                                className="hidden"
                                onChange={(e) => {
                                  onChange(e.target.files);
                                  handleImageChange(e);
                                }}
                                {...field}
                              />
                            </label>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('schools.details.name', { ns: 'schools' })}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('schools.placeholders.enterName', { ns: 'schools' })} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('schools.details.address', { ns: 'schools' })}</FormLabel>
                        <FormControl>
                          <Input placeholder={t('schools.placeholders.enterAddress', { ns: 'schools' })} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="foundedYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('schools.details.foundedYear', { ns: 'schools' })}</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="2000" 
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('schools.details.phone', { ns: 'schools' })}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('schools.placeholders.enterPhone', { ns: 'schools' })} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('schools.details.email', { ns: 'schools' })}</FormLabel>
                          <FormControl>
                            <Input placeholder={t('schools.placeholders.enterEmail', { ns: 'schools' })} type="email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="branches"
                    render={() => (
                      <FormItem>
                        <FormLabel>{t('schools.details.branches', { ns: 'schools' })}</FormLabel>
                        <FormControl>
                          <div className="grid grid-cols-2 gap-4 max-h-40 overflow-y-auto border rounded-md p-4">
                            {isLoadingBranches ? (
                              <div className="col-span-2 text-center text-muted-foreground py-4">
                                {t('common.loading', { ns: 'shared' })}
                              </div>
                            ) : branches.length === 0 ? (
                              <div className="col-span-2 text-center text-muted-foreground py-4">
                                {t('schools.messages.noBranchesAvailable', { ns: 'schools' })}
                              </div>
                            ) : (
                              branches.map((branch) => (
                                <FormField
                                  key={branch.id}
                                  control={form.control}
                                  name="branches"
                                  render={({ field }) => {
                                    return (
                                      <FormItem
                                        key={branch.id}
                                        className="flex flex-row items-start space-x-3 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            checked={field.value?.includes(branch.id!)}
                                            onCheckedChange={(checked) => {
                                              return checked
                                                ? field.onChange([...field.value, branch.id])
                                                : field.onChange(
                                                    field.value?.filter(
                                                      (value) => value !== branch.id
                                                    )
                                                  )
                                            }}
                                          />
                                        </FormControl>
                                        <FormLabel className="font-normal">
                                          {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                                        </FormLabel>
                                      </FormItem>
                                    )
                                  }}
                                />
                              ))
                            )}
                          </div>
                        </FormControl>
                        <div className="text-sm text-muted-foreground">
                          {t('schools.placeholders.selectBranches', { ns: 'schools' })}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit">{t('schools.actions.createSchool', { ns: 'schools' })}</Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
