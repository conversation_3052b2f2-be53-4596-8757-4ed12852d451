"use server";

import { revalidatePath } from 'next/cache';
import { itemPurchaseService } from '../../services/item-purchase.service';

// Create an instance of the service
const itemPurchaseServiceInstance = () => itemPurchaseService();

// Item Purchases
export async function getItemPurchases() {
  try {
    // Use the service
    const service = itemPurchaseServiceInstance();
    const result = await service.getItemPurchases();

    if (!result.success) {
      console.error("getItemPurchases error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to get item purchases");
    }

    return result.data;
  } catch (error) {
    console.error("Error getting item purchases:", error);
    throw error;
  }
}

export async function createItemPurchase(data: {
  itemId: string;
  athleteId: string;
  quantity: number;
  totalPrice: string;
  purchaseDate: string;
  status?: string;
}) {
  try {
    // Use the service
    const service = itemPurchaseServiceInstance();
    const result = await service.createItemPurchase({
      ...data,
      status: data.status as 'pending' | 'completed' | 'cancelled' | undefined
    });

    if (!result.success) {
      console.error("createItemPurchase error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to create item purchase");
    }

    revalidatePath('/item-purchases');
    revalidatePath('/items');
    return { success: true, purchase: result.data };
  } catch (error) {
    console.error("createItemPurchase error:", error);
    throw error;
  }
}

export async function createItemSale(data: {
  itemId: string;
  athleteId: string;
  quantity: number;
  paymentStatus?: 'pending' | 'completed' | 'overdue';
}) {
  try {
    // Use the service
    const service = itemPurchaseServiceInstance();
    const result = await service.createItemSale(data);

    if (!result.success) {
      console.error("createItemSale error:", result.error);
      throw new Error(result.error?.userMessage || "Failed to create item sale");
    }

    revalidatePath('/item-purchases');
    revalidatePath('/items');
    revalidatePath('/athletes');
    return { success: true, purchase: result.data.purchase, payment: result.data.payment };
  } catch (error) {
    console.error("createItemSale error:", error);
    throw error;
  }
}