"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Upload, School as SchoolIcon } from "lucide-react";
import { SecureImage } from "@/components/ui/secure-image";
import { updateSchool, getBranches } from "@/lib/actions";
import { uploadImage } from "@/lib/file-uploads";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useToast } from "@/hooks/use-toast";
import { School, Branch } from "@/lib/types";
import { Checkbox } from "@/components/ui/checkbox";

interface SchoolEditClientProps {
  school: School;
}

export function SchoolEditClient({ school: initialSchool }: SchoolEditClientProps) {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [selectedBranches, setSelectedBranches] = useState<string[]>(
    initialSchool.branches?.map(b => b.id!).filter(Boolean) || []
  );
  const [school, setSchool] = useState({
    name: initialSchool.name,
    address: initialSchool.address || "",
    phone: initialSchool.phone || "",
    email: initialSchool.email || "",
    logo: initialSchool.logo || "",
    foundedYear: initialSchool.foundedYear,
  });
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialSchool.logo || null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageError, setImageError] = useState(false);

  // Cleanup object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Load branches on component mount
  useEffect(() => {
    async function loadBranches() {
      try {
        setIsLoadingBranches(true);
        const branchData = await getBranches();
        setBranches(branchData);
      } catch (error) {
        console.error("Failed to load branches:", error);
        toast({
          title: t('common.error'),
          description: t('schools.messages.branchLoadError'),
          variant: "destructive",
        });
      } finally {
        setIsLoadingBranches(false);
      }
    }
    loadBranches();
  }, [toast, t]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: t('common.error'),
          description: t('common.upload.maxSize', { size: '5MB' }),
          variant: "destructive",
        });
        return;
      }

      // Clean up previous object URL if it exists
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }

      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setSelectedFile(file);
      setImageError(false); // Reset error state when new file is selected
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      let logoPath: string | undefined = school.logo;

      // Upload image only when form is submitted to avoid storing unnecessary files
      if (selectedFile) {
        const formData = new FormData();
        formData.append('file', selectedFile);
        logoPath = await uploadImage(formData);
      }
      
      await updateSchool(initialSchool.id, {
        name: school.name,
        address: school.address || undefined,
        phone: school.phone || undefined,
        email: school.email || undefined,
        logo: logoPath || undefined,
        foundedYear: school.foundedYear,
        branches: selectedBranches,
      });
      
      toast({
        title: t('common.success'),
        description: t("schools.messages.updateSuccess"),
      });
      
      router.push("/schools");
      router.refresh(); // Refresh to show updated data
    } catch (error) {
      console.error("Error updating school:", error);
      toast({
        title: t('common.error'),
        description: t("schools.messages.updateError"),
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <Button
        variant="ghost"
        className="mb-6"
        onClick={() => router.push("/schools")}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        {t('schools.actions.backToSchools')}
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>{t('schools.edit')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>{t('schools.details.logo')}</Label>
                <div className="flex flex-col items-center justify-center w-full">
                  <label
                    htmlFor="logo"
                    className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/40 hover:bg-muted/60 transition-colors relative overflow-hidden"
                  >
                    {previewUrl && !imageError ? (
                      <div className="relative w-full h-full">
                        <SecureImage
                          src={previewUrl}
                          alt="Preview"
                          fill
                          className="object-contain p-4"
                          onError={handleImageError}
                          placeholderIcon={SchoolIcon}
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity">
                          <p className="text-white flex items-center">
                            <Upload className="w-6 h-6 mr-2" />
                            {t('schools.placeholders.changeImage')}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          {t('schools.placeholders.uploadImage')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {t('schools.placeholders.imageFormats')}
                        </p>
                      </div>
                    )}
                    <Input
                      id="logo"
                      type="file"
                      accept="image/png,image/jpeg,image/webp"
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  </label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">{t('schools.details.name')}</Label>
                <Input
                  id="name"
                  value={school.name}
                  onChange={(e) =>
                    setSchool((prev) => ({ ...prev, name: e.target.value }))
                  }
                  placeholder={t('schools.placeholders.enterName')}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="foundedYear">{t('schools.details.foundedYear')}</Label>
                <Input
                  id="foundedYear"
                  type="number"
                  value={school.foundedYear}
                  onChange={(e) =>
                    setSchool((prev) => ({ ...prev, foundedYear: parseInt(e.target.value) || 0 }))
                  }
                  placeholder={t('schools.placeholders.enterFoundedYear')}
                  min="1900"
                  max={new Date().getFullYear()}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">{t('schools.details.address')}</Label>
                <Input
                  id="address"
                  value={school.address}
                  onChange={(e) =>
                    setSchool((prev) => ({ ...prev, address: e.target.value }))
                  }
                  placeholder={t('schools.placeholders.enterAddress')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{t('schools.details.phone')}</Label>
                <Input
                  id="phone"
                  value={school.phone}
                  onChange={(e) =>
                    setSchool((prev) => ({ ...prev, phone: e.target.value }))
                  }
                  placeholder={t('schools.placeholders.enterPhone')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">{t('schools.details.email')}</Label>
                <Input
                  id="email"
                  type="email"
                  value={school.email}
                  onChange={(e) =>
                    setSchool((prev) => ({ ...prev, email: e.target.value }))
                  }
                  placeholder={t('schools.placeholders.enterEmail')}
                />
              </div>

              <div className="space-y-2">
                <Label>{t('schools.details.branches')}</Label>
                <div className="text-sm text-muted-foreground">
                  {t('schools.placeholders.selectBranches', { ns: 'schools' })}
                </div>
                <div className="grid grid-cols-2 gap-4 max-h-40 overflow-y-auto border rounded-md p-4">
                  {isLoadingBranches ? (
                    <div className="col-span-2 text-center text-muted-foreground py-4">
                      {t('common.loading', { ns: 'shared' })}
                    </div>
                  ) : branches.length === 0 ? (
                    <div className="col-span-2 text-center text-muted-foreground py-4">
                      {t('schools.messages.noBranchesAvailable')}
                    </div>
                  ) : (
                    branches.map((branch) => (
                      <div key={branch.id} className="flex flex-row items-start space-x-3 space-y-0">
                        <Checkbox
                          checked={selectedBranches.includes(branch.id!)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedBranches(prev => [...prev, branch.id!]);
                            } else {
                              setSelectedBranches(prev => prev.filter(id => id !== branch.id));
                            }
                          }}
                        />
                        <Label className="font-normal">
                          {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                        </Label>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/schools")}
                disabled={isSubmitting}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? t('common.actions.saving') : t('schools.actions.saveChanges')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
