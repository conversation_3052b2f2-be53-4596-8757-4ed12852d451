import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';

export class BranchService extends BaseService {
  constructor() {
    super('BranchService');
  }

  async getBranches(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getBranches',
      async () => {
        return await TenantAwareDB.getBranches();
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }
}
