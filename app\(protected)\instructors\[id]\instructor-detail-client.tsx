"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, Mail, Phone, MapPin, Calendar, CreditCard, GraduationCap, Building } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Separator } from "@/components/ui/separator";
import {format} from "date-fns";

interface InstructorData {
  id: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string | null;
  birthDate?: string | null;
  address?: string | null;
  salary?: string | null;
  createdAt: Date;
  updatedAt: Date;
  branches: Array<{ id: string; name: string }>;
  schools: string[];
}

interface School {
  id: string;
  name: string;
  foundedYear: number;
}

interface InstructorDetailClientProps {
  instructor: InstructorData;
  schools?: School[];
}

export default function InstructorDetailClient({ instructor, schools = [] }: InstructorDetailClientProps) {
  const { t } = useSafeTranslation();

  // Create a map for quick school name lookup
  const schoolMap = schools.reduce((map, school) => {
    map[school.id] = school.name;
    return map;
  }, {} as Record<string, string>);

  return (
    <div className="container mx-auto px-4 py-6 max-w-6xl">
      <div className="mb-6">
        <Link href="/instructors">
          <Button variant="ghost" size="sm" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.actions.back')}
          </Button>
        </Link>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">{instructor.name} {instructor.surname}</h1>
            <p className="text-muted-foreground">{t('instructors.details.title')}</p>
          </div>
          <Link href={`/instructors/${instructor.id}/edit`}>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              {t('common.actions.edit')}
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                {t('instructors.details.personalInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.email')}</p>
                    <p className="font-medium">{instructor.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.phone')}</p>
                    <p className="font-medium">{instructor.phone}</p>
                  </div>
                </div>
              </div>

              {instructor.nationalId && (
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.nationalId')}</p>
                    <p className="font-medium">{instructor.nationalId}</p>
                  </div>
                </div>
              )}

              {instructor.birthDate && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.birthDate')}</p>
                    <p className="font-medium">{new Date(instructor.birthDate).toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit', year: 'numeric' })}</p>
                  </div>
                </div>
              )}

              {instructor.address && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.address')}</p>
                    <p className="font-medium">{instructor.address}</p>
                  </div>
                </div>
              )}

              {instructor.salary && (
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">{t('instructors.details.salary')}</p>
                    <p className="font-medium">{instructor.salary} TL</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Branches */}
          {instructor.branches.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {t('instructors.details.branches')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {instructor.branches.map((branch) => (
                    <Badge key={branch.id} variant="secondary">
                      {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Schools */}
          {instructor.schools.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <GraduationCap className="h-5 w-5" />
                  {t('instructors.details.schools')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {instructor.schools.map((schoolId) => (
                    <Badge key={schoolId} variant="outline">
                      {schoolMap[schoolId] || schoolId}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Side Information */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>{t('instructors.details.quickStats')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('instructors.details.branches')}</span>
                <span className="font-medium">{instructor.branches.length}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('instructors.details.schools')}</span>
                <span className="font-medium">{instructor.schools.length}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{t('common.dateCreated')}</span>
                <span className="font-medium text-sm">
                  {format(new Date(instructor.createdAt), 'dd.MM.yyyy')}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('common.actions.title')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Link href={`/instructors/${instructor.id}/edit`} className="block">
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-2" />
                  {t('common.actions.edit')}
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
