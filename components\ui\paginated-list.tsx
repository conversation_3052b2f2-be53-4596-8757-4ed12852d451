"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { PaginationData } from '@/hooks/use-paginated-list';

interface PaginationControlsProps {
  pagination: PaginationData;
  currentLimit: string;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: string) => void;
}

export function PaginationControls({
  pagination,
  currentLimit,
  onPageChange,
  onLimitChange,
}: PaginationControlsProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="flex items-center justify-between mt-6">
      <div className="text-sm text-muted-foreground">
        {t('common.pagination.showing', {
          start: (pagination.page - 1) * pagination.limit + 1,
          end: Math.min(pagination.page * pagination.limit, pagination.total),
          total: pagination.total
        })}
      </div>
      
      <div className="flex items-center space-x-4">
        {/* Page Size Selector */}
        <div className="flex items-center space-x-2">
          <Label>{t('common.pageSize')}:</Label>
          <Select value={currentLimit} onValueChange={onLimitChange}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Buttons */}
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page - 1)}
            disabled={!pagination.hasPreviousPage}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            {t('common.pagination.previous')}
          </Button>
          
          <div className="flex items-center space-x-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              let pageNum: number;
              if (pagination.totalPages <= 5) {
                pageNum = i + 1;
              } else if (pagination.page <= 3) {
                pageNum = i + 1;
              } else if (pagination.page >= pagination.totalPages - 2) {
                pageNum = pagination.totalPages - 4 + i;
              } else {
                pageNum = pagination.page - 2 + i;
              }
              
              return (
                <Button
                  key={pageNum}
                  variant={pagination.page === pageNum ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.page + 1)}
            disabled={!pagination.hasNextPage}
          >
            {t('common.pagination.next')}
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: () => void;
  placeholder?: string;
  isValid?: boolean;
  isSearching?: boolean;
  minLength?: number;
  showLoadingOnType?: boolean;
}

export function SearchBar({
  value,
  onChange,
  onSearch,
  placeholder,
  isValid = true,
  isSearching = false,
  minLength = 3,
  showLoadingOnType = false,
}: SearchBarProps) {
  const { t } = useSafeTranslation();

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch();
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className="relative flex-1">
        <Input
          placeholder={placeholder || t('common.search.placeholder')}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          className={!isValid ? "border-red-500" : ""}
        />
        {isSearching && showLoadingOnType && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>
      {onSearch && (
        <Button
          variant="outline"
          size="sm"
          onClick={onSearch}
          disabled={!isValid}
        >
          <Search className="h-4 w-4" />
        </Button>
      )}
      {!isValid && minLength > 0 && (
        <p className="text-sm text-red-500">
          {t('common.search.minLength', { count: minLength })}
        </p>
      )}
    </div>
  );
}

interface LoadingSkeletonProps {
  rows?: number;
  className?: string;
}

export function LoadingSkeleton({ rows = 5, className }: LoadingSkeletonProps) {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      {Array.from({ length: rows }, (_, i) => (
        <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
      ))}
    </div>
  );
}

interface ListHeaderProps {
  title: string;
  description?: string;
  actions?: React.ReactNode;
  searchBar?: React.ReactNode;
  filters?: React.ReactNode;
}

export function ListHeader({
  title,
  description,
  actions,
  searchBar,
  filters,
}: ListHeaderProps) {
  return (
    <div className="space-y-4">
      {/* Title and Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {description && (
            <p className="text-muted-foreground mt-2">{description}</p>
          )}
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </div>

      {/* Search and Filters */}
      {(searchBar || filters) && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {searchBar}
              {filters}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

interface PaginatedListContainerProps {
  children: React.ReactNode;
  isLoading?: boolean;
  loadingRows?: number;
  className?: string;
}

export function PaginatedListContainer({
  children,
  isLoading = false,
  loadingRows = 5,
  className,
}: PaginatedListContainerProps) {
  if (isLoading) {
    return <LoadingSkeleton rows={loadingRows} className={className} />;
  }

  return <div className={className}>{children}</div>;
}
