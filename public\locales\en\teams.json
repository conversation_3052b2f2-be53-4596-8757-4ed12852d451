{"teams": {"title": "Teams", "new": "New Team", "edit": "Edit Team", "details": {"name": "Team Name", "description": "Description", "team": "Team", "selectTeam": "Select a team", "school": "School", "branch": "Branch", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "schedule": "Training Schedule", "athletes": "Athletes", "moreAthletes": "more athletes", "viewAllAthletes": "View All Athletes", "noAthletes": "No athletes in this team yet", "addAthletes": "Add Athletes", "noSchedule": "No training schedule set up yet", "setupSchedule": "Set Up Schedule", "schoolAndInstructor": "School & Instructor", "downloadSchedule": "Download Schedule"}, "actions": {"viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "openMenu": "Open menu", "addNew": "Add New Team", "confirmDelete": "Confirm Delete"}, "messages": {"athleteCount": "{{count}} athlete", "createSuccess": "Team created successfully.", "createError": "Failed to create team.", "athleteCount_other": "{{count}} athletes", "updateSuccess": "Team updated successfully", "updateSuccessDetail": "Team information and training schedules have been saved successfully", "updateError": "Failed to update team", "updateErrorDetail": "Please try again or contact support", "conflictError": "Cannot save schedule with time conflicts", "conflictErrorDetail": "Please resolve all schedule conflicts before saving", "loadError": "Failed to load team data", "manageTeams": "Manage Teams", "deleteSuccess": "Team deleted successfully", "teamDeleted": "The team has been removed from the system", "deleteError": "Failed to delete team", "deleteFailed": "Unable to delete the team. Please try again.", "deleteConfirmation": "Are you sure you want to delete the team \"{{name}}\"? This action cannot be undone.", "scheduleOptional": "You can add training schedules now or later in the team edit page."}, "placeholders": {"enterName": "Enter team name", "enterDescription": "Enter description", "enterSchoolName": "Enter school name", "selectSchool": "Select a school", "selectBranch": "Select a branch", "selectInstructor": "Select an instructor", "searchTeams": "Search teams..."}, "schedule": {"addSlot": "Add Time Slot", "remove": "Remove", "day": "Day", "startTime": "Start Time", "endTime": "End Time", "facility": "Facility", "facilityAvailability": "Facility Availability", "conflictWarning": "This time slot conflicts with another team's schedule", "internalConflictWarning": "This time slot overlaps with another training session of this team", "selectFacilityToViewAvailability": "Select a facility to view availability", "conflictDetected": "Schedule Conflict Detected", "conflictWith": "Conflicts with {{team}}'s training schedule", "schedule": "Schedule", "noSchedulesThisDay": "No schedules for this day", "existingSchedules": "Existing schedules", "occupied": "Occupied", "weeklyOverview": "Weekly Overview", "available": "Available", "availableTimeSlots": "Available Time Slots"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "paymentPlan": {"bulkAssignment": "Team Payment Plan Assignment", "bulkAssignmentDescription": "Assign a payment plan to all {{count}} athletes in this team", "assignToAllAthletes": "Assign Payment Plan to All Athletes", "selectPaymentPlan": "Select Payment Plan for Team", "athletesCount": "{{count}} athletes", "assignmentWarning": "This will assign the selected payment plan to all athletes in this team. Athletes with existing active plans for this team will have their current plans replaced.", "conflictsDetected": "Payment Plan Conflicts Detected", "conflictsAndInactiveDetected": "Payment Plan Conflicts and Inactive Athletes Detected", "inactiveAthletesDetected": "Inactive Athletes Detected", "conflictsDescription": "Some athletes already have different payment plans assigned for this team:", "inactiveAthletesDescription": "Some athletes are inactive and will receive inactive payment plan assignments:", "willBeAssignedAsInactive": "Payment plan will be assigned as inactive", "currentPlan": "Current plan", "proceedDescription": "If you continue, these athletes' current payment plans for this team will be deactivated and replaced with the selected plan.", "proceedAnyway": "Continue Anyway", "bulkAssignSuccess": "Successfully assigned payment plan to {{count}} athletes ({{updated}} existing plans updated)", "bulkAssignError": "Failed to assign payment plan to team"}, "management": {"addAthlete": "Add Athlete", "removeAthlete": "Re<PERSON>ve At<PERSON>", "addAthleteToTeam": "Add Athlete to Team", "removeAthleteFromTeam": "<PERSON><PERSON><PERSON> from Team", "addAthleteDescription": "Add an athlete to {{teamName}} team", "removeAthleteConfirmation": "Are you sure you want to remove {{athleteN<PERSON>}} from {{teamName}}?", "selectAthlete": "Please select an athlete", "selectAthleteToAdd": "Select athlete to add", "selectPaymentPlan": "Please select a payment plan", "assignPaymentPlan": "Assign payment plan to athlete", "removePaymentPlans": "Remove payment plans", "noAvailableAthletes": "No available athletes to add", "noAvailablePaymentPlans": "No payment plans available for this team", "noPaymentPlansForBranch": "No payment plans are configured for this team's branch", "athleteAlreadyInTeam": "Athlete is already in this team", "athleteAddedSuccess": "{{athleteName}} has been added to {{teamName}}", "athleteRemovedSuccess": "{{athleteN<PERSON>}} has been removed from {{teamName}}", "withPaymentPlan": "with payment plan {{planName}}", "paymentPlansDeactivated": "and {{count}} payment plan(s) deactivated", "addAthleteError": "Failed to add athlete to team", "removeAthleteError": "Failed to remove athlete from team", "activePaymentPlansForTeam": "Active payment plans for this team", "paymentPlansWillBeDeactivated": "These payment plans will be deactivated", "noActivePaymentPlans": "No active payment plans for this team"}}}