"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Trash2, Plus, ToggleLeft, ToggleRight } from "lucide-react";
import { toast } from "sonner";
import { useSafeTranslation } from "@/hooks/use-safe-translation";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Team {
  id: string;
  name: string;
  branchId: string;
  branchName?: string;
  schoolId: string;
  schoolName?: string;
}

interface AthleteTeam {
  teamId: string;
  teamName: string;
  branchName: string;
  branchId: string;
  schoolName: string;
  schoolId: string;
  instructorName: string;
  instructorSurname: string;
  joinedAt: string;
  leftAt?: string | null;
}

interface AthletePaymentPlan {
  id: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string;
  isActive: boolean;
  lastPaymentDate?: string | null;
  planName: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  teamName?: string | null;
  athleteName: string;
  athleteSurname: string;
}

interface TeamPaymentPlanAssignmentProps {
  athleteId: string;
  athleteTeams: AthleteTeam[];
  availableTeams: Team[];
  athletePaymentPlans: AthletePaymentPlan[];
  availablePaymentPlans: PaymentPlan[];
  onAssignmentChange?: (assignments: AthletePaymentPlan[]) => void;
  readOnly?: boolean;
}

export function TeamPaymentPlanAssignment({ 
  athleteId,
  athleteTeams,
  availableTeams,
  athletePaymentPlans: initialPaymentPlans,
  availablePaymentPlans,
  onAssignmentChange,
  readOnly = false
}: TeamPaymentPlanAssignmentProps) {
  const { t } = useSafeTranslation();
  const [selectedTeamId, setSelectedTeamId] = useState("");
  const [selectedPlanId, setSelectedPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [localPaymentPlans, setLocalPaymentPlans] = useState<AthletePaymentPlan[]>(initialPaymentPlans);

  // Sync local state when initial payment plans change
  useEffect(() => {
    setLocalPaymentPlans(initialPaymentPlans);
  }, [initialPaymentPlans]);

  const handleAssignPaymentPlan = () => {
    if (!selectedPlanId || !selectedTeamId) {
      toast.error(t('payments.plans.assignments.selectRequired'));
      return;
    }

    // Check if athlete already has an active payment plan for this team
    const existingActivePlan = localPaymentPlans.find(
      plan => plan.teamId === selectedTeamId && plan.isActive
    );

    if (existingActivePlan) {
      // Deactivate existing plan for this team
      setLocalPaymentPlans(prev => 
        prev.map(plan => 
          plan.teamId === selectedTeamId && plan.isActive 
            ? { ...plan, isActive: false }
            : plan
        )
      );
    }

    // Find the selected plan details
    const selectedPlan = availablePaymentPlans.find(plan => plan.id === selectedPlanId);
    const selectedTeam = availableTeams.find(team => team.id === selectedTeamId);

    if (!selectedPlan || !selectedTeam) {
      toast.error(t('payments.plans.assignments.error'));
      return;
    }

    // Create new assignment (local only)
    const newAssignment: AthletePaymentPlan = {
      id: `temp-${Date.now()}`, // Temporary ID
      planId: selectedPlanId,
      teamId: selectedTeamId,
      assignedDate: new Date().toISOString().split('T')[0],
      isActive: true,
      lastPaymentDate: null,
      planName: selectedPlan.name,
      monthlyValue: selectedPlan.monthlyValue,
      assignDay: selectedPlan.assignDay,
      dueDay: selectedPlan.dueDay,
      teamName: selectedTeam.name,
      athleteName: "", // Will be filled by parent
      athleteSurname: ""
    };

    const updatedPlans = [...localPaymentPlans, newAssignment];
    setLocalPaymentPlans(updatedPlans);
    onAssignmentChange?.(updatedPlans);

    toast.success(t('payments.plans.assignments.success'));
    setIsDialogOpen(false);
    setSelectedPlanId("");
    setSelectedTeamId("");
  };

  const handleDeactivatePaymentPlan = (assignmentId: string) => {
    // Deactivate locally (not persisted until save)
    const updatedPlans = localPaymentPlans.map(plan => 
      plan.id === assignmentId ? { ...plan, isActive: false } : plan
    );
    setLocalPaymentPlans(updatedPlans);
    onAssignmentChange?.(updatedPlans);
    toast.success(t('payments.plans.assignments.deactivated'));
  };

  const handleActivatePaymentPlan = (assignmentId: string) => {
    // Check if there's already an active plan for the same team
    const planToActivate = localPaymentPlans.find(p => p.id === assignmentId);
    if (planToActivate?.teamId) {
      const existingActivePlan = localPaymentPlans.find(
        plan => plan.teamId === planToActivate.teamId && plan.isActive && plan.id !== assignmentId
      );

      if (existingActivePlan) {
        toast.error(t('payments.plans.assignments.oneActivePerTeam'));
        return;
      }
    }

    // Activate locally (not persisted until save)
    const updatedPlans = localPaymentPlans.map(plan => 
      plan.id === assignmentId ? { ...plan, isActive: true } : plan
    );
    setLocalPaymentPlans(updatedPlans);
    onAssignmentChange?.(updatedPlans);
    toast.success(t('payments.plans.assignments.activated'));
  };

  const handleDeletePaymentPlan = (assignmentId: string) => {
    // Remove completely from local state (not persisted until save)
    const updatedPlans = localPaymentPlans.filter(plan => plan.id !== assignmentId);
    setLocalPaymentPlans(updatedPlans);
    onAssignmentChange?.(updatedPlans);
    toast.success(t('payments.plans.assignments.deleted'));
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  // Filter payment plans based on selected team's branch
  const filteredPaymentPlans = selectedTeamId 
    ? availablePaymentPlans.filter(plan => {
        const selectedTeam = availableTeams.find(t => t.id === selectedTeamId);
        return plan.branches?.some(branch => branch.id === selectedTeam?.branchId) || !plan.branches?.length;
      })
    : availablePaymentPlans;

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">{t('payments.plans.assignments.title')}</h3>
        {!readOnly && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button type="button" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                {t('payments.plans.assignments.assign')}
              </Button>
            </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t('payments.plans.assignments.assignToTeam')}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>{t('teams.details.team')} *</Label>
                <Select value={selectedTeamId} onValueChange={setSelectedTeamId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('teams.details.selectTeam')} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTeams.map((team) => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name} ({t(`common.branches.${team.branchName}`, { ns: 'shared' })})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>{t('payments.plans.plan')} *</Label>
                <Select value={selectedPlanId} onValueChange={setSelectedPlanId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('payments.plans.select')} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredPaymentPlans.map((plan) => (
                      <SelectItem key={plan.id} value={plan.id}>
                        {plan.name} - {formatCurrency(plan.monthlyValue)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  {t('common.actions.cancel')}
                </Button>
                <Button
                  type="button"
                  onClick={handleAssignPaymentPlan}
                  disabled={isLoading || !selectedPlanId || !selectedTeamId}
                >
                  {isLoading ? t('common.actions.saving') : t('common.actions.assign')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
        )}
      </div>

      {/* Current Payment Plan Assignments */}
      <div className="space-y-3">
        {localPaymentPlans.length === 0 ? (
          <Card>
            <CardContent className="py-6 text-center text-muted-foreground">
              {t('payments.plans.assignments.noAssignments')}
            </CardContent>
          </Card>
        ) : (
          localPaymentPlans.map((plan) => (
            <Card key={plan.id}>
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{plan.planName}</h4>
                      <Badge variant={plan.isActive ? "default" : "secondary"}>
                        {plan.isActive ? t('common.status.active') : t('common.status.inactive')}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(plan.monthlyValue)} • {t('payments.plans.assignDay')}: {plan.assignDay} • {t('payments.plans.dueDay')}: {plan.dueDay}
                    </div>
                    {plan.teamName && (
                      <div className="text-sm text-muted-foreground">
                        {t('teams.details.team')}: {plan.teamName}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground">
                      {t('payments.plans.assignments.assignedDate')}: {new Date(plan.assignedDate).toLocaleDateString()}
                    </div>
                  </div>
                  {!readOnly && (
                    <div className="flex items-center gap-1">
                      {/* Toggle Active/Inactive */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => plan.isActive 
                          ? handleDeactivatePaymentPlan(plan.id)
                          : handleActivatePaymentPlan(plan.id)
                        }
                        className={plan.isActive 
                          ? "text-green-600 hover:text-green-700"
                          : "text-yellow-600 hover:text-yellow-700" 
                        }
                        title={plan.isActive 
                          ? t('payments.plans.assignments.deactivate')
                          : t('payments.plans.assignments.activate')
                        }
                      >
                        {plan.isActive ? (
                          <ToggleRight className="h-4 w-4" />
                        ) : (
                          <ToggleLeft className="h-4 w-4" />
                        )}
                      </Button>
                      
                      {/* Delete */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeletePaymentPlan(plan.id)}
                        className="text-red-600 hover:text-red-700"
                        title={t('payments.plans.assignments.delete')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
