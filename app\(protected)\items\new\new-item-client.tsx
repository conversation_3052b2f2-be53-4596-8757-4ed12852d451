"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Upload } from "lucide-react";
import Link from "next/link";
import { SecureImage } from "@/components/ui/secure-image";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createItem } from "@/lib/db/actions";
import { uploadImage } from "@/lib/file-uploads";
import { toast } from "sonner";

export default function NewItemClient() {
  const router = useRouter();
  const { t } = useSafeTranslation();
  const [loading, setLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  // Cleanup object URL when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(t('common.upload.maxSize', { size: '5MB' }));
        return;
      }
      
      // Clean up previous object URL if it exists
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      setImageFile(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const formData = new FormData(e.currentTarget);

      // Upload image only when form is submitted to avoid storing unnecessary files
      let imageUrl = "";
      if (imageFile) {
        const imageFormData = new FormData();
        imageFormData.append('file', imageFile);
        imageUrl = await uploadImage(imageFormData);
      }

      // Prepare item data
      const itemData = {
        name: formData.get("name") as string,
        description: formData.get("description") as string || undefined,
        price: formData.get("price") as string,
        category: formData.get("category") as "equipment" | "clothing" | "accessories" | "other",
        stock: parseInt(formData.get("stock") as string),
        image: imageUrl || undefined,
      };

      // Validate required fields
      if (!itemData.name || !itemData.price || !itemData.category) {
        toast.error(t('items.errors.requiredFields'));
        return;
      }

      // Create item in database
      await createItem(itemData);

      toast.success(t('items.messages.createSuccess'));
      router.push("/items");
      router.refresh();
    } catch (error) {
      console.error("Failed to create item:", error);
      toast.error(t('items.messages.createError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/items">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold tracking-tight">{t('items.new')}</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('items.details.information')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              {/* Image Upload Section */}
              <div className="space-y-2">
                <Label>{t('items.details.image')}</Label>
                <div className="flex flex-col items-center justify-center w-full">
                  <label
                    htmlFor="image"
                    className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/40 hover:bg-muted/60 transition-colors relative overflow-hidden"
                  >
                    {previewUrl ? (
                      <div className="relative w-full h-full">
                        <SecureImage
                          src={previewUrl}
                          alt="Preview"
                          fill
                          className="object-contain p-4"
                        />
                        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity">
                          <p className="text-white flex items-center">
                            <Upload className="w-6 h-6 mr-2" />
                            {t('items.actions.changeImage')}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-muted-foreground" />
                        <p className="mb-2 text-sm text-muted-foreground">
                          {t('items.placeholders.uploadImage')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {t('items.placeholders.imageFormats')}
                        </p>
                      </div>
                    )}
                    <Input
                      id="image"
                      name="image"
                      type="file"
                      accept="image/png,image/jpeg,image/webp"
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  </label>
                </div>
              </div>

              {/* Item Details */}
              <div className="space-y-2">
                <Label htmlFor="name">{t('items.details.name')} *</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder={t('items.placeholders.enterName')}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('items.details.description')}</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder={t('items.placeholders.enterDescription')}
                  className="resize-none"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">{t('items.details.price')} *</Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder={t('items.placeholders.enterPrice')}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">{t('items.details.category')} *</Label>
                  <Select name="category" required>
                    <SelectTrigger>
                      <SelectValue placeholder={t('items.placeholders.category')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="equipment">{t('items.categories.equipment')}</SelectItem>
                      <SelectItem value="clothing">{t('items.categories.clothing')}</SelectItem>
                      <SelectItem value="accessories">{t('items.categories.accessories')}</SelectItem>
                      <SelectItem value="other">{t('items.categories.other')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="stock">{t('items.details.stock')} *</Label>
                  <Input
                    id="stock"
                    name="stock"
                    type="number"
                    min="0"
                    placeholder={t('items.placeholders.enterStock')}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Link href="/items">
                <Button variant="outline" type="button">
                  {t('common.actions.cancel')}
                </Button>
              </Link>
              <Button type="submit" disabled={loading}>
                {loading ? t('items.actions.creating') : t('items.actions.createItem')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
