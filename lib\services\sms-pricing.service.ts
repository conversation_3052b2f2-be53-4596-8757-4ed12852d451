import { ServiceResult } from '@/lib/errors/types';
import { BaseService } from './base';
import { TenantAwareDB } from '@/lib/db';

export interface SmsPricingTier {
  minCredits: number;
  maxCredits: number | null;
  pricePerCredit: number; // in cents
  description: string;
}

export interface SmsPurchaseCalculation {
  credits: number;
  totalPrice: number; // in cents
  pricePerCredit: number; // in cents
  tier: SmsPricingTier;
  currency: string;
}

/**
 * SMS Pricing Service
 * Handles SMS credit pricing calculations and validation
 */
export class SmsPricingService extends BaseService {
  private static instance: SmsPricingService;

  constructor() {
    super('SmsPricingService');
  }

  public static getInstance(): SmsPricingService {
    if (!SmsPricingService.instance) {
      SmsPricingService.instance = new SmsPricingService();
    }
    return SmsPricingService.instance;
  }

  /**
   * Get pricing tiers from database
   */
  private async getPricingTiersFromDB(): Promise<SmsPricingTier[]> {
    // Initialize default tiers if none exist
    await TenantAwareDB.initializeDefaultPricingTiers();

    // Get active pricing tiers from database
    const dbTiers = await TenantAwareDB.getActivePricingTiers();

    return dbTiers.map(tier => ({
      minCredits: tier.minCredits,
      maxCredits: tier.maxCredits,
      pricePerCredit: tier.pricePerCredit,
      description: tier.description
    }));
  }

  /**
   * Calculate SMS purchase price with graduated pricing
   */
  async calculatePurchasePrice(credits: number): Promise<ServiceResult<SmsPurchaseCalculation>> {
    return this.executeOperation(
      'calculatePurchasePrice',
      async () => {
        // Validate input
        if (!credits || credits <= 0) {
          throw new Error('Credits must be a positive number');
        }

        if (credits > 10000) {
          throw new Error('Maximum purchase limit is 10,000 credits');
        }

        // Get pricing tiers from database
        const pricingTiers = await this.getPricingTiersFromDB();

        let totalPrice = 0;
        let remainingCredits = credits;
        let applicableTier: SmsPricingTier | null = null;

        // Calculate price using graduated tiers
        for (const tier of pricingTiers) {
          if (remainingCredits <= 0) break;

          const tierMax = tier.maxCredits || Infinity;
          const creditsInThisTier = Math.min(
            remainingCredits,
            tierMax - tier.minCredits + 1
          );

          if (creditsInThisTier > 0) {
            totalPrice += creditsInThisTier * tier.pricePerCredit;
            remainingCredits -= creditsInThisTier;

            // Set the highest tier used for display
            applicableTier = tier;
          }
        }

        if (!applicableTier) {
          throw new Error('Unable to calculate pricing for the requested credits');
        }

        return {
          credits,
          totalPrice,
          pricePerCredit: Math.round(totalPrice / credits), // Average price per credit
          tier: applicableTier,
          currency: 'USD'
        };
      },
      {
        resource: 'sms_pricing',
        metadata: { credits }
      }
    );
  }

  /**
   * Get pricing tiers for display
   */
  async getPricingTiers(): Promise<ServiceResult<SmsPricingTier[]>> {
    return this.executeOperation(
      'getPricingTiers',
      async () => {
        return await this.getPricingTiersFromDB();
      },
      {
        resource: 'sms_pricing'
      }
    );
  }

  /**
   * Validate purchase amount server-side
   */
  async validatePurchase(credits: number, expectedPrice: number): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'validatePurchase',
      async () => {
        const calculation = await this.calculatePurchasePrice(credits);

        if (!calculation.success || !calculation.data) {
          throw new Error('Failed to calculate purchase price');
        }

        // Allow small rounding differences (within 1 cent)
        const priceDifference = Math.abs(calculation.data.totalPrice - expectedPrice);
        if (priceDifference > 1) {
          throw new Error('Price validation failed - client and server prices do not match');
        }

        return true;
      },
      {
        resource: 'sms_pricing',
        metadata: { credits, expectedPrice }
      }
    );
  }

  /**
   * Format price for display
   */
  formatPrice(priceInCents: number, currency: string = 'USD'): string {
    const price = priceInCents / 100;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  }
}

// Export singleton instance
export const smsPricingService = () => SmsPricingService.getInstance();
