"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Menu, Users, LogOut } from "lucide-react";
import { ModeToggle } from "@/components/layout/mode-toggle";
import { LanguageSwitcher } from "@/components/layout/language-switcher";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { logoutFromZitadel } from "@/lib/zitadel-logout";

export function MainNav() {
  const pathname = usePathname();
  const router = useRouter();
  const { t } = useTranslation('shared');
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const routes = [
    {
      href: "/dashboard",
      label: isMounted ? t("nav.dashboard") : "nav.dashboard",
      active: pathname === "/dashboard",
    },
    {
      href: "/schools",
      label: isMounted ? t("nav.schools") : "nav.schools",
      active: pathname.startsWith("/schools"),
    },
    {
      href: "/instructors",
      label: isMounted ? t("nav.instructors") : "nav.instructors",
      active: pathname.startsWith("/instructors"),
    },
    {
      href: "/teams",
      label: isMounted ? t("nav.teams") : "nav.teams",
      active: pathname.startsWith("/teams"),
    },
    {
      href: "/athletes",
      label: isMounted ? t("nav.athletes") : "nav.athletes",
      active: pathname.startsWith("/athletes"),
    },
    {
      href: "/items",
      label: isMounted ? t("nav.items") : "nav.items",
      active: pathname.startsWith("/items"),
    },
    {
      href: "/payments",
      label: isMounted ? t("nav.payments") : "nav.payments",
      active: pathname.startsWith("/payments") || pathname.startsWith("/payment-plans"),
    },
    {
      href: "/expenses",
      label: isMounted ? t("nav.expenses") : "nav.expenses",
      active: pathname.startsWith("/expenses"),
    },
    {
      href: "/facilities",
      label: isMounted ? t("nav.facilities") : "nav.facilities",
      active: pathname.startsWith("/facilities"),
    },
  ];

  const handleSignOut = async () => {
    await logoutFromZitadel();
  };

  return (
    <div className="border-b">
      <div className="flex h-16 items-center px-4">
        <div className="flex items-center mr-4 md:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-5 w-5" />
                <span className="sr-only">{t('actions.toggleMenu')}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-52">
              {routes.map((route) => (
                <DropdownMenuItem key={route.href} asChild>
                  <Link
                    href={route.href}
                    className={cn(
                      "block w-full",
                      route.active && "font-bold"
                    )}
                  >
                    {route.label}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Link href="/dashboard" className="flex items-center">
          <div className="flex items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-orange-500 w-8 h-8 mr-2">
            <Users className="h-4 w-4 text-white" />
          </div>
          <span className="hidden md:inline-block font-bold text-xl">
            Sports Academy
          </span>
        </Link>

        <nav className="hidden md:flex items-center space-x-4 lg:space-x-6 ml-6">
          {routes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "text-sm font-medium transition-colors hover:text-primary",
                route.active
                  ? "text-foreground"
                  : "text-muted-foreground"
              )}
            >
              {route.label}
            </Link>
          ))}
        </nav>

        <div className="ml-auto flex items-center space-x-2">
          <LanguageSwitcher />
          <ModeToggle />
          <Button variant="ghost" size="icon" onClick={handleSignOut}>
            <LogOut className="h-5 w-5" />
            <span className="sr-only">{t('actions.signOut')}</span>
          </Button>
        </div>
      </div>
    </div>
  );
}

