"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, Mail, Phone, MapPin, Calendar, CreditCard, Users, Activity, ChevronDown, ChevronUp } from "lucide-react";
import Link from "next/link";
import { getAthleteById } from "@/lib/actions";
import { Athlete, AthleteTeamDetail } from "@/lib/types";
import { format } from "date-fns";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { BadgeStatus } from "@/components/ui/badge-status";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { TeamPaymentPlanAssignment } from "@/components/athletes/team-payment-plan-assignment";

import { PaymentPlanView } from "@/components/athletes/payment-plan-view";
import { AthleteTeamManagement } from "@/components/athletes/athlete-team-management";
import { getAthleteAssignedPlans } from "@/lib/actions/payment-plan-assignments";

export default function AthleteDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { t } = useSafeTranslation();
  const [athlete, setAthlete] = useState<Athlete | null>(null);
  const [athletePaymentPlans, setAthletePaymentPlans] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Collapsible states - all expanded by default
  const [isPersonalInfoOpen, setIsPersonalInfoOpen] = useState(true);
  const [isParentInfoOpen, setIsParentInfoOpen] = useState(true);
  const [isTeamsOpen, setIsTeamsOpen] = useState(true);
  const [isPaymentPlansOpen, setIsPaymentPlansOpen] = useState(true);
  const [isStatusBalanceOpen, setIsStatusBalanceOpen] = useState(true);
  const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const athleteId = params.id as string;
        
        const [athleteData, paymentPlansData] = await Promise.all([
          getAthleteById(athleteId),
          getAthleteAssignedPlans(athleteId)
        ]);

        if (!athleteData) {
          setError(t('athletes.errors.notFound'));
          return;
        }

        setAthlete(athleteData);
        setAthletePaymentPlans(paymentPlansData);
      } catch (error) {
        console.error("Error fetching athlete details:", error);
        setError(t('athletes.errors.loadFailed'));
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchData();
    }
  }, [params.id, t, refreshKey]);

  if (loading) {
    return <AthleteDetailSkeleton />;
  }

  if (error || !athlete) {
    return (
      <div className="container mx-auto py-6">
        <Button variant="ghost" className="mb-6" asChild>
          <Link href="/athletes">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Link>
        </Button>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-2">{t('common.error')}</h2>
              <p className="text-muted-foreground">{error || t('athletes.errors.notFound')}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Use the teamDetails from the athlete object instead of filtering teams
  const athleteTeams = athlete.teamDetails || [];

  const handleTeamChange = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <Button variant="ghost" asChild>
          <Link href="/athletes">
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.actions.back')}
          </Link>
        </Button>
        <Button asChild>
          <Link href={`/athletes/${athlete.id}/edit`}>
            <Edit className="mr-2 h-4 w-4" />
            {t('athletes.actions.editAthlete')}
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Collapsible open={isPersonalInfoOpen} onOpenChange={setIsPersonalInfoOpen}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      {t('athletes.details.personalInfo')}
                    </CardTitle>
                    {isPersonalInfoOpen ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.firstName')}
                      </label>
                      <p className="text-lg font-medium">{athlete.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.lastName')}
                      </label>
                      <p className="text-lg font-medium">{athlete.surname}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.nationalId')}
                      </label>
                      <p className="font-mono">{athlete.nationalId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.birthDate')}
                      </label>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {format(new Date(athlete.birthDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.registrationDate')}
                      </label>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {format(new Date(athlete.registrationDate), "dd/MM/yyyy")}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>

          {/* Parent Information */}
          <Collapsible open={isParentInfoOpen} onOpenChange={setIsParentInfoOpen}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      {t('athletes.details.parent')}
                    </CardTitle>
                    {isParentInfoOpen ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.parentName')}
                      </label>
                      <p className="text-lg font-medium">
                        {athlete.parentName} {athlete.parentSurname}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.parentPhone')}
                      </label>
                      <p className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {athlete.parentPhone}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.parentEmail')}
                      </label>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {athlete.parentEmail}
                      </p>
                    </div>
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-muted-foreground">
                        {t('athletes.details.parentAddress')}
                      </label>
                      <p className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {athlete.parentAddress}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>

          {/* Teams */}
          <AthleteTeamManagement
            athleteId={athlete.id}
            athleteName={athlete.name}
            athleteSurname={athlete.surname}
            athleteTeams={athleteTeams}
            onTeamChange={handleTeamChange}
          />

          {/* Payment Plan Assignments */}
          <Collapsible open={isPaymentPlansOpen} onOpenChange={setIsPaymentPlansOpen}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      {t('payments.plans.assignments.title')}
                    </CardTitle>
                    {isPaymentPlansOpen ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent>
                  <PaymentPlanView athletePaymentPlans={athletePaymentPlans} />
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status and Balance */}
          <Collapsible open={isStatusBalanceOpen} onOpenChange={setIsStatusBalanceOpen}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      {t('athletes.details.statusAndBalance')}
                    </CardTitle>
                    {isStatusBalanceOpen ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('athletes.table.status')}
                    </label>
                    <div className="mt-1">
                      <BadgeStatus status={athlete.status} />
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t('athletes.table.balance')}
                    </label>
                    <div className="mt-1">
                      <div className={`text-2xl font-bold ${
                        parseFloat(athlete.balance) < 0 ? "text-red-500" : "text-green-500"
                      }`}>
                        {athlete.balance} {t('common.currency')}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>

          {/* Quick Actions */}
          <Collapsible open={isQuickActionsOpen} onOpenChange={setIsQuickActionsOpen}>
            <Card>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-muted/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Edit className="h-5 w-5" />
                      {t('common.actions.view')}
                    </CardTitle>
                    {isQuickActionsOpen ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href={`/payments?athleteId=${athlete.id}`}>
                      <CreditCard className="mr-2 h-4 w-4" />
                      {t('payments.title')}
                    </Link>
                  </Button>
                  <Button variant="outline" className="w-full justify-start" asChild>
                    <Link href={`/athletes/${athlete.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      {t('athletes.actions.editAthlete')}
                    </Link>
                  </Button>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>
        </div>
      </div>
    </div>
  );
}

function AthleteDetailSkeleton() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-32" />
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-full" />
                  </div>
                ))}
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-8 w-24" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
