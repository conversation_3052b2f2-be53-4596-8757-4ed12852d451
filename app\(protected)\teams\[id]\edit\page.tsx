import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { getTeamById, getSchools, getInstructors, getFacilities } from '@/lib/db/actions';
import TeamEditClient from '@/components/teams/edit/TeamEditClient';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface TeamEditPageProps {
  params: Promise<{ id: string }>;
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <Button variant="ghost" size="icon" asChild>
        <Link href="/teams">
          <ArrowLeft className="h-4 w-4" />
        </Link>
      </Button>
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="h-10 bg-muted animate-pulse rounded-md" />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default async function TeamEditPage({ params }: TeamEditPageProps) {
  const { id } = await params;

  try {
    const [team, schools, instructors, facilities] = await Promise.all([
      getTeamById(id),
      getSchools(),
      getInstructors(),
      getFacilities()
    ]);

    if (!team) {
      notFound();
    }

    return (
      <Suspense fallback={<LoadingSkeleton />}>
        <TeamEditClient 
          team={team}
          schools={schools}
          instructors={instructors}
          facilities={facilities}
        />
      </Suspense>
    );
  } catch (error) {
    console.error('Error loading team edit data:', error);
    notFound();
  }
}
