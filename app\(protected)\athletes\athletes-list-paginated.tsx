"use client";

import { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PlusCircle, Search, Filter, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { Athlete } from "@/lib/types";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { DataTable } from "@/components/ui/data-table";
import { useColumns } from "@/components/athletes-table-columns";
import { useRouter, useSearchParams } from "next/navigation";
import { AthleteImportDialog } from "@/components/athletes/import/AthleteImportDialog";

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface AthletesListPaginatedProps {
  initialData: Athlete[];
  initialPagination: PaginationData;
  initialSearchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    name?: string;
    surname?: string;
    parentEmail?: string;
    parentPhone?: string;
    nationalId?: string;
    status?: 'active' | 'inactive' | 'suspended';
  };
}

export function AthletesListPaginated({ 
  initialData, 
  initialPagination, 
  initialSearchParams 
}: AthletesListPaginatedProps) {
  const { t, isClient } = useSafeTranslation();
  const columns = useColumns();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [athletes, setAthletes] = useState<Athlete[]>(initialData);
  const [loading] = useState(false);

  // Local state for UI interactions - initialize once from URL, never sync
  const [localSearch, setLocalSearch] = useState(() => initialSearchParams.search || "");
  const [filters, setFilters] = useState(() => ({
    name: initialSearchParams.name || "",
    surname: initialSearchParams.surname || "",
    parentEmail: initialSearchParams.parentEmail || "",
    parentPhone: initialSearchParams.parentPhone || "",
    nationalId: initialSearchParams.nationalId || "",
    status: initialSearchParams.status || "",
  }));
  const [showFilters, setShowFilters] = useState(() => 
    !!(initialSearchParams.name || initialSearchParams.surname || initialSearchParams.parentEmail || initialSearchParams.parentPhone || initialSearchParams.nationalId || initialSearchParams.status)
  );

  // Track the previous search value to only trigger on actual changes
  const [prevLocalSearch, setPrevLocalSearch] = useState(localSearch);

  // Update athletes when initialData changes (when server component re-renders)
  useEffect(() => {
    setAthletes(initialData);
  }, [initialData]);

  // Read URL params for server-side values (read-only) - get from current URL not just initial
  const currentSearchParams = searchParams;
  const search = currentSearchParams.get('search') || "";
  const sortBy = currentSearchParams.get('sortBy') || "createdAt";
  const sortOrder = (currentSearchParams.get('sortOrder') as 'asc' | 'desc') || "desc";
  const currentLimit = currentSearchParams.get('limit') || "10";

  // Calculate if search is valid (empty or has at least 3 characters)
  const isSearchValid = useMemo(() => {
    return localSearch.trim().length === 0 || localSearch.trim().length >= 3;
  }, [localSearch]);

  // Function to update URL with new search params
  const updateURL = useCallback((newParams: Record<string, string | undefined>) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Remove undefined values and update params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value === undefined || value === '') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    // Only reset to page 1 when filters/search/sort/limit change, but NOT when page is explicitly being changed
    const shouldResetPage = !('page' in newParams);
    if (shouldResetPage) {
      params.set('page', '1');
    }

    const finalURL = `/athletes?${params.toString()}`;
    router.push(finalURL);
  }, [router, searchParams]);
  
  useEffect(() => {
    // Only trigger if localSearch actually changed (not just component re-render)
    if (localSearch !== prevLocalSearch) {
      setPrevLocalSearch(localSearch);
      
      const shouldTriggerSearch = localSearch.trim().length === 0 || localSearch.trim().length >= 3;
      
      if (shouldTriggerSearch) {
        const timeoutId = setTimeout(() => {
          updateURL({ search: localSearch.trim() || undefined });
        }, 300); // 300ms debounce
        
        return () => clearTimeout(timeoutId);
      }
    }
  }, [localSearch, prevLocalSearch, updateURL]);

  // Don't render until client-side hydration is complete
  if (!isClient) {
    return (
      <div className="container mx-auto py-10">
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
          ))}
        </div>
      </div>
    );
  }

  const handlePageChange = (newPage: number) => {
    updateURL({ page: newPage.toString() });
  };

  const handleLimitChange = (newLimit: string) => {
    updateURL({ limit: newLimit });
  };

  const handleSearch = () => {
    // Manual search - trigger immediate search
    updateURL({ search: localSearch.trim() || undefined });
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const cleanFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value.trim() !== "")
    );
    updateURL({
      name: cleanFilters.name || undefined,
      surname: cleanFilters.surname || undefined,
      parentEmail: cleanFilters.parentEmail || undefined,
      parentPhone: cleanFilters.parentPhone || undefined,
      nationalId: cleanFilters.nationalId || undefined,
      status: cleanFilters.status || undefined,
    });
  };

  const handleClearFilters = () => {
    setFilters({
      name: "",
      surname: "",
      parentEmail: "",
      parentPhone: "",
      nationalId: "",
      status: "",
    });
    updateURL({
      search: undefined,
      name: undefined,
      surname: undefined,
      parentEmail: undefined,
      parentPhone: undefined,
      nationalId: undefined,
      status: undefined,
    });
  };

  const handleSortChange = (field: string) => {
    const newSortOrder = sortBy === field ? (sortOrder === 'asc' ? 'desc' : 'asc') : 'asc';
    updateURL({ 
      sortBy: field, 
      sortOrder: newSortOrder 
    });
  };

  const handleSortOrderChange = (newSortOrder: 'asc' | 'desc') => {
    updateURL({ sortOrder: newSortOrder });
  };

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-4xl font-bold tracking-tight">{t('athletes.title')}</h1>
          <p className="text-muted-foreground">{t('athletes.messages.manageAthletes')}</p>
        </div>
        <div className="flex gap-2">
          <AthleteImportDialog onImportComplete={() => window.location.reload()} />
          <Link href="/athletes/new">
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              {t('athletes.actions.addNew')}
            </Button>
          </Link>
        </div>
      </div>

      <Card>
        <br></br>
        <CardContent>
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            {/* Search Bar */}
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Input
                  placeholder={t('athletes.placeholders.searchAthletes')}
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className={!isSearchValid ? "border-yellow-500 focus:border-yellow-500" : ""}
                />
                {localSearch.trim().length > 0 && localSearch.trim().length < 3 && (
                  <div className="absolute top-full left-0 mt-1 text-xs text-yellow-600 dark:text-yellow-400">
                    {t('athletes.placeholders.searchMinChars')}
                  </div>
                )}
              </div>
              <Button 
                onClick={handleSearch} 
                variant="outline"
                disabled={!isSearchValid}
              >
                <Search className="h-4 w-4 mr-2" />
                {t('common.actions.search')}
              </Button>
              <Button 
                onClick={() => setShowFilters(!showFilters)} 
                variant="outline"
              >
                <Filter className="h-4 w-4 mr-2" />
                {t('common.actions.filter')}
              </Button>
            </div>

            {/* Column Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg bg-muted/50">
                <div className="space-y-2">
                  <Label htmlFor="nameFilter">{t('athletes.details.name')}</Label>
                  <Input
                    id="nameFilter"
                    placeholder={t('common.actions.filterBy', { field: t('athletes.details.name') })}
                    value={filters.name}
                    onChange={(e) => handleFilterChange('name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="surnameFilter">{t('athletes.details.surname')}</Label>
                  <Input
                    id="surnameFilter"
                    placeholder={t('common.actions.filterBy', { field: t('athletes.details.surname') })}
                    value={filters.surname}
                    onChange={(e) => handleFilterChange('surname', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="parentEmailFilter">{t('athletes.details.parentEmail')}</Label>
                  <Input
                    id="parentEmailFilter"
                    placeholder={t('common.actions.filterBy', { field: t('athletes.details.parentEmail') })}
                    value={filters.parentEmail}
                    onChange={(e) => handleFilterChange('parentEmail', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="parentPhoneFilter">{t('athletes.details.parentPhone')}</Label>
                  <Input
                    id="parentPhoneFilter"
                    placeholder={t('common.actions.filterBy', { field: t('athletes.details.parentPhone') })}
                    value={filters.parentPhone}
                    onChange={(e) => handleFilterChange('parentPhone', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="nationalIdFilter">{t('athletes.details.nationalId')}</Label>
                  <Input
                    id="nationalIdFilter"
                    placeholder={t('common.actions.filterBy', { field: t('athletes.details.nationalId') })}
                    value={filters.nationalId}
                    onChange={(e) => handleFilterChange('nationalId', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="statusFilter">{t('athletes.details.status')}</Label>
                  <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('common.actions.filterBy', { field: t('athletes.details.status') })} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">{t('athletes.status.active')}</SelectItem>
                      <SelectItem value="inactive">{t('athletes.status.inactive')}</SelectItem>
                      <SelectItem value="suspended">{t('athletes.status.suspended')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2 md:col-span-2 lg:col-span-4">
                  <Button onClick={handleApplyFilters} size="sm">
                    {t('common.actions.apply')}
                  </Button>
                  <Button onClick={handleClearFilters} variant="outline" size="sm">
                    {t('common.actions.clear')}
                  </Button>
                </div>
              </div>
            )}

            {/* Sort and Page Size Controls */}
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center space-x-2">
                <Label>{t('common.sortBy')}:</Label>
                <Select 
                  value={sortBy} 
                  onValueChange={(field) => handleSortChange(field)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">{t('athletes.details.name')}</SelectItem>
                    <SelectItem value="surname">{t('athletes.details.surname')}</SelectItem>
                    <SelectItem value="parentEmail">{t('athletes.details.parentEmail')}</SelectItem>
                    <SelectItem value="status">{t('athletes.details.status')}</SelectItem>
                    <SelectItem value="balance">{t('athletes.details.balance')}</SelectItem>
                    <SelectItem value="birthDate">{t('athletes.details.birthDate')}</SelectItem>
                    <SelectItem value="createdAt">{t('common.createdAt')}</SelectItem>
                  </SelectContent>
                </Select>
                <Select 
                  value={sortOrder} 
                  onValueChange={(value) => handleSortOrderChange(value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">{t('common.ascending')}</SelectItem>
                    <SelectItem value="desc">{t('common.descending')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Label>{t('common.pageSize')}:</Label>
                <Select value={currentLimit} onValueChange={handleLimitChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Data Table */}
          {loading ? (
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
              ))}
            </div>
          ) : (
            <>
              <DataTable 
                columns={columns} 
                data={athletes}
                showPagination={false}
              />

              {/* Pagination Controls */}
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-muted-foreground">
                  {t('common.pagination.showing', {
                    start: (initialPagination.page - 1) * initialPagination.limit + 1,
                    end: Math.min(initialPagination.page * initialPagination.limit, initialPagination.total),
                    total: initialPagination.total
                  })}
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(initialPagination.page - 1)}
                    disabled={!initialPagination.hasPreviousPage}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    {t('common.pagination.previous')}
                  </Button>
                  
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, initialPagination.totalPages) }, (_, i) => {
                      let pageNum: number;
                      if (initialPagination.totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (initialPagination.page <= 3) {
                        pageNum = i + 1;
                      } else if (initialPagination.page >= initialPagination.totalPages - 2) {
                        pageNum = initialPagination.totalPages - 4 + i;
                      } else {
                        pageNum = initialPagination.page - 2 + i;
                      }
                      
                      return (
                        <Button
                          key={pageNum}
                          variant={initialPagination.page === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => handlePageChange(pageNum)}
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(initialPagination.page + 1)}
                    disabled={!initialPagination.hasNextPage}
                  >
                    {t('common.pagination.next')}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
