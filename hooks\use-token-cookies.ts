"use client";

import { useSession, signOut } from 'next-auth/react';
import { useEffect, useRef } from 'react';
import axios from 'axios';

export function useTokenCookies() {
  const { data: session, status } = useSession();
  const lastTokenUpdate = useRef<string>('');
  const isUpdating = useRef<boolean>(false);

  useEffect(() => {
    if (status === 'authenticated' && session) {
      // Check if there's a refresh error
      if (session.error === 'RefreshAccessTokenError') {
        console.warn('Token refresh failed, signing out user');
        signOut({ callbackUrl: '/auth/signin' });
        return;
      }

      if (session.accessToken) {
        // Create a unique identifier for this token set
        const currentTokenId = session.accessToken.substring(0, 20);
        
        // Only update cookies if tokens have changed and not already updating
        if (currentTokenId !== lastTokenUpdate.current && !isUpdating.current) {
          isUpdating.current = true;
          
          const updateTokenCookies = async () => {
            try {
              await axios.get('/api/auth/set-tokens', {
                withCredentials: true,
                timeout: 5000, // 5 second timeout
              });
              
              lastTokenUpdate.current = currentTokenId;
              console.log('Token cookies updated successfully');
            } catch (error) {
              console.error('Error updating token cookies:', error);
              
              // Handle specific error cases
              if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                if (status === 401) {
                  console.warn('Unauthorized while setting tokens, signing out');
                  signOut({ callbackUrl: '/auth/signin' });
                } else if (error.code === 'ECONNABORTED') {
                  console.warn('Token update request timed out');
                } else if (status && status >= 500) {
                  console.warn('Server error while setting tokens, will retry on next session change');
                }
              }
            } finally {
              isUpdating.current = false;
            }
          };

          updateTokenCookies();
        }
      }
    }
  }, [session?.accessToken, session?.error, status]);

  return { 
    session, 
    status,
    isTokenUpdateInProgress: isUpdating.current 
  };
}
