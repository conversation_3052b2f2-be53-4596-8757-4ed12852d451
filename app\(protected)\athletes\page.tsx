import React, { Suspense } from "react";
import { getAthletesPaginated } from "@/lib/actions/athletes";
import { AthletesListPaginated } from "./athletes-list-paginated";

interface AthletesPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    name?: string;
    surname?: string;
    parentEmail?: string;
    parentPhone?: string;
    nationalId?: string;
    status?: 'active' | 'inactive' | 'suspended';
  }>;
}

async function AthletesListServer({ searchParams }: AthletesPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = parseInt(resolvedSearchParams.limit || '10');
  const search = resolvedSearchParams.search;
  const sortBy = resolvedSearchParams.sortBy || 'createdAt';
  const sortOrder = resolvedSearchParams.sortOrder || 'desc';
  
  const filters: Record<string, string> = {};
  if (resolvedSearchParams.name) filters.name = resolvedSearchParams.name;
  if (resolvedSearchParams.surname) filters.surname = resolvedSearchParams.surname;
  if (resolvedSearchParams.parentEmail) filters.parentEmail = resolvedSearchParams.parentEmail;
  if (resolvedSearchParams.parentPhone) filters.parentPhone = resolvedSearchParams.parentPhone;
  if (resolvedSearchParams.nationalId) filters.nationalId = resolvedSearchParams.nationalId;
  if (resolvedSearchParams.status) filters.status = resolvedSearchParams.status;

  const result = await getAthletesPaginated(
    page,
    limit,
    search,
    sortBy,
    sortOrder,
    filters
  );

  return (
    <AthletesListPaginated 
      initialData={result.data || []}
      initialPagination={result.pagination || {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPreviousPage: false,
      }}
      initialSearchParams={resolvedSearchParams}
    />
  );
}

function AthletesListSkeleton() {
  return (
    <div className="space-y-4">
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="h-16 bg-muted animate-pulse rounded-md" />
      ))}
    </div>
  );
}

export default function AthletesPage({ searchParams }: AthletesPageProps) {
  return (
    <Suspense fallback={<AthletesListSkeleton />}>
      <AthletesListServer searchParams={searchParams} />
    </Suspense>
  );
}