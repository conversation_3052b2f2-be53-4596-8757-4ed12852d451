"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { downloadAthleteTemplate, bulkImportAthletes } from "@/lib/db/actions/athletes";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { BulkImportResult } from "@/lib/services/athlete.service";

interface AthleteImportDialogProps {
  onImportComplete?: () => void;
}

export function AthleteImportDialog({ onImportComplete }: AthleteImportDialogProps) {
  const { t, i18n } = useSafeTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<BulkImportResult | null>(null);
  const [showErrors, setShowErrors] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDownloadTemplate = async () => {
    try {
      // Get current locale from i18n hook
      const locale = i18n.language || 'en';

      const blob = await downloadAthleteTemplate(locale);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'athlete-import-template.xlsx';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success(t('athletes.import.messages.templateDownloaded'));
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error(t('athletes.import.error'));
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file extension and MIME type - only allow .xlsx files
      const fileName = file.name.toLowerCase();
      const isXlsx = fileName.endsWith('.xlsx') &&
                    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

      if (isXlsx) {
        setSelectedFile(file);
        setImportResult(null);
      } else {
        toast.error(t('athletes.import.messages.invalidFileType'));
        event.target.value = '';
      }
    }
  };

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error(t('athletes.import.messages.noFileSelected'));
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setImportResult(null);

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const formData = new FormData();
      formData.append('file', selectedFile);

      // Get current locale
      const locale = i18n.language || 'en';

      const result = await bulkImportAthletes(formData, locale);

      clearInterval(progressInterval);
      setProgress(100);

      if (result) {
        setImportResult(result);

        if (result.success && result.created > 0) {
          toast.success(t('athletes.import.success'));
          onImportComplete?.();
        } else if (result.errors.length > 0) {
          toast.error(t('athletes.import.error'));
        }
      } else {
        // Handle case where result is undefined
        setImportResult({
          success: false,
          processed: 0,
          created: 0,
          errors: [{ row: 0, message: 'Import failed - no result returned' }]
        });
        toast.error(t('athletes.import.error'));
      }
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        processed: 0,
        created: 0,
        errors: [{ row: 0, message: t('athletes.import.error') }]
      });
      toast.error(t('athletes.import.error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const resetDialog = () => {
    setSelectedFile(null);
    setImportResult(null);
    setProgress(0);
    setShowErrors(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      resetDialog();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline">
          <FileSpreadsheet className="mr-2 h-4 w-4" />
          {t('athletes.actions.importFromExcel')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('athletes.import.title')}</DialogTitle>
          <DialogDescription>
            {t('athletes.import.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download Section */}
          <div className="space-y-2">
            <Label>{t('athletes.import.downloadTemplate')}</Label>
            <Button 
              variant="outline" 
              onClick={handleDownloadTemplate}
              className="w-full"
            >
              <Download className="mr-2 h-4 w-4" />
              {t('athletes.import.downloadTemplate')}
            </Button>
          </div>

          {/* File Upload Section */}
          <div className="space-y-2">
            <Label htmlFor="excel-file">{t('athletes.import.uploadFile')}</Label>
            <Input
              id="excel-file"
              type="file"
              accept=".xlsx"
              onChange={handleFileSelect}
              ref={fileInputRef}
              disabled={isProcessing}
            />
            {selectedFile && (
              <p className="text-sm text-muted-foreground">
                Selected: {selectedFile.name}
              </p>
            )}
          </div>

          {/* Progress Section */}
          {isProcessing && (
            <div className="space-y-2">
              <Label>{t('athletes.import.processing')}</Label>
              <Progress value={progress} className="w-full" />
            </div>
          )}

          {/* Results Section */}
          {importResult && (
            <div className="space-y-4">
              <Alert className={importResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                <div className="flex items-center">
                  {importResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription className="ml-2">
                    <div className="space-y-1">
                      <p>{t('athletes.import.results.processed', { count: importResult.processed })}</p>
                      <p>{t('athletes.import.results.created', { count: importResult.created })}</p>
                      {importResult.errors.length > 0 && (
                        <p className="text-red-600">
                          {t('athletes.import.results.errors', { count: importResult.errors.length })}
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </div>
              </Alert>

              {importResult.errors.length > 0 && (
                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowErrors(!showErrors)}
                  >
                    {t('athletes.import.results.viewErrors')}
                  </Button>
                  
                  {showErrors && (
                    <div className="mt-2 max-h-40 overflow-y-auto border rounded p-2 bg-red-50">
                      {importResult.errors.map((error, index) => (
                        <div key={index} className="text-sm text-red-600 mb-1">
                          Row {error.row}: {error.message}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              {t('common.actions.cancel')}
            </Button>
            <Button 
              onClick={handleImport}
              disabled={!selectedFile || isProcessing}
            >
              <Upload className="mr-2 h-4 w-4" />
              {isProcessing ? t('athletes.import.processing') : t('athletes.import.uploadFile')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
